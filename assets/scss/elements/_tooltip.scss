.openuptip{
    &[title] {
        position: relative; /* opinion 1 */
        cursor: pointer;
    }
    &[title]::before,
    &[title]::after {
      text-transform: none; /* opinion 2 */
      font-size: .9em; /* opinion 3 */
      line-height: 1;
      user-select: none;
      pointer-events: none;
      position: absolute;
      display: none;
      opacity: 0;
    }
    &[title]::before {
        content: '';
        border: 5px solid transparent; /* opinion 4 */
        z-index: 1001; /* absurdity 1 */
    }
    &[title]::after {
        content: attr(title); /* magic! */
        
        /* most of the rest of this is opinion */
        font-family: Helvetica, sans-serif;
        text-align: center;
        
        /* 
        Let the content set the size of the titles 
        but this will also keep them from being obnoxious
        */
        min-width: 3em;
        max-width: 21em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 1ch 1.5ch;
        border-radius: .3ch;
        box-shadow: 0 1em 2em -.5em rgba(255, 255, 255, 0.35);
        background: var(--color-primary);
        color: #fff;
        z-index: 1000; /* absurdity 2 */
      }
    &[title]:hover::before,
    &[title]:hover::after {
      display: block;
    }
    &[title='']::before,
    &[title='']::after {
      display: none !important;
    }

    &[title]:not([flow])::before,
    &[title][flow^="up"]::before {
      bottom: 100%;
      border-bottom-width: 0;
      border-top-color: var(--color-primary);
    }
    &[title]:not([flow])::after,
    &[title][flow^="up"]::after {
      bottom: calc(100% + 5px);
    }
    &[title]:not([flow])::before,
    &[title]:not([flow])::after,
    &[title][flow^="up"]::before,
    &[title][flow^="up"]::after {
      left: 50%;
      transform: translate(-50%, -.5em);
    }

    @keyframes titles-vert {
        to {
          opacity: 1;
          transform: translate(-50%, 0);
        }
      }
      
      @keyframes titles-horz {
        to {
          opacity: 1;
          transform: translate(0, -50%);
        }
      }

    &[title]:not([flow]):hover::before,
    &[title]:not([flow]):hover::after,
    &[title][flow^="up"]:hover::before,
    &[title][flow^="up"]:hover::after,
    &[title][flow^="down"]:hover::before,
    &[title][flow^="down"]:hover::after {
    animation: titles-vert 300ms ease-out forwards;
    }
    
    &[title][flow^="left"]:hover::before,
    &[title][flow^="left"]:hover::after,
    &[title][flow^="right"]:hover::before,
    &[title][flow^="right"]:hover::after {
    animation: titles-horz 300ms ease-out forwards;
    }

    &[title]:not([flow])::before,
    &[title][flow^="up"]::before {
      bottom: 100%;
      border-bottom-width: 0;
      border-top-color: var(--color-primary);
    }
    &[title]:not([flow])::after,
    &[title][flow^="up"]::after {
      bottom: calc(100% + 5px);
    }
    &[title]:not([flow])::before,
    &[title]:not([flow])::after,
    &[title][flow^="up"]::before,
    &[title][flow^="up"]::after {
      left: 50%;
      transform: translate(-50%, -.5em);
    }
    
    /* FLOW: DOWN */
    &[title][flow^="down"]::before {
      top: 100%;
      border-top-width: 0;
      border-bottom-color: var(--color-primary);
    }
    &[title][flow^="down"]::after {
      top: calc(100% + 5px);
    }
    &[title][flow^="down"]::before,
    &[title][flow^="down"]::after {
      left: 50%;
      transform: translate(-50%, .5em);
    }
    
    /* FLOW: LEFT */
    &[title][flow^="left"]::before {
      top: 50%;
      border-right-width: 0;
      border-left-color: var(--color-primary);
      left: calc(0em - 5px);
      transform: translate(-.5em, -50%);
    }
    &[title][flow^="left"]::after {
      top: 50%;
      right: calc(100% + 5px);
      transform: translate(-.5em, -50%);
    }
    
    /* FLOW: RIGHT */
    &[title][flow^="right"]::before {
      top: 50%;
      border-left-width: 0;
      border-right-color: var(--color-primary);
      right: calc(0em - 5px);
      transform: translate(.5em, -50%);
    }
    &[title][flow^="right"]::after {
      top: 50%;
      left: calc(100% + 5px);
      transform: translate(.5em, -50%);
    }
    
}


body{
    &.modal-open{
        overflow: hidden;
        padding-right: 0 !important;
    }
}