// back to top style scss
.progress-wrap {
	position: fixed;
	right: 30px;
	bottom: 30px;
	height: 46px;
	width: 46px;
	cursor: pointer;
	display: block;
	border-radius: 50px;
	z-index: 10000;
	opacity: 1;
	visibility: hidden;
	transform: translateY(15px);
	-webkit-transition: all 200ms linear;
    transition: all 200ms linear;

}
.progress-wrap.active-progress {
	opacity: 1;
	visibility: visible;
	transform: translateY(0);
	box-shadow: #629d2310 0px 0px 6px 7px;
}
.progress-wrap::after {
	position: absolute;
	font-family: var(--font-three);
	content: '\f077';
	text-align: center;
	line-height: 46px;
	font-size: 16px;
	color: var(--color-primary);
	left: 0;
	top: 0;
	height: 46px;
	width: 46px;
	cursor: pointer;
	display: block;
	z-index: 1;
	-webkit-transition: all 200ms linear;
    transition: all 200ms linear;
    border: 0px solid var(--color-primary);
    box-shadow: none;
	border-radius: 50% !important;
	border-radius: 5px;
    font-weight: 300;
}
.progress-wrap:hover::after {
	opacity: 1;
    content: '\f077';
    border: 0px solid var(--color-primary);
    font-weight: 300;
}
.progress-wrap::before {
	position: absolute;
	font-family: var(--font-three);
	content: '\f077';
	text-align: center;
	line-height: 46px;
	font-size: 16px;
	opacity: 0;
	background: var(--color-primary);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	left: 0;
	top: 0;
	height: 46px;
	width: 46px;
	cursor: pointer;
	display: block;
	z-index: 2;
	-webkit-transition: all 200ms linear;
    transition: all 200ms linear;
    font-weight: 300;
}
.progress-wrap:hover::before {
	opacity: 0;
}
.progress-wrap svg path { 
	fill: none; 
}
.progress-wrap svg { 
    color: var(--color-primary);
	border-radius: 50%;
	background: #fff;
}
.progress-wrap svg.progress-circle path {
    stroke: var(--color-primary);
    stroke-width: 0px;
	box-sizing:border-box;
	-webkit-transition: all 200ms linear;
    transition: all 200ms linear;
    padding: 2px;
}



.home-blue{
	.progress-wrap svg.progress-circle path{
		stroke: var(--color-primary-2);
	}
	.progress-wrap::after{
		border-color: var(--color-primary-2);
		box-shadow: 0px 3px 20px 6px #0742e952;
		color: var(--color-primary-2);
	}
}

