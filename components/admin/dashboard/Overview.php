<?php

if (!function_exists('render_admin_overview_section')) {
    function render_admin_overview_section(array $cards): void
    {
        ?>
        <div class="title-right-actioin-btn-wrapper-product-list">
            <h3 class="title">Overview</h3>
            <div class="button-wrapper">
                <div class="single-select">
                    <select style="display: none;">
                        <option data-display="Export">Export</option>
                        <option value="1">Some option</option>
                        <option value="2">Another option</option>
                        <option value="3" disabled="">A disabled option</option>
                        <option value="4">Potato</option>
                    </select>
                    <div class="nice-select" tabindex="0"><span class="current">30 Days</span>
                        <ul class="list">
                            <li data-value="Default Sorting" data-display="30 Days" class="option selected">30 Days</li>
                            <li data-value="1" class="option">60 Dayes</li>
                            <li data-value="2" class="option">10 Week</li>
                            <li data-value="4" class="option">6 Month</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="row g-5">
            <?php foreach ($cards as $card): ?>
                <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                    <div class="single-over-fiew-card">
                        <span class="top-main"><?= htmlspecialchars($card['label'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span>
                        <div class="bottom">
                            <h2 class="title"><?= htmlspecialchars($card['value'] ?? '', ENT_QUOTES, 'UTF-8'); ?></h2>
                            <div class="right-primary">
                                <div class="increase">
                                    <?php if (!empty($card['trend_icon'])): ?>
                                        <i class="<?= htmlspecialchars($card['trend_icon'], ENT_QUOTES, 'UTF-8'); ?>"></i>
                                    <?php endif; ?>
                                    <span><?= htmlspecialchars($card['change'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span>
                                </div>
                                <?php if (!empty($card['image'])): ?>
                                    <img src="<?= htmlspecialchars($card['image'], ENT_QUOTES, 'UTF-8'); ?>" alt="rcf">
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
    }
}
