<?php

if (!function_exists('get_admin_dashboard_data')) {
    function get_admin_dashboard_data(): array
    {
        return [
            'sidebar' => [
                'logo' => [
                    'href' => 'index.php',
                    'src' => 'assets/images/logo/logo.svg',
                    'alt' => 'logo',
                    'width' => 131,
                    'height' => 32,
                ],
                'menu' => [
                    [
                        'label' => 'Dashboard',
                        'icon' => 'assets/images/icons/01.svg',
                        'href' => '#',
                        'submenu_class' => 'submenu mm-collapse parent-nav',
                        'submenu' => [
                            ['label' => 'Mian Demo', 'href' => 'index.php'],
                            ['label' => 'Coming Soon', 'href' => '#'],
                        ],
                    ],
                    [
                        'label' => 'Order',
                        'icon' => 'assets/images/icons/09.svg',
                        'href' => '#',
                        'submenu_class' => 'submenu mm-collapse parent-nav',
                        'submenu' => [
                            ['label' => 'Order', 'href' => 'order.php'],
                            ['label' => 'Order Details', 'href' => 'order-details.php'],
                        ],
                    ],
                    [
                        'label' => 'Product',
                        'icon' => 'assets/images/icons/02.svg',
                        'href' => '#',
                        'submenu_class' => 'submenu mm-collapse parent-nav',
                        'submenu' => [
                            ['label' => 'product List', 'href' => 'product-list.php'],
                        ],
                    ],
                    [
                        'label' => 'Add Product',
                        'icon' => 'assets/images/icons/03.svg',
                        'href' => '#',
                        'submenu_class' => 'submenu mm-collapse',
                        'submenu' => [
                            ['label' => 'Add Product', 'href' => 'add-product.php'],
                        ],
                    ],
                    [
                        'label' => 'Vendor',
                        'icon' => 'assets/images/icons/04.svg',
                        'href' => '#',
                        'submenu_class' => 'submenu mm-collapse parent-nav',
                        'submenu' => [
                            ['label' => 'Vendor Grid', 'href' => 'vendor-grid.php'],
                            ['label' => 'Vendor List', 'href' => 'vendor-list.php'],
                            ['label' => 'Vendor Details', 'href' => 'vendor-details.php'],
                        ],
                    ],
                    [
                        'label' => 'Transactions',
                        'icon' => 'assets/images/icons/06.svg',
                        'href' => 'transaction.php',
                        'submenu_class' => null,
                        'submenu' => [],
                    ],
                    [
                        'label' => 'Reviews',
                        'icon' => 'assets/images/icons/07.svg',
                        'href' => 'review.php',
                        'submenu_class' => null,
                        'submenu' => [],
                    ],
                    [
                        'label' => 'Brand',
                        'icon' => 'assets/images/icons/16.svg',
                        'href' => 'brand.php',
                        'submenu_class' => null,
                        'submenu' => [],
                    ],
                    [
                        'label' => 'Categories',
                        'icon' => 'assets/images/icons/08.svg',
                        'href' => 'categories.php',
                        'submenu_class' => 'submenu mm-collapse parent-nav',
                        'submenu' => [
                            ['label' => 'Category List', 'href' => 'categories.php'],
                            ['label' => 'Add Category', 'href' => 'add-category.php'],
                        ],
                    ],
                    [
                        'label' => 'User Profile',
                        'icon' => 'assets/images/icons/05.svg',
                        'href' => '#',
                        'submenu_class' => 'submenu mm-collapse parent-nav',
                        'submenu' => [
                            ['label' => 'Profile Setting', 'href' => 'profile-setting.php'],
                            ['label' => 'Log In', 'href' => 'log-in.php'],
                            ['label' => 'Registration', 'href' => 'registration.php'],
                        ],
                    ],
                ],
            ],
            'header' => [
                'search_placeholder' => 'Search',
                'notifications' => [
                    [
                        'avatar' => 'assets/images/avatar/user.svg',
                        'name' => 'MR.Crow Kader',
                        'time' => '1.3 hrs ago',
                        'excerpt' => 'Lorem ipsum dolor amet cosec...',
                    ],
                    [
                        'avatar' => 'assets/images/avatar/user-3.svg',
                        'name' => 'MR.Crow Kader',
                        'time' => '1.3 hrs ago',
                        'excerpt' => 'Lorem ipsum dolor amet cosec...',
                    ],
                    [
                        'avatar' => 'assets/images/avatar/user.svg',
                        'name' => 'MR.Crow Kader',
                        'time' => '1.3 hrs ago',
                        'excerpt' => 'Lorem ipsum dolor amet cosec...',
                    ],
                    [
                        'avatar' => 'assets/images/avatar/user.svg',
                        'name' => 'MR.Crow Kader',
                        'time' => '1.3 hrs ago',
                        'excerpt' => 'Lorem ipsum dolor amet cosec...',
                    ],
                    [
                        'avatar' => 'assets/images/avatar/user.svg',
                        'name' => 'MR.Crow Kader',
                        'time' => '1.3 hrs ago',
                        'excerpt' => 'Lorem ipsum dolor amet cosec...',
                    ],
                ],
                'language_options' => ['English', 'Bangla', 'Hindi', 'Latin'],
                'user' => [
                    'avatar' => 'assets/images/avatar/01.png',
                    'profile_avatar' => 'assets/images/avatar/user-2.svg',
                    'name' => 'MR.Crow Kader',
                    'role' => 'CEO, Valo How Masud',
                    'links' => [
                        ['href' => 'profile-setting.php', 'icon' => 'fa-light fa-user', 'label' => 'Profile Setting'],
                        ['href' => '#', 'icon' => 'fa-regular fa-gear', 'label' => 'Settings'],
                        ['href' => '#', 'icon' => 'fa-light fa-person-snowmobiling', 'label' => 'Billing'],
                        ['href' => '#', 'icon' => 'fa-solid fa-wave-pulse', 'label' => 'Activity'],
                        ['href' => '#', 'icon' => 'fa-regular fa-bell', 'label' => 'Help'],
                    ],
                    'logout_href' => '#',
                ],
            ],
            'overview_cards' => [
                [
                    'label' => 'Revenue',
                    'value' => '$1280',
                    'change' => '50.8%',
                    'trend_icon' => 'fa-light fa-arrow-up',
                    'image' => 'assets/images/avatar/04.png',
                ],
                [
                    'label' => 'Revenue',
                    'value' => '158',
                    'change' => '50.8%',
                    'trend_icon' => 'fa-light fa-arrow-up',
                    'image' => 'assets/images/avatar/05.png',
                ],
                [
                    'label' => 'Revenue',
                    'value' => '358',
                    'change' => '50.8%',
                    'trend_icon' => 'fa-light fa-arrow-up',
                    'image' => 'assets/images/avatar/06.png',
                ],
                [
                    'label' => 'Revenue',
                    'value' => '$89k',
                    'change' => '50.8%',
                    'trend_icon' => 'fa-light fa-arrow-up',
                    'image' => 'assets/images/avatar/07.png',
                ],
            ],
            'top_products' => [
                [
                    'image' => 'assets/images/grocery/08.jpg',
                    'name' => 'Quaker Oats Healthy Meal...',
                    'items' => '500 Items',
                    'coupon' => '2415',
                    'logo' => 'assets/images/brand/01.png',
                    'change' => '5.29%',
                    'price' => '$79.00',
                    'trend_icon' => 'assets/images/brand/arrow-m.png',
                ],
                [
                    'image' => 'assets/images/grocery/09.jpg',
                    'name' => 'Quaker Oats Healthy Meal...',
                    'items' => '500 Items',
                    'coupon' => '2415',
                    'logo' => 'assets/images/brand/08.png',
                    'change' => '5.29%',
                    'price' => '$79.00',
                    'trend_icon' => 'assets/images/brand/arrow-m.png',
                ],
                [
                    'image' => 'assets/images/grocery/10.jpg',
                    'name' => 'Quaker Oats Healthy Meal...',
                    'items' => '500 Items',
                    'coupon' => '2415',
                    'logo' => 'assets/images/brand/01.png',
                    'change' => '5.29%',
                    'price' => '$79.00',
                    'trend_icon' => 'assets/images/brand/arrow-m.png',
                ],
                [
                    'image' => 'assets/images/grocery/11.jpg',
                    'name' => 'Quaker Oats Healthy Meal...',
                    'items' => '500 Items',
                    'coupon' => '2415',
                    'logo' => 'assets/images/brand/09.png',
                    'change' => '5.29%',
                    'price' => '$79.00',
                    'trend_icon' => 'assets/images/brand/arrow-m.png',
                ],
                [
                    'image' => 'assets/images/grocery/11.jpg',
                    'name' => 'Quaker Oats Healthy Meal...',
                    'items' => '500 Items',
                    'coupon' => '2415',
                    'logo' => 'assets/images/brand/10.png',
                    'change' => '5.29%',
                    'price' => '$79.00',
                    'trend_icon' => 'assets/images/brand/arrow-m.png',
                ],
            ],
            'top_countries' => [
                ['flag' => 'assets/images/brand/02.png', 'name' => 'USA', 'value' => '6,546', 'date' => '04 Jul 2024'],
                ['flag' => 'assets/images/brand/02.png', 'name' => 'Fracnh', 'value' => '6,546', 'date' => '04 Jul 2024'],
                ['flag' => 'assets/images/brand/03.png', 'name' => 'India', 'value' => '6,546', 'date' => '04 Jul 2024'],
                ['flag' => 'assets/images/brand/04.png', 'name' => 'italy', 'value' => '6,546', 'date' => '04 Jul 2024'],
                ['flag' => 'assets/images/brand/05.png', 'name' => 'japan', 'value' => '6,546', 'date' => '04 Jul 2024'],
                ['flag' => 'assets/images/brand/06.png', 'name' => 'Koria', 'value' => '6,546', 'date' => '04 Jul 2024'],
                ['flag' => 'assets/images/brand/07.png', 'name' => 'Indor', 'value' => '6,546', 'date' => '04 Jul 2024'],
                ['flag' => 'assets/images/brand/05.png', 'name' => 'Vutan', 'value' => '6,546', 'date' => '04 Jul 2024'],
            ],
            'orders' => [
                ['image' => 'assets/images/grocery/08.jpg', 'name' => 'Quaker Oats Healthy Meal...', 'items' => '500 Items', 'price' => '$86.00', 'date' => '5 June 2024'],
                ['image' => 'assets/images/grocery/09.jpg', 'name' => 'Quaker Oats Healthy Meal...', 'items' => '500 Items', 'price' => '$86.00', 'date' => '5 June 2024'],
                ['image' => 'assets/images/grocery/10.jpg', 'name' => 'Quaker Oats Healthy Meal...', 'items' => '500 Items', 'price' => '$86.00', 'date' => '5 June 2024'],
                ['image' => 'assets/images/grocery/11.jpg', 'name' => 'Quaker Oats Healthy Meal...', 'items' => '500 Items', 'price' => '$86.00', 'date' => '5 June 2024'],
                ['image' => 'assets/images/grocery/12.jpg', 'name' => 'Quaker Oats Healthy Meal...', 'items' => '500 Items', 'price' => '$86.00', 'date' => '5 June 2024'],
                ['image' => 'assets/images/grocery/13.jpg', 'name' => 'Quaker Oats Healthy Meal...', 'items' => '500 Items', 'price' => '$86.00', 'date' => '5 June 2024'],
            ],
            'best_sellers' => [
                ['image' => 'assets/images/grocery/01.png', 'name' => 'Robert', 'purchases' => '75 Purchases', 'category' => 'Food, Grocery', 'revenue' => '$1,000', 'badge' => 'assets/images/grocery/02.png'],
                ['image' => 'assets/images/grocery/03.png', 'name' => 'mark Henri', 'purchases' => '75 Purchases', 'category' => 'Juice, Grocery', 'revenue' => '$1,000', 'badge' => 'assets/images/grocery/02.png'],
                ['image' => 'assets/images/grocery/04.png', 'name' => 'Krisob Kadri', 'purchases' => '75 Purchases', 'category' => 'Food, Grocery', 'revenue' => '$1,000', 'badge' => 'assets/images/grocery/02.png'],
                ['image' => 'assets/images/grocery/05.png', 'name' => 'Koriana Joo', 'purchases' => '75 Purchases', 'category' => 'Food, Grocery', 'revenue' => '$1,000', 'badge' => 'assets/images/grocery/02.png'],
                ['image' => 'assets/images/grocery/06.png', 'name' => 'Marlee', 'purchases' => '75 Purchases', 'category' => 'Food, Grocery', 'revenue' => '$1,000', 'badge' => 'assets/images/grocery/02.png'],
                ['image' => 'assets/images/grocery/01.png', 'name' => 'John Brush', 'purchases' => '75 Purchases', 'category' => 'Food, Grocery', 'revenue' => '$1,000', 'badge' => 'assets/images/grocery/02.png'],
                ['image' => 'assets/images/grocery/03.png', 'name' => 'Robert', 'purchases' => '75 Purchases', 'category' => 'Food, Grocery', 'revenue' => '$1,000', 'badge' => 'assets/images/grocery/02.png'],
            ],
            'footer' => [
                'copyright' => 'Copyright © 2024 All Right Reserved.',
                'links' => [
                    ['label' => 'Terms', 'href' => '#'],
                    ['label' => 'Privacy', 'href' => '#'],
                    ['label' => 'Help', 'href' => '#'],
                ],
            ],
        ];
    }
}
