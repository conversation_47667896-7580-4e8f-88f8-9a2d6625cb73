<?php

if (!function_exists('render_admin_top_products_section')) {
    function render_admin_top_products_section(array $products, array $countries): void
    {
        ?>
        <div class="row mt--10 g-5">
            <div class="col-xl-8 col-lg-12">
                <div class="top-product-wrapper-scroll">
                    <div class="top-product-area-start">
                        <div class="between-area-top">
                            <div class="left-area">
                                <h4 class="title">Top Products</h4>
                                <span>Top Products List</span>
                            </div>
                        </div>
                        <?php foreach ($products as $product): ?>
                            <div class="product-top-area-single">
                                <div class="image-area">
                                    <a href="#" class="thumbnail">
                                        <?php if (!empty($product['image'])): ?>
                                            <img src="<?= htmlspecialchars($product['image'], ENT_QUOTES, 'UTF-8'); ?>" alt="grocery">
                                        <?php endif; ?>
                                    </a>
                                    <div class="information">
                                        <p><?= htmlspecialchars($product['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                        <span><?= htmlspecialchars($product['items'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span>
                                    </div>
                                </div>

                                <div class="coupon-code flex-direction-column">
                                    <p>Coupon Code</p>
                                    <span class="d-block"><?= htmlspecialchars($product['coupon'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span>
                                </div>
                                <div class="logo">
                                    <?php if (!empty($product['logo'])): ?>
                                        <img src="<?= htmlspecialchars($product['logo'], ENT_QUOTES, 'UTF-8'); ?>" alt="rcf">
                                    <?php endif; ?>
                                </div>
                                <div class="indec">
                                    <div class="left">
                                        <p><?= htmlspecialchars($product['change'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                        <span><?= htmlspecialchars($product['price'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span>
                                    </div>
                                    <?php if (!empty($product['trend_icon'])): ?>
                                        <img src="<?= htmlspecialchars($product['trend_icon'], ENT_QUOTES, 'UTF-8'); ?>" alt="rcf">
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <div class="col-xl-4 col-lg-12">
                <div class="rop-product-right">
                    <div class="top-product-area-start">
                        <div class="between-area-top">
                            <div class="left-area">
                                <h4 class="title">Top Countries Sales</h4>
                                <span>Top Products List</span>
                            </div>
                            <div class="single-select">
                                <select style="display: none;">
                                    <option data-display="View all">View all</option>
                                    <option value="1">Some option</option>
                                    <option value="2">Another option</option>
                                    <option value="3" disabled="">A disabled option</option>
                                    <option value="4">Potato</option>
                                </select>
                                <div class="nice-select" tabindex="0"><span class="current">View all</span>
                                    <ul class="list">
                                        <li data-value="Default Sorting" data-display="View all" class="option selected">Default Sorting</li>
                                        <li data-value="1" class="option">Some option</li>
                                        <li data-value="2" class="option">Another option</li>
                                        <li data-value="3" class="option disabled">A disabled option</li>
                                        <li data-value="4" class="option">Potato</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php foreach ($countries as $country): ?>
                            <div class="product-top-area-single">
                                <div class="image-area">
                                    <a href="#" class="thumbnail">
                                        <?php if (!empty($country['flag'])): ?>
                                            <img src="<?= htmlspecialchars($country['flag'], ENT_QUOTES, 'UTF-8'); ?>" alt="grocery">
                                        <?php endif; ?>
                                    </a>
                                    <div class="information">
                                        <p class="mb--0"><?= htmlspecialchars($country['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                    </div>
                                </div>

                                <div class="coupon-code">
                                    <img src="assets/images/brand/arrow-m.png" alt="rcf">
                                </div>
                                <div class="coupon-code">
                                    <p><?= htmlspecialchars($country['value'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                </div>

                                <div class="indec mr--0">
                                    <p><?= htmlspecialchars($country['date'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}
