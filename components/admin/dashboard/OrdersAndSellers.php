<?php

if (!function_exists('render_admin_orders_and_sellers')) {
    function render_admin_orders_and_sellers(array $orders, array $sellers): void
    {
        ?>
        <div class="row g-5 mt--10">
            <div class="col-xl-6 col-lg-12">
                <div class="best-shop-seller-top-scroll">
                    <div class="top-product-area-start">
                        <div class="between-area-top">
                            <div class="left-area">
                                <h4 class="title">Orders</h4>
                            </div>
                            <div class="single-select">
                                <select style="display: none;">
                                    <option data-display="This Week">This Week</option>
                                    <option value="1">Some option</option>
                                    <option value="2">Another option</option>
                                    <option value="3" disabled="">A disabled option</option>
                                    <option value="4">Potato</option>
                                </select>
                                <div class="nice-select" tabindex="0"><span class="current">This Week</span>
                                    <ul class="list">
                                        <li data-value="Default Sorting" data-display="This Week" class="option selected">Default Sorting</li>
                                        <li data-value="1" class="option">Last Week</li>
                                        <li data-value="2" class="option">3 Month</li>
                                        <li data-value="4" class="option">6 Month</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php foreach ($orders as $order): ?>
                            <div class="product-top-area-single bottom">
                                <div class="image-area">
                                    <a href="#" class="thumbnail">
                                        <?php if (!empty($order['image'])): ?>
                                            <img src="<?= htmlspecialchars($order['image'], ENT_QUOTES, 'UTF-8'); ?>" alt="grocery">
                                        <?php endif; ?>
                                    </a>
                                    <div class="information">
                                        <p><?= htmlspecialchars($order['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                        <span><?= htmlspecialchars($order['items'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span>
                                    </div>
                                </div>

                                <div class="coupon-code">
                                    <p><?= htmlspecialchars($order['price'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                </div>
                                <div class="indec mr--0">
                                    <p><?= htmlspecialchars($order['date'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <div class="col-xl-6 col-lg-12">
                <div class="best-shop-seller-top-scroll">
                    <div class="top-product-area-start">
                        <div class="between-area-top">
                            <div class="left-area">
                                <h4 class="title">Best Shop Sellers</h4>
                            </div>
                            <div class="single-select">
                                <select style="display: none;">
                                    <option data-display="View all">View all</option>
                                    <option value="1">Some option</option>
                                    <option value="2">Another option</option>
                                    <option value="3" disabled="">A disabled option</option>
                                    <option value="4">Potato</option>
                                </select>
                                <div class="nice-select" tabindex="0"><span class="current">last Week</span>
                                    <ul class="list">
                                        <li data-value="Default Sorting" data-display="last Week" class="option selected">Last Week</li>
                                        <li data-value="1" class="option">1 Month</li>
                                        <li data-value="2" class="option">3 Month</li>
                                        <li data-value="4" class="option">6 Month</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <?php foreach ($sellers as $seller): ?>
                            <div class="product-top-area-single bottom">
                                <div class="image-area">
                                    <a href="#" class="thumbnail">
                                        <?php if (!empty($seller['image'])): ?>
                                            <img src="<?= htmlspecialchars($seller['image'], ENT_QUOTES, 'UTF-8'); ?>" alt="grocery">
                                        <?php endif; ?>
                                    </a>
                                    <div class="information">
                                        <p class="mb--5"><?= htmlspecialchars($seller['name'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                        <span><?= htmlspecialchars($seller['purchases'] ?? '', ENT_QUOTES, 'UTF-8'); ?></span>
                                    </div>
                                </div>

                                <div class="coupon-code justify-content-center">
                                    <p><?= htmlspecialchars($seller['category'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                </div>
                                <div class="coupon-code justify-content-center">
                                    <p><?= htmlspecialchars($seller['revenue'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                                </div>
                                <div class="indec mr--0">
                                    <?php if (!empty($seller['badge'])): ?>
                                        <img src="<?= htmlspecialchars($seller['badge'], ENT_QUOTES, 'UTF-8'); ?>" alt="rcf">
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}
