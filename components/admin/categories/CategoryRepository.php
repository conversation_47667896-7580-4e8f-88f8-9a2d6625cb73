<?php

use RCF\Repositories\CategoryRepository as MongoCategoryRepository;

class CategoryRepository
{
    private MongoCategoryRepository $repository;

    public function __construct()
    {
        if (!function_exists('getCategoryRepository')) {
            throw new RuntimeException('Bootstrap not loaded. Call require_once "../bootstrap.php" before using CategoryRepository.');
        }

        $this->repository = getCategoryRepository();

    }

    public function allStatuses(): array
    {
        return [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'draft' => 'Draft',
        ];
    }

    public function create(array $data): string
    {
        $categoryData = [
            'name' => $data['name'],
            'slug' => $data['slug'],
            'description' => $data['description'] ?? null,
            'status' => $data['status'] ?? 'active',
            'image_path' => $data['image_path'] ?? null,
        ];

        $categoryId = $this->repository->createCategory($categoryData);
        if ($categoryId === null) {
            throw new RuntimeException('Failed to create category in MongoDB.');
        }

        return $categoryId;
    }

    public function update(string $id, array $data): bool
    {
        $updateData = [
            'name' => $data['name'],
            'slug' => $data['slug'],
            'description' => $data['description'] ?? null,
            'status' => $data['status'] ?? 'active',
        ];

        if (array_key_exists('image_path', $data)) {
            $updateData['image_path'] = $data['image_path'];
        }

        return $this->repository->updateById($id, $updateData);
    }

    public function delete(string $id): bool
    {
        return $this->repository->deleteById($id);
    }

    public function find(string $id): ?array
    {
        $category = $this->repository->findById($id);
        return $category ? $this->normaliseCategory($category) : null;
    }

    public function findByName(string $name): ?array
    {
        $category = $this->repository->findOne(['name' => $name]);
        return $category ? $this->normaliseCategory($category) : null;
    }

    public function findBySlug(string $slug): ?array
    {
        $category = $this->repository->findBySlug($slug);
        return $category ? $this->normaliseCategory($category) : null;
    }

    public function count(?string $search = null, ?string $status = null): int
    {
        $filter = $this->buildFilter($search, $status);
        return $this->repository->count($filter);
    }

    public function paginate(int $page, int $perPage, ?string $search = null, ?string $status = null): array
    {
        $page = max(1, $page);
        $perPage = max(1, $perPage);

        $filter = $this->buildFilter($search, $status);
        $offset = ($page - 1) * $perPage;
        $options = [
            'skip' => $offset,
            'limit' => $perPage,
            'sort' => ['created_at' => -1],
        ];

        if ($this->isUsingMock() && $offset > 0) {
            $allResults = $this->repository->find($filter, ['sort' => $options['sort']]);
            $results = array_slice($allResults, $offset, $perPage);
        } else {
            $results = $this->repository->find($filter, $options);
        }

        return array_map(fn (array $category) => $this->normaliseCategory($category), $results);
    }

    public function all(): array
    {
        $results = $this->repository->find([], ['sort' => ['name' => 1]]);
        return array_map(fn (array $category) => $this->normaliseCategory($category), $results);
    }

    private function buildFilter(?string $search, ?string $status): array
    {
        $filter = [];

        if ($status !== null && $status !== '') {
            $filter['status'] = $status;
        }

        if ($search !== null && $search !== '') {
            $filter['$or'] = [
                ['name' => ['$regex' => $search, '$options' => 'i']],
                ['description' => ['$regex' => $search, '$options' => 'i']],
            ];
        }

        return $filter;
    }

    private function normaliseCategory(array $category): array
    {
        $category['description'] = $category['description'] ?? null;
        $category['image_path'] = $category['image_path'] ?? null;
        $category['status'] = $category['status'] ?? 'active';

        return $category;
    }

    private function isUsingMock(): bool
    {
        try {
            return getMongoConnection()->isUsingMock();
        } catch (\Throwable $exception) {
            return false;
        }
    }
}
