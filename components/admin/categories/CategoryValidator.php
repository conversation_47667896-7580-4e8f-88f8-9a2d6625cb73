<?php

class CategoryValidator
{
    public const NAME_MAX_LENGTH = 100;
    public const DESCRIPTION_MAX_LENGTH = 500;
    public const IMAGE_MAX_BYTES = 2_097_152; // 2 MB
    private const IMAGE_ALLOWED_MIME_TYPES = [
        'image/jpeg' => 'jpg',
        'image/png' => 'png',
        'image/webp' => 'webp',
    ];

    /**
     * @return array{valid: array<string, mixed>, errors: array<string, string>}
     */
    public static function validate(array $input, array $statusOptions, ?array $imageUpload = null): array
    {
        $errors = [];

        $name = trim((string) ($input['name'] ?? ''));
        $description = trim((string) ($input['description'] ?? ''));
        $status = (string) ($input['status'] ?? 'active');

        if ($name === '') {
            $errors['name'] = 'Category name is required.';
        } elseif (strlen($name) > self::NAME_MAX_LENGTH) {
            $errors['name'] = sprintf('Category name must be %d characters or fewer.', self::NAME_MAX_LENGTH);
        }

        if ($description !== '' && strlen($description) > self::DESCRIPTION_MAX_LENGTH) {
            $errors['description'] = sprintf('Description must be %d characters or fewer.', self::DESCRIPTION_MAX_LENGTH);
        }

        if (!array_key_exists($status, $statusOptions)) {
            $errors['status'] = 'Please select a valid status.';
        }

        $imageValidation = self::validateImageUpload($imageUpload);
        if (!$imageValidation['valid']) {
            $errors['image'] = $imageValidation['error'];
        }

        return [
            'valid' => [
                'name' => $name,
                'description' => $description !== '' ? $description : null,
                'status' => $status,
                'image_upload' => $imageValidation['file'],
            ],
            'errors' => $errors,
        ];
    }

    /**
     * @return array{valid: bool, error?: string, file: ?array}
     */
    private static function validateImageUpload(?array $imageUpload): array
    {
        if ($imageUpload === null || ($imageUpload['error'] ?? UPLOAD_ERR_NO_FILE) === UPLOAD_ERR_NO_FILE) {
            return ['valid' => true, 'file' => null];
        }

        if (!isset($imageUpload['error']) || $imageUpload['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'error' => 'Image upload failed. Please try again.',
                'file' => null,
            ];
        }

        $size = (int) ($imageUpload['size'] ?? 0);
        if ($size <= 0) {
            return [
                'valid' => false,
                'error' => 'The uploaded file appears to be empty.',
                'file' => null,
            ];
        }

        if ($size > self::IMAGE_MAX_BYTES) {
            return [
                'valid' => false,
                'error' => 'Please choose an image that is 2MB or smaller.',
                'file' => null,
            ];
        }

        $tmpName = $imageUpload['tmp_name'] ?? '';
        if (!is_string($tmpName) || $tmpName === '' || !is_uploaded_file($tmpName)) {
            return [
                'valid' => false,
                'error' => 'Invalid image upload. Please try again.',
                'file' => null,
            ];
        }

        $mimeType = null;
        if (class_exists('finfo')) {
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->file($tmpName) ?: null;
        }

        if ($mimeType === null && function_exists('mime_content_type')) {
            $mimeType = mime_content_type($tmpName) ?: null;
        }

        if (!is_string($mimeType) || !array_key_exists($mimeType, self::IMAGE_ALLOWED_MIME_TYPES)) {
            return [
                'valid' => false,
                'error' => 'Unsupported image format. Please upload a JPG, PNG, or WEBP file.',
                'file' => null,
            ];
        }

        $extension = self::IMAGE_ALLOWED_MIME_TYPES[$mimeType];

        $sanitisedUpload = $imageUpload;
        $sanitisedUpload['extension'] = $extension;
        $sanitisedUpload['mime_type'] = $mimeType;

        return [
            'valid' => true,
            'file' => $sanitisedUpload,
        ];
    }
}
