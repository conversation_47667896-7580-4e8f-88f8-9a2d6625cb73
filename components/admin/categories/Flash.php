<?php

class Flash
{
    private const FLASH_KEY = 'admin_flash_messages';

    public static function add(string $type, string $message): void
    {
        self::ensureSession();
        $_SESSION[self::FLASH_KEY][$type][] = $message;
    }

    /**
     * @return array<string, array<int, string>>
     */
    public static function consume(): array
    {
        self::ensureSession();
        $messages = $_SESSION[self::FLASH_KEY] ?? [];
        unset($_SESSION[self::FLASH_KEY]);

        return $messages;
    }

    private static function ensureSession(): void
    {
        if (session_status() === PHP_SESSION_NONE && !headers_sent()) {
            session_start();
        }
    }
}
