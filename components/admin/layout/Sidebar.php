<?php

if (!function_exists('render_admin_sidebar')) {
    function render_admin_sidebar(array $sidebar): void
    {
        $logo = $sidebar['logo'] ?? [];
        $menu = $sidebar['menu'] ?? [];
        ?>
        <div class="sidebar_left">
            <?php if (!empty($logo['src'])): ?>
                <a href="<?= htmlspecialchars($logo['href'] ?? '#', ENT_QUOTES, 'UTF-8'); ?>" class="logo">
                    <img src="<?= htmlspecialchars($logo['src'], ENT_QUOTES, 'UTF-8'); ?>" alt="<?= htmlspecialchars($logo['alt'] ?? 'logo', ENT_QUOTES, 'UTF-8'); ?>"<?php
                        if (isset($logo['height'])) {
                            echo html_attr('height', (string) $logo['height']);
                        }
                        if (isset($logo['width'])) {
                            echo html_attr('width', (string) $logo['width']);
                        }
                    ?>>
                </a>
            <?php endif; ?>

            <ul class="rts-side-nav-area-left menu-active-parent">
                <?php foreach ($menu as $item): ?>
                    <?php
                        $itemClasses = ['single-menu-item'];
                        if (!empty($item['is_active'])) {
                            $itemClasses[] = 'active';
                        }
                    ?>
                    <li class="<?= htmlspecialchars(implode(' ', $itemClasses), ENT_QUOTES, 'UTF-8'); ?>">
                        <?php $hasSubmenu = !empty($item['submenu']); ?>
                        <a href="<?= htmlspecialchars($item['href'] ?? '#', ENT_QUOTES, 'UTF-8'); ?>"<?= $hasSubmenu ? ' class="with-plus"' : ''; ?>>
                            <?php if (!empty($item['icon'])): ?>
                                <img src="<?= htmlspecialchars($item['icon'], ENT_QUOTES, 'UTF-8'); ?>" alt="icon" class="icon">
                            <?php endif; ?>
                            <p><?= htmlspecialchars($item['label'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
                        </a>
                        <?php if ($hasSubmenu): ?>
                            <ul class="<?= htmlspecialchars($item['submenu_class'] ?? 'submenu mm-collapse', ENT_QUOTES, 'UTF-8'); ?>">
                                <?php foreach ($item['submenu'] as $submenu): ?>
                                    <li>
                                        <a class="mobile-menu-link" href="<?= htmlspecialchars($submenu['href'] ?? '#', ENT_QUOTES, 'UTF-8'); ?>">
                                            <?= htmlspecialchars($submenu['label'] ?? '', ENT_QUOTES, 'UTF-8'); ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php
    }
}
