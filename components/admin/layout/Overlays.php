<?php

if (!function_exists('render_admin_overlays')) {
    function render_admin_overlays(): void
    {
        ?>
        <div class="progress-wrap active-progress">
            <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
                <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
            </svg>
        </div>

        <div class="search-input-area">
            <div class="container">
                <div class="search-input-inner">
                    <div class="input-div">
                        <input class="search-input autocomplete ui-autocomplete-input" type="text" placeholder="Search by keyword or #" autocomplete="off">
                        <button><i class="far fa-search"></i></button>
                    </div>
                </div>
            </div>
            <div id="close" class="search-close-icon"><i class="far fa-times"></i></div>
        </div>

        <div id="anywhere-home"></div>

        <div id="side-bar" class="side-bar header-two right-collups-add-product">
            <button class="close-icon-menu"><i class="far fa-times"></i></button>
            <div class="right-collups-area-top">
                <h6 class="title">Add New Product</h6>
                <p>Add information and add new product</p>
            </div>
            <div class="input-main-wrapper">
                <div class="single-input">
                    <label for="one">Product Name</label>
                    <input type="text" id="one" placeholder="Quaker Oats Healthy Meal...">
                </div>
                <div class="single-input">
                    <label for="Two">Regular Price</label>
                    <input type="text" id="Two" placeholder="240">
                </div>
                <div class="single-input">
                    <label for="sale">Sale Price</label>
                    <input type="text" id="sale" placeholder="$250">
                </div>
                <div class="single-input">
                    <label for="Stock">Stock</label>
                    <input type="text" id="Stock" placeholder="530">
                </div>
                <div class="single-input">
                    <label for="sku">SKU</label>
                    <input type="text" id="sku" placeholder="3245">
                </div>
                <div class="single-input">
                    <label for="cate">Category</label>
                    <input type="text" id="cate" placeholder="Notebook">
                </div>
                <div class="single-input">
                    <label for="Tag">Tag</label>
                    <input type="text" id="Tag" placeholder="Iphone, Mobile">
                </div>
                <div class="single-input">
                    <label for="text">Description</label>
                    <textarea name="text" id="text" placeholder="Type something"></textarea>
                </div>
                <div class="single-input">
                    <div class="file-upload-add-product">
                        <div class="profile-left">
                            <div class="profile-image mb--30">
                                <img id="rts_image" src="assets/images/grocery/16.png" alt="Profile-NFT">
                                <span>Drag and drop Image</span>
                            </div>
                            <div class="button-area">
                                <div class="brows-file-wrapper">
                                    <input name="rts_images1" id="rts_images1" type="file">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="button-area-botton-wrapper-p-list">
                    <button class="rts-btn btn-primary">Save</button>
                    <button class="rts-btn btn-primary bg-transparent">Cancel</button>
                </div>
            </div>
        </div>
        <?php
    }
}
