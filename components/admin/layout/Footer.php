<?php

if (!function_exists('render_admin_footer')) {
    function render_admin_footer(array $footer): void
    {
        $links = $footer['links'] ?? [];
        ?>
        <div class="footer-copyright">
            <div class="left">
                <p><?= htmlspecialchars($footer['copyright'] ?? '', ENT_QUOTES, 'UTF-8'); ?></p>
            </div>
            <ul>
                <?php foreach ($links as $link): ?>
                    <li><a href="<?= htmlspecialchars($link['href'] ?? '#', ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($link['label'] ?? '', ENT_QUOTES, 'UTF-8'); ?></a></li>
                <?php endforeach; ?>
            </ul>
         </div>
        <?php
    }
}
