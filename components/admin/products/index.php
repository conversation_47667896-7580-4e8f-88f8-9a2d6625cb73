<?php

require_once __DIR__ . '/ProductValidator.php';
require_once __DIR__ . '/ProductService.php';
require_once __DIR__ . '/../../shared/FileUploadService.php';

// Ensure ProductRepository is available
if (!class_exists('ProductRepository')) {
    require_once __DIR__ . '/../../../src/Repositories/ProductRepository.php';
    
    // Create a helper function to get ProductRepository instance
    if (!function_exists('getProductRepository')) {
        function getProductRepository(): \RCF\Repositories\ProductRepository {
            static $repository = null;
            if ($repository === null) {
                $repository = new \RCF\Repositories\ProductRepository();
            }
            return $repository;
        }
    }
}
