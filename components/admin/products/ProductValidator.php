<?php

class ProductValidator
{
    private const IMAGE_MAX_BYTES = 5 * 1024 * 1024; // 5MB
    private const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    private const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

    /**
     * Validate product data and image upload
     * 
     * @param array $input Raw input data
     * @param array|null $imageUpload File upload data
     * @param bool $isUpdate Whether this is an update operation
     * @return array{valid: array, errors: array<string, string>}
     */
    public static function validate(array $input, ?array $imageUpload = null, bool $isUpdate = false): array
    {
        $errors = [];
        $valid = [];

        // Validate required fields
        $requiredFields = $isUpdate ? [] : ['name', 'price'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                $errors[$field] = ucfirst($field) . ' is required.';
            }
        }

        // Validate product name
        if (!empty($input['name'])) {
            $name = trim($input['name']);
            if (strlen($name) < 2) {
                $errors['name'] = 'Product name must be at least 2 characters long.';
            } elseif (strlen($name) > 255) {
                $errors['name'] = 'Product name must not exceed 255 characters.';
            } else {
                $valid['name'] = $name;
            }
        }

        // Validate description
        if (!empty($input['description'])) {
            $description = trim($input['description']);
            if (strlen($description) > 5000) {
                $errors['description'] = 'Description must not exceed 5000 characters.';
            } else {
                $valid['description'] = $description;
            }
        }

        // Validate price
        if (!empty($input['price'])) {
            $price = filter_var($input['price'], FILTER_VALIDATE_FLOAT);
            if ($price === false || $price < 0) {
                $errors['price'] = 'Please enter a valid price (0 or greater).';
            } elseif ($price > 999999.99) {
                $errors['price'] = 'Price cannot exceed 999,999.99.';
            } else {
                $valid['price'] = $price;
            }
        }

        // Validate sale price
        if (!empty($input['sale_price'])) {
            $salePrice = filter_var($input['sale_price'], FILTER_VALIDATE_FLOAT);
            if ($salePrice === false || $salePrice < 0) {
                $errors['sale_price'] = 'Please enter a valid sale price (0 or greater).';
            } elseif ($salePrice > 999999.99) {
                $errors['sale_price'] = 'Sale price cannot exceed 999,999.99.';
            } elseif (isset($valid['price']) && $salePrice >= $valid['price']) {
                $errors['sale_price'] = 'Sale price must be less than the regular price.';
            } else {
                $valid['sale_price'] = $salePrice;
            }
        }

        // Validate stock quantity
        if (isset($input['stock_quantity'])) {
            $stock = filter_var($input['stock_quantity'], FILTER_VALIDATE_INT);
            if ($stock === false || $stock < 0) {
                $errors['stock_quantity'] = 'Please enter a valid stock quantity (0 or greater).';
            } elseif ($stock > 999999) {
                $errors['stock_quantity'] = 'Stock quantity cannot exceed 999,999.';
            } else {
                $valid['stock_quantity'] = $stock;
            }
        }

        // Validate category
        if (!empty($input['category_id'])) {
            $categoryId = trim($input['category_id']);
            if (!self::isValidObjectId($categoryId)) {
                $errors['category_id'] = 'Please select a valid category.';
            } else {
                $valid['category_id'] = $categoryId;
            }
        }

        // Validate tags
        if (!empty($input['tags'])) {
            $tags = is_array($input['tags']) ? $input['tags'] : explode(',', $input['tags']);
            $validTags = [];
            
            foreach ($tags as $tag) {
                $tag = trim($tag);
                if ($tag !== '' && strlen($tag) <= 50) {
                    $validTags[] = $tag;
                }
            }
            
            if (count($validTags) > 20) {
                $errors['tags'] = 'Maximum 20 tags are allowed.';
            } else {
                $valid['tags'] = $validTags;
            }
        }

        // Validate status
        if (!empty($input['status'])) {
            $allowedStatuses = ['active', 'inactive', 'draft', 'out_of_stock'];
            if (!in_array($input['status'], $allowedStatuses)) {
                $errors['status'] = 'Please select a valid status.';
            } else {
                $valid['status'] = $input['status'];
            }
        }

        // Validate featured flag
        if (isset($input['featured'])) {
            $valid['featured'] = !empty($input['featured']);
        }

        // Validate weight
        if (!empty($input['weight'])) {
            $weight = filter_var($input['weight'], FILTER_VALIDATE_FLOAT);
            if ($weight === false || $weight < 0) {
                $errors['weight'] = 'Please enter a valid weight (0 or greater).';
            } elseif ($weight > 99999.99) {
                $errors['weight'] = 'Weight cannot exceed 99,999.99.';
            } else {
                $valid['weight'] = $weight;
            }
        }

        // Validate dimensions
        foreach (['length', 'width', 'height'] as $dimension) {
            if (!empty($input[$dimension])) {
                $value = filter_var($input[$dimension], FILTER_VALIDATE_FLOAT);
                if ($value === false || $value < 0) {
                    $errors[$dimension] = "Please enter a valid {$dimension} (0 or greater).";
                } elseif ($value > 9999.99) {
                    $errors[$dimension] = ucfirst($dimension) . " cannot exceed 9,999.99.";
                } else {
                    $valid[$dimension] = $value;
                }
            }
        }

        // Validate SKU
        if (!empty($input['sku'])) {
            $sku = trim($input['sku']);
            if (!preg_match('/^[A-Za-z0-9_-]+$/', $sku)) {
                $errors['sku'] = 'SKU can only contain letters, numbers, hyphens, and underscores.';
            } elseif (strlen($sku) > 50) {
                $errors['sku'] = 'SKU must not exceed 50 characters.';
            } else {
                $valid['sku'] = strtoupper($sku);
            }
        }

        // Validate image upload
        $imageValidation = self::validateImageUpload($imageUpload);
        if (!$imageValidation['valid']) {
            if (isset($imageValidation['error'])) {
                $errors['image'] = $imageValidation['error'];
            }
        } else {
            $valid['image_upload'] = $imageValidation['file'];
        }

        return [
            'valid' => $valid,
            'errors' => $errors,
        ];
    }

    /**
     * Validate image upload
     * 
     * @return array{valid: bool, error?: string, file: ?array}
     */
    private static function validateImageUpload(?array $imageUpload): array
    {
        if ($imageUpload === null || ($imageUpload['error'] ?? UPLOAD_ERR_NO_FILE) === UPLOAD_ERR_NO_FILE) {
            return ['valid' => true, 'file' => null];
        }

        if (!isset($imageUpload['error']) || $imageUpload['error'] !== UPLOAD_ERR_OK) {
            $errorCode = isset($imageUpload['error']) ? (int) $imageUpload['error'] : UPLOAD_ERR_NO_FILE;
            return [
                'valid' => false,
                'error' => self::getUploadErrorMessage($errorCode),
                'file' => null,
            ];
        }

        $size = (int) ($imageUpload['size'] ?? 0);
        if ($size <= 0) {
            return [
                'valid' => false,
                'error' => 'The uploaded file appears to be empty.',
                'file' => null,
            ];
        }

        if ($size > self::IMAGE_MAX_BYTES) {
            return [
                'valid' => false,
                'error' => 'Please choose an image that is 5MB or smaller.',
                'file' => null,
            ];
        }

        $tmpName = $imageUpload['tmp_name'] ?? '';
        if (!is_string($tmpName) || $tmpName === '' || !is_uploaded_file($tmpName)) {
            return [
                'valid' => false,
                'error' => 'Invalid image upload. Please try again.',
                'file' => null,
            ];
        }

        // Validate MIME type
        $mimeType = self::detectMimeType($tmpName);
        if (!in_array($mimeType, self::ALLOWED_IMAGE_TYPES)) {
            return [
                'valid' => false,
                'error' => 'Please upload a valid image file (JPEG, PNG, GIF, or WebP).',
                'file' => null,
            ];
        }

        // Validate file extension
        $originalName = $imageUpload['name'] ?? '';
        $extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
        if (!in_array($extension, self::ALLOWED_EXTENSIONS)) {
            return [
                'valid' => false,
                'error' => 'Please upload a file with a valid extension (jpg, jpeg, png, gif, webp).',
                'file' => null,
            ];
        }

        // Validate image dimensions and integrity
        $imageInfo = @getimagesize($tmpName);
        if ($imageInfo === false) {
            return [
                'valid' => false,
                'error' => 'The uploaded file is not a valid image.',
                'file' => null,
            ];
        }

        [$width, $height] = $imageInfo;
        if ($width < 50 || $height < 50) {
            return [
                'valid' => false,
                'error' => 'Image must be at least 50x50 pixels.',
                'file' => null,
            ];
        }

        if ($width > 5000 || $height > 5000) {
            return [
                'valid' => false,
                'error' => 'Image dimensions cannot exceed 5000x5000 pixels.',
                'file' => null,
            ];
        }

        return [
            'valid' => true,
            'file' => [
                'tmp_name' => $tmpName,
                'name' => $originalName,
                'extension' => $extension,
                'size' => $size,
                'mime_type' => $mimeType,
                'width' => $width,
                'height' => $height,
            ],
        ];
    }

    /**
     * Detect MIME type of uploaded file
     */
    private static function detectMimeType(string $filePath): ?string
    {
        if (class_exists('finfo')) {
            $finfo = new finfo(FILEINFO_MIME_TYPE);
            $mimeType = $finfo->file($filePath);
            if ($mimeType !== false) {
                return $mimeType;
            }
        }

        if (function_exists('mime_content_type')) {
            $mimeType = mime_content_type($filePath);
            if ($mimeType !== false) {
                return $mimeType;
            }
        }

        return null;
    }

    /**
     * Get human-readable upload error message
     */
    private static function getUploadErrorMessage(int $errorCode): string
    {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return 'The uploaded file is too large.';
            case UPLOAD_ERR_PARTIAL:
                return 'The file was only partially uploaded. Please try again.';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder. Please contact support.';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk. Please contact support.';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension. Please contact support.';
            default:
                return 'Image upload failed. Please try again.';
        }
    }

    /**
     * Check if string is a valid MongoDB ObjectId
     */
    private static function isValidObjectId(string $id): bool
    {
        return preg_match('/^[a-f\d]{24}$/i', $id) === 1;
    }
}
