<?php

if (!function_exists('render_compare_product')) {
    /**
     * Shared renderer for compare-modal product rows.
     */
    function render_compare_product(array $product): void
    {
        $image = $product['image'] ?? [];
        $title = $product['title'] ?? '';
        $href = $product['href'] ?? '#';
        $price = $product['price'] ?? [];
        $description = $product['description'] ?? '';
        $rating = $product['rating'] ?? 0;
        $cta = $product['cta'] ?? null;
        $meta = $product['meta'] ?? [];

        echo '<div class="single-compare-start">';
        echo '<div class="image-area">';
        if ($image) {
            echo '<img src="' . htmlspecialchars($image['src'] ?? '', ENT_QUOTES, 'UTF-8') . '" alt="' . htmlspecialchars($image['alt'] ?? 'compare', ENT_QUOTES, 'UTF-8') . '">';
        }
        echo '<a href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '" class="title">' . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . '</a>';
        render_price(['current' => $price['current'] ?? null, 'previous' => $price['previous'] ?? null]);
        echo '</div>';
        echo '<div class="information-area">';
        if ($description) {
            echo '<p class="disc">' . htmlspecialchars($description, ENT_QUOTES, 'UTF-8') . '</p>';
        }
        if ($rating) {
            echo '<div class="rating">';
            for ($i = 0; $i < 5; $i++) {
                $iconClass = $i < $rating ? 'fa-solid fa-star' : 'fa-regular fa-star';
                render_icon($iconClass);
            }
            echo '</div>';
        }
        if ($cta) {
            render_button_element($cta);
        }
        if ($meta) {
            echo '<div class="product-details-deep">';
            foreach ($meta as $item) {
                echo '<p class="deep-d"><span>' . htmlspecialchars($item['label'] ?? '', ENT_QUOTES, 'UTF-8') . ':</span> ' . htmlspecialchars($item['value'] ?? '', ENT_QUOTES, 'UTF-8') . '</p>';
            }
            echo '</div>';
        }
        echo '</div>';
        echo '</div>';
    }
}
