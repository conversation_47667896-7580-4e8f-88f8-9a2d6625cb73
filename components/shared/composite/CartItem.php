<?php

if (!function_exists('render_cart_item')) {
    /**
     * Shared renderer for mini-cart line items.
     */
    function render_cart_item(array $item): void
    {
        $wrapperClass = $item['class'] ?? 'cart-item-1';
        $image = $item['image'] ?? [];
        $title = $item['title'] ?? '';
        $href = $item['href'] ?? '#';
        $quantity = $item['quantity'] ?? 1;
        $price = $item['price'] ?? '';
        $removable = $item['removable'] ?? true;

        echo '<div' . html_attr('class', $wrapperClass) . '>';
        echo '<div class="img-name">';
        if ($image) {
            echo '<div class="thumbanil">';
            echo '<img src="' . htmlspecialchars($image['src'] ?? '', ENT_QUOTES, 'UTF-8') . '" alt="' . htmlspecialchars($image['alt'] ?? '', ENT_QUOTES, 'UTF-8') . '">';
            echo '</div>';
        }
        echo '<div class="details">';
        echo '<a href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '"><h5 class="title">' . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . '</h5></a>';
        echo '<div class="number">' . htmlspecialchars((string) $quantity, ENT_QUOTES, 'UTF-8');
        echo ' <i class="fa-regular fa-x"></i>';
        if ($price !== '') {
            echo '<span>' . htmlspecialchars($price, ENT_QUOTES, 'UTF-8') . '</span>';
        }
        echo '</div>';
        echo '</div>';
        echo '</div>';
        if ($removable) {
            echo '<div class="close-c1"><i class="fa-regular fa-x"></i></div>';
        }
        echo '</div>';
    }
}
