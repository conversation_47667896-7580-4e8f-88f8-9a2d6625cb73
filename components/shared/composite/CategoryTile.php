<?php

if (!function_exists('render_category_tile')) {
    /**
     * Lightweight renderer for category grid/list tiles.
     */
    function render_category_tile(array $category): void
    {
        $href = $category['href'] ?? '#';
        $image = $category['image'] ?? null;
        $label = $category['label'] ?? '';
        $wrapperClass = $category['class'] ?? 'single-category-one';

        echo '<a' . html_attr('href', $href) . html_attr('class', $wrapperClass) . '>';
        if ($image) {
            $src = $image['src'] ?? '';
            $alt = $image['alt'] ?? 'category';
            echo '<img src="' . htmlspecialchars($src, ENT_QUOTES, 'UTF-8') . '" alt="' . htmlspecialchars($alt, ENT_QUOTES, 'UTF-8') . '">';
        }
        if ($label !== '') {
            echo '<p>' . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . '</p>';
        }
        echo '</a>';
    }
}
