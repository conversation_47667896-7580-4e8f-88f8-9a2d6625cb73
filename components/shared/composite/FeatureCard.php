<?php

if (!function_exists('render_feature_card')) {
    /**
     * Renders the promotional feature cards used in the home layouts.
     */
    function render_feature_card(array $card): void
    {
        $wrapperClass = $card['class'] ?? 'single-feature-card bg_image';
        $label = $card['label'] ?? null;
        $title = $card['title'] ?? '';
        $titleAccent = $card['accent'] ?? '';
        $cta = $card['cta'] ?? null;
        $shopNow = $card['shop_now'] ?? null;

        echo '<div' . html_attr('class', $wrapperClass) . '>';
        echo '<div class="content-area">';
        if ($label) {
            render_button_element($label);
        }
        if ($title) {
            echo '<h3 class="title">' . htmlspecialchars($title, ENT_QUOTES, 'UTF-8');
            if ($titleAccent) {
                echo '<br><span>' . htmlspecialchars($titleAccent, ENT_QUOTES, 'UTF-8') . '</span>';
            }
            echo '</h3>';
        }
        if ($shopNow) {
            echo '<a' . html_attr('href', $shopNow['href'] ?? '#') . ' class="shop-now-goshop-btn">';
            echo '<span class="text">' . htmlspecialchars($shopNow['text'] ?? 'Shop Now', ENT_QUOTES, 'UTF-8') . '</span>';
            echo '<div class="plus-icon">';
            render_icon('fa-sharp fa-regular fa-plus');
            echo '</div><div class="plus-icon">';
            render_icon('fa-sharp fa-regular fa-plus');
            echo '</div>';
            echo '</a>';
        }
        if ($cta) {
            render_button_element($cta);
        }
        echo '</div>';
        echo '</div>';
    }
}
