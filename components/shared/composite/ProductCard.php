<?php

if (!function_exists('render_product_card')) {
    /**
     * Outputs a full product card, including imagery, pricing, and CTA.
     */
    function render_product_card(array $product): void
    {
        $wrapperClass = $product['class'] ?? 'single-shopping-card-one tranding-product';
        $image = $product['image'] ?? [];
        $badge = $product['badge'] ?? null;
        $actions = $product['actions'] ?? [];
        $title = $product['title'] ?? '';
        $titleHref = $product['href'] ?? '#';
        $availability = $product['availability'] ?? null;
        $price = $product['price'] ?? [];
        $detailsButton = $product['details_button'] ?? null;

        echo '<div' . html_attr('class', $wrapperClass) . '>';
        if ($image) {
            $thumbnailClass = $image['class'] ?? 'thumbnail-preview';
            $imgSrc = $image['src'] ?? '';
            $imgAlt = $image['alt'] ?? 'product';
            $imgHref = $image['href'] ?? $titleHref;

            echo '<a' . html_attr('href', $imgHref) . html_attr('class', $thumbnailClass) . '>';
            if ($badge) {
                echo '<div class="badge">';
                if (!empty($badge['prefix'])) {
                    echo '<span>' . htmlspecialchars($badge['prefix'], ENT_QUOTES, 'UTF-8') . ' <br>';
                    echo htmlspecialchars($badge['text'] ?? '', ENT_QUOTES, 'UTF-8') . '</span>';
                } else {
                    render_badge(['text' => $badge['text'] ?? '', 'class' => $badge['class'] ?? 'badge']);
                }
                if (!empty($badge['icon'])) {
                    render_icon($badge['icon']);
                }
                echo '</div>';
            }
            if ($imgSrc !== '') {
                echo '<img src="' . htmlspecialchars($imgSrc, ENT_QUOTES, 'UTF-8') . '" alt="' . htmlspecialchars($imgAlt, ENT_QUOTES, 'UTF-8') . '">';
            }
            echo '</a>';
        }

        if (!empty($actions)) {
            echo '<div class="top-action">';
            foreach ($actions as $action) {
                $actionClass = $action['class'] ?? 'single-action';
                $actionTitle = $action['title'] ?? '';
                $iconClass = $action['icon'] ?? '';
                $extraAttrs = '';
                foreach (($action['attributes'] ?? []) as $attr => $value) {
                    $extraAttrs .= html_attr($attr, $value);
                }
                echo '<div' . html_attr('class', $actionClass) . html_attr('data-flow', $action['flow'] ?? null) . html_attr('title', $actionTitle) . $extraAttrs . '>';
                render_icon($iconClass);
                echo '</div>';
            }
            echo '</div>';
        }

        echo '<div class="body-content">';
        if ($title) {
            echo '<a' . html_attr('href', $titleHref) . '><h4 class="title">' . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . '</h4></a>';
        }
        if ($availability) {
            echo '<span class="availability">' . htmlspecialchars($availability, ENT_QUOTES, 'UTF-8') . '</span>';
        }
        if (!empty($price)) {
            render_price($price);
        }
        if ($detailsButton) {
            echo '<div class="cart-counter-action">';
            render_quantity_stepper($detailsButton['quantity'] ?? []);
            if (!empty($detailsButton['button'])) {
                render_button_element($detailsButton['button']);
            }
            echo '</div>';
        }
        echo '</div>';

        echo '</div>';
    }
}
