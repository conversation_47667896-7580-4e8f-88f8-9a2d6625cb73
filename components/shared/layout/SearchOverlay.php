<?php

if (!function_exists('render_search_overlay')) {
    /**
     * Renders the global search overlay and progress indicator.
     */
    function render_search_overlay(): void
    {
        $overlayPlaceholder = site_config('search.overlay_placeholder', 'Search by keyword or #');
        ?>
        <div class="search-input-area">
            <div class="container">
                <div class="search-input-inner">
                    <div class="input-div">
                        <input id="searchInput1" class="search-input" type="text" placeholder="<?= htmlspecialchars($overlayPlaceholder, ENT_QUOTES, 'UTF-8'); ?>">
                        <button><i class="far fa-search"></i></button>
                    </div>
                </div>
            </div>
            <div id="close" class="search-close-icon"><i class="far fa-times"></i></div>
        </div>
        <div id="anywhere-home" class="anywere"></div>
        <div class="progress-wrap">
            <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
                <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 307.919;"></path>
            </svg>
        </div>
        <?php
    }
}
