<?php

if (!function_exists('resolve_body_class')) {
    function resolve_body_class(?string $custom): string
    {
        $defaults = site_config('layout.default_body_class', '');
        $parts = array_filter([
            $defaults,
            $custom,
        ]);

        return trim(implode(' ', array_unique(array_map('trim', $parts))));
    }
}

if (!function_exists('render_document_open')) {
    /**
     * Outputs the document shell up to the start of the page body.
     */
    function render_document_open(array $meta = []): void
    {
        $title = $meta['title'] ?? site_config('meta.title', 'RC Furnishing');
        $description = $meta['description'] ?? site_config('meta.description', '');
        $keywords = $meta['keywords'] ?? site_config('meta.keywords', '');
        $favicon = site_config('brand.favicon', 'assets/images/fav.png');
        $bodyClass = resolve_body_class($meta['body_class'] ?? null);

        echo "<!DOCTYPE html>\n";
        echo '<html lang="en">';
        echo '<head>';
        echo '<meta charset="UTF-8">';
        echo '<meta name="description" content="' . htmlspecialchars($description, ENT_QUOTES, 'UTF-8') . '">';
        echo '<meta name="viewport" content="width=device-width, initial-scale=1.0">';
        echo '<meta name="keywords" content="' . htmlspecialchars($keywords, ENT_QUOTES, 'UTF-8') . '">';
        echo '<title>' . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . '</title>';
        echo '<link rel="shortcut icon" type="image/x-icon" href="' . htmlspecialchars($favicon, ENT_QUOTES, 'UTF-8') . '">';
        echo '<link rel="stylesheet preload" href="assets/css/plugins.css" as="style">';
        echo '<link rel="stylesheet preload" href="assets/css/style.css" as="style">';
        echo '</head>';
        echo '<body' . html_attr('class', $bodyClass !== '' ? $bodyClass : null) . '>';
    }
}

if (!function_exists('render_document_close')) {
    /**
     * Closes the document body and html tags.
     */
    function render_document_close(): void
    {
        echo '</body>';
        echo '</html>';
    }
}
