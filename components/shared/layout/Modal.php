<?php

if (!function_exists('build_default_compare_products')) {
    function build_default_compare_products(): array
    {
        return [
            [
                'image' => ['src' => 'assets/images/grocery/01.jpg', 'alt' => 'grocery'],
                'title' => 'Foster Farms Takeout Crispy Classic Buffalo Wings',
                'href' => 'shop-details.php',
                'price' => ['current' => '36.00', 'previous' => '36.00'],
                'description' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                'rating' => 4,
                'cta' => [
                    'text' => 'Add to cart',
                    'href' => 'cart.php',
                    'class' => 'rts-btn btn-primary',
                ],
                'meta' => [
                    ['label' => 'SKU', 'value' => '2546-1'],
                    ['label' => 'Availability', 'value' => '2546-1'],
                    ['label' => 'Vendor', 'value' => '2546-1'],
                ],
            ],
            [
                'image' => ['src' => 'assets/images/grocery/02.jpg', 'alt' => 'grocery'],
                'title' => 'Foster Farms Takeout Crispy Classic Buffalo Wings',
                'href' => 'shop-details.php',
                'price' => ['current' => '36.00', 'previous' => '36.00'],
                'description' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                'rating' => 4,
                'cta' => [
                    'text' => 'Add to cart',
                    'href' => 'cart.php',
                    'class' => 'rts-btn btn-primary',
                ],
                'meta' => [
                    ['label' => 'SKU', 'value' => '2546-1'],
                    ['label' => 'Availability', 'value' => '2546-1'],
                    ['label' => 'Vendor', 'value' => '2546-1'],
                ],
            ],
        ];
    }
}

if (!function_exists('render_compare_modal')) {
    /**
     * Renders the compare modal contents reused across templates.
     */
    function render_compare_modal(?array $products = null): void
    {
        $products = $products ?? build_default_compare_products();
        ?>
        <div class="modal fade" id="exampleModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <h5 class="title">Compare</h5>
                        <div class="compare-wrapper-modal">
                            <?php foreach ($products as $product): ?>
                                <?php render_compare_product($product); ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}
