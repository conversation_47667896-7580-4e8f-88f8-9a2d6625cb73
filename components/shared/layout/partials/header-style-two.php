<?php
if (!isset($topAreaClass)) {
    $topAreaClass = 'header-top-area-two';
}
if (!isset($searchAreaClass)) {
    $searchAreaClass = 'search-header-area-main';
}
if (!isset($searchContainerClass)) {
    $searchContainerClass = 'container-2';
}
?>
<header class="header-style-two bg-primary-header">
    <div<?= html_attr('class', $topAreaClass); ?>>
        <div class="container-2">
            <div class="row">
                <div class="col-lg-12">
                    <div class="hader-top-between-two">
                        <p><?= htmlspecialchars($header['welcome_message'], ENT_QUOTES, 'UTF-8'); ?></p>
                        <ul class="nav-header-top">
                            <?php render_nav_links($header['header_top_links']); ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div<?= html_attr('class', $searchAreaClass); ?>>
        <div<?= html_attr('class', $searchContainerClass); ?>>
            <div class="row">
                <div class="col-lg-12">
                    <div class="logo-search-category-wrapper">
                        <a href="index.php" class="logo-area">
                            <img src="<?= htmlspecialchars($header['secondary_logo'], ENT_QUOTES, 'UTF-8'); ?>" alt="logo-main" class="logo">
                        </a>
                        <div class="category-search-wrapper">
                            <div class="category-btn category-hover-header">
                                <img class="parent" src="assets/images/icons/bar-1.svg" alt="icons">
                                <span>Categories</span>
                                <ul class="category-sub-menu" id="category-active-four">
                                    <?php render_category_menu($header['categories'], [
                                        'item_link_class' => 'menu-item',
                                        'submenu_class' => 'submenu mm-collapse',
                                        'submenu_link_class' => 'mobile-menu-link',
                                    ]); ?>
                                </ul>
                            </div>
                            <form action="#" class="search-header">
                                <input type="text" placeholder="<?= htmlspecialchars($header['search_placeholder'], ENT_QUOTES, 'UTF-8'); ?>" required>
                                <?php render_button('search'); ?>
                            </form>
                        </div>
                        <div class="accont-wishlist-cart-area-header">
                            <a href="account.php" class="btn-border-only account">
                                <i class="fa-light fa-user"></i>
                                <span>Account</span>
                            </a>
                            <a href="wishlist.php" class="btn-border-only wishlist">
                                <i class="fa-regular fa-heart"></i>
                                <span class="text">Wishlist</span>
                                <span class="number">2</span>
                            </a>
                            <div class="btn-border-only cart category-hover-header">
                                <i class="fa-sharp fa-regular fa-paper-plane"></i>
                                <span class="text">My Cart</span>
                                <span class="number"><?= count($header['cart_items']); ?></span>
                                <div class="category-sub-menu card-number-show">
                                    <h5 class="shopping-cart-number">Shopping Cart (<?= count($header['cart_items']); ?>)</h5>
                                    <?php foreach ($header['cart_items'] as $index => $cartItem): ?>
                                        <?php
                                        $itemClass = 'cart-item-1' . ($index === 0 ? ' border-top' : '');
                                        render_cart_item(array_merge($cartItem, ['class' => $itemClass]));
                                        ?>
                                    <?php endforeach; ?>
                                    <div class="sub-total-cart-balance">
                                        <div class="bottom-content-deals mt--10">
                                            <div class="top">
                                                <span>Sub Total:</span>
                                                <span class="number-c">$108.00</span>
                                            </div>
                                            <div class="single-progress-area-incard">
                                                <div class="progress">
                                                    <div class="progress-bar wow fadeInLeft" role="progressbar" style="width: 80%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </div>
                                            <p>Spend More <span>$125.00</span> to reach <span>Free Shipping</span></p>
                                        </div>
                                        <div class="button-wrapper d-flex align-items-center justify-content-between">
                                            <?php render_button_element([
                                                'text' => 'View Cart',
                                                'href' => 'cart.php',
                                                'class' => 'rts-btn btn-primary',
                                            ]); ?>
                                            <?php render_button_element([
                                                'text' => 'CheckOut',
                                                'href' => 'checkout.php',
                                                'class' => 'rts-btn btn-primary border-only',
                                            ]); ?>
                                        </div>
                                    </div>
                                </div>
                                <a href="cart.php" class="over_link"></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php render_partial(__DIR__ . '/nav-primary.php', [
        'header' => $header,
        'navClass' => 'rts-header-nav-area-one header-two header--sticky',
        'containerClass' => 'container-2',
    ]); ?>
</header>
<?php render_partial(__DIR__ . '/header-mobile.php', ['header' => $header]); ?>
