<?php
$navClass = isset($navClass) ? $navClass : 'rts-header-nav-area-one header--sticky';
$containerClass = isset($containerClass) ? $containerClass : 'container';
$menuItems = $header['main_menu'] ?? [];
$navCta = $header['nav_cta'] ?? [];

$renderSubmenu = function (array $submenu) {
    echo '<ul class="submenu">';
    foreach ($submenu as $link) {
        $label = $link['label'] ?? '';
        $href = $link['href'] ?? '#';
        $class = 'sub-b' . (!empty($link['class']) ? ' ' . $link['class'] : '');
        echo '<li><a class="' . htmlspecialchars($class, ENT_QUOTES, 'UTF-8') . '" href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . '</a></li>';
    }
    echo '</ul>';
};

$renderMegamenu = function (array $megamenu) {
    $columns = $megamenu['columns'] ?? [];
    $feature = $megamenu['feature'] ?? null;
    echo '<div class="rts-megamenu">';
    echo '<div class="wrapper">';
    echo '<div class="row align-items-center">';
    echo '<div class="col-lg-8">';
    echo '<div class="megamenu-item-wrapper">';
    foreach ($columns as $column) {
        $title = $column['title'] ?? '';
        $links = $column['links'] ?? [];
        echo '<div class="single-megamenu-wrapper">';
        if ($title !== '') {
            echo '<p class="title">' . htmlspecialchars($title, ENT_QUOTES, 'UTF-8') . '</p>';
        }
        if ($links) {
            echo '<ul>';
            foreach ($links as $link) {
                $href = $link['href'] ?? '#';
                $label = $link['label'] ?? '';
                echo '<li><a class="sub-b" href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . '</a></li>';
            }
            echo '</ul>';
        }
        echo '</div>';
    }
    echo '</div>';
    echo '</div>';
    if ($feature) {
        $featureHref = $feature['href'] ?? '#';
        $featureSrc = $feature['image'] ?? '';
        $featureAlt = $feature['alt'] ?? 'feature';
        echo '<div class="col-lg-4">';
        echo '<a href="' . htmlspecialchars($featureHref, ENT_QUOTES, 'UTF-8') . '" class="feature-add-megamenu-area">';
        if ($featureSrc !== '') {
            echo '<img src="' . htmlspecialchars($featureSrc, ENT_QUOTES, 'UTF-8') . '" alt="' . htmlspecialchars($featureAlt, ENT_QUOTES, 'UTF-8') . '">';
        }
        echo '</a>';
        echo '</div>';
    }
    echo '</div>';
    echo '</div>';
    echo '</div>';
};
?>
<div<?= html_attr('class', $navClass); ?>>
    <div<?= html_attr('class', $containerClass); ?>>
        <div class="row">
            <div class="col-lg-12">
                <div class="nav-and-btn-wrapper">
                    <div class="nav-area">
                        <nav>
                            <ul class="parent-nav">
                                <?php foreach ($menuItems as $item): ?>
                                    <?php
                                    $label = $item['label'] ?? '';
                                    $href = $item['href'] ?? '#';
                                    $classes = $item['class'] ?? 'parent';
                                    $submenu = $item['submenu'] ?? [];
                                    $megamenu = $item['megamenu'] ?? null;
                                    ?>
                                    <li class="<?= htmlspecialchars($classes, ENT_QUOTES, 'UTF-8'); ?>">
                                        <a class="nav-link" href="<?= htmlspecialchars($href, ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?></a>
                                        <?php if ($megamenu): ?>
                                            <?php $renderMegamenu($megamenu); ?>
                                        <?php elseif ($submenu): ?>
                                            <?php $renderSubmenu($submenu); ?>
                                        <?php endif; ?>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </nav>
                    </div>
                    <div class="right-btn-area">
                        <?php if (!empty($navCta['pill']['label'])): ?>
                            <a href="<?= htmlspecialchars($navCta['pill']['href'] ?? '#', ENT_QUOTES, 'UTF-8'); ?>" class="<?= htmlspecialchars($navCta['pill']['class'] ?? 'btn-narrow', ENT_QUOTES, 'UTF-8'); ?>">
                                <?= htmlspecialchars($navCta['pill']['label'], ENT_QUOTES, 'UTF-8'); ?>
                            </a>
                        <?php endif; ?>
                        <?php if (!empty($navCta['button']['label'])): ?>
                            <a href="<?= htmlspecialchars($navCta['button']['href'] ?? '#', ENT_QUOTES, 'UTF-8'); ?>" class="<?= htmlspecialchars($navCta['button']['class'] ?? 'rts-btn btn-primary', ENT_QUOTES, 'UTF-8'); ?>">
                                <?= htmlspecialchars($navCta['button']['label'], ENT_QUOTES, 'UTF-8'); ?>
                                <?php if (!empty($navCta['button']['badge'])): ?>
                                    <span><?= htmlspecialchars($navCta['button']['badge'], ENT_QUOTES, 'UTF-8'); ?></span>
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
