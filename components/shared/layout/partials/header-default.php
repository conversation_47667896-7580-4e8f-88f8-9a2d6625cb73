<?php $headerTopLinks = $header['header_top_links']; ?>
<div class="rts-header-one-area-one">
    <div class="header-top-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="bwtween-area-header-top">
                        <div class="discount-area">
                            <p class="disc"><?= htmlspecialchars($header['discount_message'], ENT_QUOTES, 'UTF-8'); ?></p>
                            <div class="countdown">
                                <div class="countDown"><?= htmlspecialchars($header['countdown_target'], ENT_QUOTES, 'UTF-8'); ?></div>
                            </div>
                        </div>
                        <div class="contact-number-area">
                            <p>Need help? Call Us:
                                <a href="<?= htmlspecialchars($header['support_phone_href'], ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($header['support_phone_display'], ENT_QUOTES, 'UTF-8'); ?></a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="search-header-area-main">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="logo-search-category-wrapper">
                        <a href="index.php" class="logo-area">
                            <img src="<?= htmlspecialchars($header['primary_logo'], ENT_QUOTES, 'UTF-8'); ?>" alt="logo-main" class="logo">
                        </a>
                        <div class="category-search-wrapper">
                            <div class="category-btn category-hover-header">
                                <img class="parent" src="assets/images/icons/bar-1.svg" alt="icons">
                                <span>Categories</span>
                                <ul class="category-sub-menu" id="category-active-four">
                                    <?php render_category_menu($header['categories'], [
                                        'item_link_class' => 'menu-item',
                                        'submenu_class' => 'submenu mm-collapse',
                                        'submenu_link_class' => 'mobile-menu-link',
                                    ]); ?>
                                </ul>
                            </div>
                            <form action="#" class="search-header">
                                <input type="text" placeholder="<?= htmlspecialchars($header['search_placeholder'], ENT_QUOTES, 'UTF-8'); ?>" required>
                                <?php render_button('search'); ?>
                            </form>
                        </div>
                        <div class="actions-area">
                            <div class="search-btn" id="searchs">
                                <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15.75 14.7188L11.5625 10.5312C12.4688 9.4375 12.9688 8.03125 12.9688 6.5C12.9688 2.9375 10.0312 0 6.46875 0C2.875 0 0 2.9375 0 6.5C0 10.0938 2.90625 13 6.46875 13C7.96875 13 9.375 12.5 10.5 11.5938L14.6875 15.7812C14.8438 15.9375 15.0312 16 15.25 16C15.4375 16 15.625 15.9375 15.75 15.7812C16.0625 15.5 16.0625 15.0312 15.75 14.7188ZM1.5 6.5C1.5 3.75 3.71875 1.5 6.5 1.5C9.25 1.5 11.5 3.75 11.5 6.5C11.5 9.28125 9.25 11.5 6.5 11.5C3.71875 11.5 1.5 9.28125 1.5 6.5Z" fill="#1F1F25"></path>
                                </svg>
                            </div>
                            <div class="menu-btn" id="menu-btn">
                                <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect y="14" width="20" height="2" fill="#1F1F25"></rect>
                                    <rect y="7" width="20" height="2" fill="#1F1F25"></rect>
                                    <rect width="20" height="2" fill="#1F1F25"></rect>
                                </svg>
                            </div>
                        </div>
                        <div class="accont-wishlist-cart-area-header">
                            <a href="account.php" class="btn-border-only account">
                                <i class="fa-light fa-user"></i>
                                <span>Account</span>
                            </a>
                            <a href="wishlist.php" class="btn-border-only wishlist">
                                <i class="fa-regular fa-heart"></i>
                                <span class="text">Wishlist</span>
                                <span class="number">2</span>
                            </a>
                            <div class="btn-border-only cart category-hover-header">
                                <i class="fa-sharp fa-regular fa-paper-plane"></i>
                                <span class="text">My Cart</span>
                                <span class="number">2</span>
                                <div class="category-sub-menu card-number-show">
                                    <h5 class="shopping-cart-number">Shopping Cart (<?= count($header['cart_items']); ?>)</h5>
                                    <?php foreach ($header['cart_items'] as $index => $cartItem): ?>
                                        <?php
                                        $itemClass = 'cart-item-1' . ($index === 0 ? ' border-top' : '');
                                        render_cart_item(array_merge($cartItem, ['class' => $itemClass]));
                                        ?>
                                    <?php endforeach; ?>
                                    <div class="sub-total-cart-balance">
                                        <div class="bottom-content-deals mt--10">
                                            <div class="top">
                                                <span>Sub Total:</span>
                                                <span class="number-c">$108.00</span>
                                            </div>
                                            <div class="single-progress-area-incard">
                                                <div class="progress">
                                                    <div class="progress-bar wow fadeInLeft" role="progressbar" style="width: 80%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                            </div>
                                            <p>Spend More <span>$125.00</span> to reach <span>Free Shipping</span></p>
                                        </div>
                                        <div class="button-wrapper d-flex align-items-center justify-content-between">
                                            <?php render_button_element([
                                                'text' => 'View Cart',
                                                'href' => 'cart.php',
                                                'class' => 'rts-btn btn-primary',
                                            ]); ?>
                                            <?php render_button_element([
                                                'text' => 'CheckOut',
                                                'href' => 'checkout.php',
                                                'class' => 'rts-btn btn-primary border-only',
                                            ]); ?>
                                        </div>
                                    </div>
                                </div>
                                <a href="cart.php" class="over_link"></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php render_partial(__DIR__ . '/nav-primary.php', ['header' => $header]); ?>
</div>
<?php render_partial(__DIR__ . '/header-mobile.php', ['header' => $header]); ?>
