<?php

if (!function_exists('build_footer_data')) {
    function build_footer_data(array $overrides = []): array
    {
        $logos = site_config('brand.logos', []);

        $defaults = [
            'logo' => $logos['secondary'] ?? 'assets/images/logo/logo-02.svg',
            'cta_phone_label' => site_config('contact.cta_phone_label', '(+2) 580 123 456 789'),
            'cta_phone_href' => site_config('contact.cta_phone_href', 'tel:(+2)580123456789'),
            'cta_email_label' => site_config('contact.cta_email_label', '<EMAIL>'),
            'cta_email_href' => site_config('contact.cta_email_href', 'mailto:<EMAIL>'),
        ];

        return array_replace_recursive($defaults, $overrides);
    }
}

if (!function_exists('render_layout_footer')) {
    /**
     * Renders the global site footer.
     */
    function render_layout_footer(array $options = []): void
    {
        $data = build_footer_data($options);
        render_partial(__DIR__ . '/partials/footer-default.php', ['footer' => $data]);
    }
}
