<?php

if (!function_exists('build_header_data')) {
    function build_header_data(array $overrides = []): array
    {
        $logos = site_config('brand.logos', []);

        $defaults = [
            'discount_message' => site_config('contact.discount_message', ''),
            'countdown_target' => site_config('contact.countdown_target', ''),
            'support_phone_display' => site_config('contact.support_phone_display', ''),
            'support_phone_href' => site_config('contact.support_phone_href', '#'),
            'header_top_links' => site_config('navigation.header_top_links', []),
            'welcome_message' => site_config('navigation.welcome_message', ''),
            'mid_left_links' => site_config('navigation.mid_left_links', []),
            'language' => site_config('navigation.language', ['current' => 'English', 'options' => []]),
            'currency' => site_config('navigation.currency', ['current' => 'USD', 'options' => []]),
            'mid_right_links' => site_config('navigation.mid_right_links', []),
            'main_menu' => site_config('navigation.main_menu', []),
            'nav_cta' => site_config('navigation.nav_cta', []),
            'categories' => site_config('navigation.categories', []),
            'search_placeholder' => site_config('search.placeholder', 'Search...'),
            'mobile_contact' => site_config('navigation.mobile_contact', []),
            'primary_logo' => $logos['primary'] ?? 'assets/images/logo/logo-01.svg',
            'secondary_logo' => $logos['secondary'] ?? 'assets/images/logo/logo-02.svg',
            'cart_items' => [
                [
                    'image' => ['src' => 'assets/images/shop/cart-1.png', 'alt' => 'cart item 1'],
                    'title' => "Foster Farms Breast Nuggets Shaped Chicken",
                    'href' => 'shop-details.php',
                    'quantity' => 1,
                    'price' => '$36.00',
                ],
                [
                    'image' => ['src' => 'assets/images/shop/05.png', 'alt' => 'cart item 2'],
                    'title' => "Foster Farms Breast Nuggets Shaped Chicken",
                    'href' => 'shop-details.php',
                    'quantity' => 1,
                    'price' => '$36.00',
                ],
                [
                    'image' => ['src' => 'assets/images/shop/04.png', 'alt' => 'cart item 3'],
                    'title' => "Foster Farms Breast Nuggets Shaped Chicken",
                    'href' => 'shop-details.php',
                    'quantity' => 1,
                    'price' => '$36.00',
                ],
            ],
        ];

        return array_replace_recursive($defaults, $overrides);
    }
}

if (!function_exists('render_layout_header')) {
    /**
     * Renders the primary site header. Variants can be introduced via options.
     */
    function render_layout_header(array $options = []): void
    {
        $dataOverrides = $options;
        unset($dataOverrides['variant'], $dataOverrides['view']);
        $data = build_header_data($dataOverrides);
        $variant = $options['variant'] ?? 'default';
        $viewData = is_array($options['view'] ?? null) ? $options['view'] : [];
        $partial = __DIR__ . '/partials/header-' . $variant . '.php';
        if (!file_exists($partial)) {
            $partial = __DIR__ . '/partials/header-default.php';
        }

        $viewData['header'] = $data;
        render_partial($partial, $viewData);
    }
}
