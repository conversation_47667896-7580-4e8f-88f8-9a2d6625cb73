<?php

/**
 * Enhanced File Upload Service
 * 
 * Provides robust file upload functionality with multiple unique naming strategies,
 * collision handling, and comprehensive validation.
 */
class FileUploadService
{
    public const STRATEGY_TIMESTAMP = 'timestamp';
    public const STRATEGY_UUID = 'uuid';
    public const STRATEGY_HASH = 'hash';
    public const STRATEGY_HYBRID = 'hybrid';

    private string $projectRoot;
    private array $config;

    /**
     * @var array<int, array{disk: string, absolute: string, public: ?string}>
     */
    private array $uploadLocations;

    public function __construct(array $uploadLocations, array $config = [])
    {
        $this->projectRoot = dirname(__DIR__, 2);
        $this->uploadLocations = $uploadLocations;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Upload a file with unique naming strategy
     * 
     * @param array{tmp_name: string, name?: string, size?: int} $fileData
     * @param string $namingStrategy
     * @param string $prefix
     * @return array{success: bool, path?: string, filename?: string, error?: string}
     */
    public function uploadFile(array $fileData, string $namingStrategy = self::STRATEGY_HYBRID, string $prefix = 'file'): array
    {
        if (!$this->validateFileData($fileData)) {
            return [
                'success' => false,
                'error' => 'Invalid file data provided.'
            ];
        }

        $extension = $this->extractFileExtension($fileData);
        if (!$extension) {
            return [
                'success' => false,
                'error' => 'Unable to determine file extension.'
            ];
        }

        $filename = $this->generateUniqueFilename($fileData, $namingStrategy, $prefix, $extension);
        if (!$filename) {
            return [
                'success' => false,
                'error' => 'Unable to generate a unique filename after maximum attempts.'
            ];
        }

        return $this->storeFile($fileData['tmp_name'], $filename, $prefix);
    }

    /**
     * Generate unique filename using specified strategy
     */
    private function generateUniqueFilename(array $fileData, string $strategy, string $prefix, string $extension): ?string
    {
        $maxAttempts = $this->config['max_naming_attempts'];
        
        for ($attempt = 0; $attempt < $maxAttempts; $attempt++) {
            $filename = $this->createFilename($fileData, $strategy, $prefix, $extension, $attempt);
            
            if (!$this->filenameExists($filename)) {
                return $filename;
            }
        }

        // Final fallback with guaranteed uniqueness
        return $this->createFallbackFilename($prefix, $extension);
    }

    /**
     * Create filename based on strategy
     */
    private function createFilename(array $fileData, string $strategy, string $prefix, string $extension, int $attempt): string
    {
        switch ($strategy) {
            case self::STRATEGY_UUID:
                return $this->createUuidFilename($prefix, $extension, $attempt);
                
            case self::STRATEGY_HASH:
                return $this->createHashFilename($fileData, $prefix, $extension, $attempt);
                
            case self::STRATEGY_TIMESTAMP:
                return $this->createTimestampFilename($prefix, $extension, $attempt);
                
            case self::STRATEGY_HYBRID:
            default:
                return $this->createHybridFilename($fileData, $prefix, $extension, $attempt);
        }
    }

    /**
     * Create UUID-based filename
     */
    private function createUuidFilename(string $prefix, string $extension, int $attempt): string
    {
        $uuid = $this->generateUuid();
        $suffix = $attempt > 0 ? "-{$attempt}" : '';
        return sprintf('%s-%s%s.%s', $prefix, $uuid, $suffix, $extension);
    }

    /**
     * Create hash-based filename using file content
     */
    private function createHashFilename(array $fileData, string $prefix, string $extension, int $attempt): string
    {
        $hash = $this->generateFileHash($fileData['tmp_name']);
        $suffix = $attempt > 0 ? "-{$attempt}" : '';
        return sprintf('%s-%s%s.%s', $prefix, $hash, $suffix, $extension);
    }

    /**
     * Create timestamp-based filename with microsecond precision
     */
    private function createTimestampFilename(string $prefix, string $extension, int $attempt): string
    {
        $timestamp = new DateTimeImmutable('now', new DateTimeZone(date_default_timezone_get()));
        $timePart = $timestamp->format('Ymd-His-u'); // Include microseconds
        $suffix = $attempt > 0 ? "-{$attempt}" : '';
        return sprintf('%s-%s%s.%s', $prefix, $timePart, $suffix, $extension);
    }

    /**
     * Create hybrid filename combining timestamp and random elements
     */
    private function createHybridFilename(array $fileData, string $prefix, string $extension, int $attempt): string
    {
        $timestamp = new DateTimeImmutable('now', new DateTimeZone(date_default_timezone_get()));
        $timePart = $timestamp->format('Ymd-His');
        $randomPart = bin2hex(random_bytes(3)); // 6 character random string
        $suffix = $attempt > 0 ? "-{$attempt}" : '';
        return sprintf('%s-%s-%s%s.%s', $prefix, $timePart, $randomPart, $suffix, $extension);
    }

    /**
     * Create guaranteed unique fallback filename
     */
    private function createFallbackFilename(string $prefix, string $extension): string
    {
        $timestamp = microtime(true);
        $random = bin2hex(random_bytes(8));
        $pid = getmypid();
        return sprintf('%s-fallback-%s-%s-%s.%s', $prefix, $timestamp, $random, $pid, $extension);
    }

    /**
     * Generate UUID v4
     */
    private function generateUuid(): string
    {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // Version 4
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // Variant bits
        
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * Generate file content hash
     */
    private function generateFileHash(string $filePath): string
    {
        if (!is_readable($filePath)) {
            return bin2hex(random_bytes(16));
        }
        
        return substr(hash_file('sha256', $filePath), 0, 16);
    }

    /**
     * Check if filename exists in any upload location
     */
    private function filenameExists(string $filename): bool
    {
        foreach ($this->uploadLocations as $location) {
            $path = rtrim($location['absolute'], "/\\") . '/' . $filename;
            if (is_file($path)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Store file in upload locations
     * 
     * @return array{success: bool, path?: string, filename?: string, error?: string}
     */
    private function storeFile(string $tmpPath, string $filename, string $prefix): array
    {
        foreach ($this->uploadLocations as $location) {
            $absoluteDirectory = $location['absolute'];

            if (!$this->ensureWritableDirectory($absoluteDirectory)) {
                continue;
            }

            $destination = rtrim($absoluteDirectory, "/\\") . '/' . $filename;

            if (!$this->moveUploadedFile($tmpPath, $destination)) {
                continue;
            }

            $publicPath = $this->buildPublicPath($location, $filename, $prefix);
            if ($publicPath === null) {
                @unlink($destination);
                continue;
            }

            return [
                'success' => true,
                'path' => $publicPath,
                'filename' => $filename
            ];
        }

        return [
            'success' => false,
            'error' => 'Unable to store file in any upload location. Please check directory permissions.'
        ];
    }

    /**
     * Validate file data structure
     */
    private function validateFileData(array $fileData): bool
    {
        return isset($fileData['tmp_name']) &&
               is_string($fileData['tmp_name']) &&
               (is_uploaded_file($fileData['tmp_name']) || is_file($fileData['tmp_name'])); // Allow regular files for testing
    }

    /**
     * Extract file extension from file data
     */
    private function extractFileExtension(array $fileData): ?string
    {
        // Try to get extension from original filename
        if (!empty($fileData['name'])) {
            $extension = strtolower(pathinfo($fileData['name'], PATHINFO_EXTENSION));
            if ($extension) {
                return $extension;
            }
        }

        // Fallback to MIME type detection
        if (function_exists('mime_content_type')) {
            $mimeType = mime_content_type($fileData['tmp_name']);
            return $this->getExtensionFromMimeType($mimeType);
        }

        return null;
    }

    /**
     * Get file extension from MIME type
     */
    private function getExtensionFromMimeType(?string $mimeType): ?string
    {
        $mimeMap = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/svg+xml' => 'svg',
        ];

        return $mimeMap[$mimeType] ?? null;
    }

    /**
     * Ensure directory exists and is writable
     */
    private function ensureWritableDirectory(string $directory): bool
    {
        if (!is_dir($directory)) {
            if (!@mkdir($directory, 0755, true)) {
                return false;
            }
        }

        return is_writable($directory);
    }

    /**
     * Move uploaded file with fallback
     */
    private function moveUploadedFile(string $tmpName, string $destination): bool
    {
        if (@move_uploaded_file($tmpName, $destination)) {
            return true;
        }

        // Fallback for environments where move_uploaded_file is restricted or for testing
        if (@copy($tmpName, $destination)) {
            return true;
        }

        return @rename($tmpName, $destination);
    }

    /**
     * Build public path for file access
     */
    private function buildPublicPath(array $location, string $filename, string $prefix): ?string
    {
        if (!$this->isValidStoredFilename($filename)) {
            return null;
        }

        if ($location['public'] !== null) {
            return rtrim($location['public'], "/\\") . '/' . $filename;
        }

        $scriptMap = [
            'category-image' => 'serve-category-image.php',
            'product-image' => 'serve-product-image.php',
        ];

        $script = $scriptMap[$prefix] ?? null;
        if ($script === null) {
            return null;
        }

        return $script . '?disk=' . rawurlencode($location['disk']) . '&file=' . rawurlencode($filename);
    }

    /**
     * Validate stored filename format
     */
    private function isValidStoredFilename(string $filename): bool
    {
        // Allow alphanumeric, hyphens, underscores, and dots
        return preg_match('/^[a-zA-Z0-9_-]+\.[a-zA-Z0-9]+$/', $filename) === 1;
    }

    /**
     * Get default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'max_naming_attempts' => 10,
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'max_file_size' => 2 * 1024 * 1024, // 2MB
        ];
    }

    /**
     * Slugify filename for safe storage
     */
    public function slugifyFilename(string $originalName): string
    {
        $name = trim(pathinfo($originalName, PATHINFO_FILENAME) ?: '');
        if ($name === '') {
            return '';
        }

        $transliterated = iconv('UTF-8', 'ASCII//TRANSLIT', $name);
        if (!is_string($transliterated)) {
            $transliterated = $name;
        }

        $sanitised = preg_replace('/[^A-Za-z0-9]+/', '-', strtolower($transliterated));
        return trim((string) $sanitised, '-');
    }
}
