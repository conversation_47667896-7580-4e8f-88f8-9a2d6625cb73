<?php

// Aggregates shared components for simplified importing.

$sharedComponentFiles = [
    __DIR__ . '/atoms/Icon.php',
    __DIR__ . '/atoms/Button.php',
    __DIR__ . '/atoms/Badge.php',
    __DIR__ . '/atoms/Price.php',
    __DIR__ . '/atoms/QuantityStepper.php',
    __DIR__ . '/composite/ProductCard.php',
    __DIR__ . '/composite/FeatureCard.php',
    __DIR__ . '/composite/CategoryTile.php',
    __DIR__ . '/composite/CartItem.php',
    __DIR__ . '/composite/CompareProduct.php',
    __DIR__ . '/layout/Header.php',
    __DIR__ . '/layout/Footer.php',
    __DIR__ . '/layout/SearchOverlay.php',
    __DIR__ . '/layout/Modal.php',
    __DIR__ . '/layout/Document.php',
];

foreach ($sharedComponentFiles as $componentFile) {
    if (file_exists($componentFile)) {
        require_once $componentFile;
    }
}
