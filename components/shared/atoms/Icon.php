<?php

if (!function_exists('render_icon')) {
    /**
     * Outputs an icon element, defaulting to an <i> tag.
     */
    function render_icon(string $class, array $options = []): void
    {
        $tag = $options['tag'] ?? 'i';
        $attrs = $options['attributes'] ?? [];
        $combinedClass = trim($class . ' ' . ($options['class'] ?? ''));
        $attrs['class'] = $combinedClass;

        $attrString = '';
        foreach ($attrs as $name => $value) {
            $attrString .= html_attr($name, (string) $value);
        }

        echo sprintf('<%1$s%2$s></%1$s>', $tag, $attrString);
    }
}
