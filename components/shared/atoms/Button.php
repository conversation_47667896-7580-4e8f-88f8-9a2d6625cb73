<?php

if (!function_exists('normalize_button_props')) {
    /**
     * Normalises button configuration array with sensible defaults.
     */
    function normalize_button_props(array $props): array
    {
        $defaults = [
            'tag' => 'a',
            'href' => '#',
            'class' => 'rts-btn btn-primary',
            'text' => 'Button',
            'icons' => [],
            'text_wrapper' => 'span',
            'attributes' => [],
        ];

        $normalized = array_merge($defaults, $props);
        $normalized['class'] = trim($normalized['class']);

        return $normalized;
    }
}

if (!function_exists('render_button_element')) {
    /**
     * Renders a configurable button-like element.
     */
    function render_button_element(array $props): void
    {
        $button = normalize_button_props($props);
        $tag = $button['tag'];
        $isAnchor = $tag === 'a';
        $attributes = $button['attributes'];
        $class = $button['class'];
        $textWrapper = $button['text_wrapper'] ?? 'span';

        $attrString = html_attr('class', $class);
        if ($isAnchor) {
            $attrString .= html_attr('href', $button['href']);
        }
        foreach ($attributes as $name => $value) {
            $attrString .= html_attr($name, $value);
        }

        echo '<' . $tag . $attrString . '>';
        echo '<' . $textWrapper . ' class="btn-text">' . htmlspecialchars($button['text'], ENT_QUOTES, 'UTF-8') . '</' . $textWrapper . '>';
        foreach ($button['icons'] as $icon) {
            $wrapper = $icon['wrapper'] ?? 'span';
            $wrapperClass = $icon['wrapper_class'] ?? 'arrow-icon';
            $iconClass = $icon['class'] ?? '';
            $iconTag = $icon['tag'] ?? 'i';

            echo '<' . $wrapper . html_attr('class', $wrapperClass) . '>';
            render_icon($iconClass, ['tag' => $iconTag, 'class' => '', 'attributes' => $icon['attributes'] ?? []]);
            echo '</' . $wrapper . '>';
        }
        echo '</' . $tag . '>';
    }
}

if (!function_exists('render_button')) {
    /**
     * Convenience wrapper that pulls button presets from the site config.
     */
    function render_button(string $key, array $overrides = []): void
    {
        $defaults = site_config('buttons.' . $key, []);
        $props = array_replace_recursive($defaults, $overrides);
        render_button_element($props);
    }
}
