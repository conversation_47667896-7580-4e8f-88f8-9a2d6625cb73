<?php

if (!function_exists('render_price')) {
    /**
     * Renders the current/previous price block used across product cards.
     */
    function render_price(array $price): void
    {
        $current = $price['current'] ?? null;
        $previous = $price['previous'] ?? null;
        $currency = $price['currency'] ?? '$';
        $wrapperClass = $price['class'] ?? 'price-area';

        if ($current === null && $previous === null) {
            return;
        }

        echo '<div' . html_attr('class', $wrapperClass) . '>';
        if ($current !== null) {
            echo '<span class="current">' . htmlspecialchars($currency . $current, ENT_QUOTES, 'UTF-8') . '</span>';
        }
        if ($previous !== null) {
            echo '<div class="previous">' . htmlspecialchars($currency . $previous, ENT_QUOTES, 'UTF-8') . '</div>';
        }
        echo '</div>';
    }
}
