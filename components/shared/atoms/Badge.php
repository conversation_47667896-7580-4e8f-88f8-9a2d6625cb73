<?php

if (!function_exists('render_badge')) {
    /**
     * Renders a simple badge/label element with optional icon.
     */
    function render_badge(array $props): void
    {
        $text = $props['text'] ?? '';
        $tag = $props['tag'] ?? 'span';
        $class = $props['class'] ?? 'badge';
        $icon = $props['icon'] ?? null;
        $additional = $props['attributes'] ?? [];

        $attrString = html_attr('class', $class);
        foreach ($additional as $name => $value) {
            $attrString .= html_attr($name, $value);
        }

        echo '<' . $tag . $attrString . '>';
        if ($icon) {
            render_icon($icon['class'] ?? '', $icon);
        }
        echo htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
        echo '</' . $tag . '>';
    }
}
