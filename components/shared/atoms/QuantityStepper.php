<?php

if (!function_exists('render_quantity_stepper')) {
    /**
     * Displays the quantity selector control used inside product cards.
     */
    function render_quantity_stepper(array $props = []): void
    {
        $value = $props['value'] ?? 1;
        $inputName = $props['name'] ?? 'quantity';
        $wrapperClass = $props['class'] ?? 'quantity-edit';

        echo '<div' . html_attr('class', $wrapperClass) . '>';
        echo '<input type="text" class="input" value="' . htmlspecialchars((string) $value, ENT_QUOTES, 'UTF-8') . '" name="' . htmlspecialchars($inputName, ENT_QUOTES, 'UTF-8') . '">';
        echo '<div class="button-wrapper-action">';
        echo '<button class="button"><i class="fa-regular fa-chevron-down"></i></button>';
        echo '<button class="button plus">+<i class="fa-regular fa-chevron-up"></i></button>';
        echo '</div>';
        echo '</div>';
    }
}
