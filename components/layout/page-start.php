<?php
require_once __DIR__ . '/../utils.php';

$pageMetaDefaults = site_config('meta', []);
$pageMetaOverrides = (isset($pageMeta) && is_array($pageMeta)) ? $pageMeta : [];

$pageMetaResolved = [
    'title' => $pageTitle ?? ($pageMetaOverrides['title'] ?? ($pageMetaDefaults['title'] ?? 'RC Furnishing')),
    'description' => $pageDescription ?? ($pageMetaOverrides['description'] ?? ($pageMetaDefaults['description'] ?? '')),
    'keywords' => $pageKeywords ?? ($pageMetaOverrides['keywords'] ?? ($pageMetaDefaults['keywords'] ?? '')),
    'body_class' => $bodyClass ?? null,
];

render_document_open($pageMetaResolved);
$headerOptions = isset($layoutHeaderOptions) && is_array($layoutHeaderOptions) ? $layoutHeaderOptions : [];
render_layout_header($headerOptions);
