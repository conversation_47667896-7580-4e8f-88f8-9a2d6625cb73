<?php

namespace RCF\Database;

/**
 * Simple Environment Variable Loader
 * 
 * Loads environment variables from .env file
 */
class EnvLoader
{
    /**
     * Load environment variables from .env file
     */
    public static function load(string $path = null): void
    {
        $envFile = $path ?? __DIR__ . '/../../.env';
        
        if (!file_exists($envFile)) {
            return; // .env file is optional
        }
        
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // Skip comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (preg_match('/^(["\'])(.*)\\1$/', $value, $matches)) {
                    $value = $matches[2];
                }
                
                // Set environment variable if not already set
                if (!isset($_ENV[$key])) {
                    $_ENV[$key] = $value;
                    putenv("{$key}={$value}");
                }
            }
        }
    }
}
