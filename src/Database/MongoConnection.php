<?php

namespace RCF\Database;

use MongoDB\Client;
use MongoDB\Database;
use MongoDB\Collection;
use MongoDB\Exception\Exception as MongoException;
use RCF\Database\Logger;
use RCF\Database\ErrorHandler;
use RCF\Database\MockMongoDB;

/**
 * MongoDB Connection Service
 * 
 * Provides a singleton pattern for MongoDB connections with error handling,
 * logging, and connection pooling.
 */
class MongoConnection
{
    private static ?MongoConnection $instance = null;
    private ?Client $client = null;
    private ?Database $database = null;
    private ?MockMongoDB $mockDb = null;
    private array $config;
    private $logger = null;
    private bool $useMock = false;
    private int $lastConnectAttempt = 0;
    private int $retryIntervalSeconds = 30;
    
    /**
     * Private constructor to prevent direct instantiation
     */
    private function __construct()
    {
        $this->loadConfiguration();
        $this->initializeLogger();
    }
    
    /**
     * Get the singleton instance
     */
    public static function getInstance(): MongoConnection
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }
    
    /**
     * Load database configuration
     */
    private function loadConfiguration(): void
    {
        $configFile = __DIR__ . '/../../config/database.php';
        if (!file_exists($configFile)) {
            throw new \RuntimeException('Database configuration file not found');
        }
        
        $this->config = require $configFile;

        $retryInterval = $this->config['mongodb']['connection']['retry_interval'] ?? 30;
        $retryInterval = is_numeric($retryInterval) ? (int) $retryInterval : 30;
        $this->retryIntervalSeconds = $retryInterval > 0 ? $retryInterval : 30;
    }
    
    /**
     * Initialize logger for database operations
     */
    private function initializeLogger(): void
    {
        // Initialize the centralized logger
        Logger::init($this->config['logging']);

        // Keep reference for backward compatibility
        $this->logger = Logger::getLogger();
    }
    

    
    /**
     * Get MongoDB client instance
     */
    public function getClient(): Client
    {
        $this->ensureConnection();

        return $this->client;
    }
    
    /**
     * Get MongoDB database instance
     */
    public function getDatabase()
    {
        $this->ensureConnection();

        if ($this->useMock) {
            return $this->mockDb; // Return mock for compatibility
        }

        return $this->database;
    }
    
    /**
     * Get a specific collection (MongoDB or mock)
     */
    public function getCollection(string $collectionName)
    {
        $this->ensureConnection();

        if ($this->useMock) {
            return $this->mockDb->getCollection($collectionName);
        }

        return $this->database->selectCollection($collectionName);
    }
    
    /**
     * Establish MongoDB connection or initialize mock
     */
    private function connect(): void
    {
        $this->lastConnectAttempt = time();

        // Check if MongoDB extension is available
        if (!extension_loaded('mongodb')) {
            Logger::logConnection('mongodb_extension_missing');
            $this->initializeMock();
            return;
        }

        try {
            $mongoConfig = $this->config['mongodb'];
            $uri = $mongoConfig['connection']['uri'];
            $options = $mongoConfig['connection']['options'];

            Logger::logConnection('attempting', ['uri' => $uri]);

            $this->client = new Client($uri, $options);
            $this->database = $this->client->selectDatabase($mongoConfig['connection']['database']);

            // Test the connection
            $this->database->command(['ping' => 1]);

            Logger::logConnection('success');

            // Ensure we are no longer using the mock connection after a successful handshake
            $this->useMock = false;
            $this->mockDb = null;

        } catch (\Exception $e) {
            Logger::logConnection('failed', [
                'error' => $e->getMessage(),
                'code' => $e->getCode()
            ]);

            // Clear any partially initialised real connection handles
            $this->client = null;
            $this->database = null;

            // Fall back to mock implementation
            Logger::logConnection('falling_back_to_mock');
            $this->initializeMock();
        }
    }

    /**
     * Initialize mock MongoDB implementation
     */
    private function initializeMock(): void
    {
        $this->client = null;
        $this->database = null;
        $this->useMock = true;
        $mockDataDir = dirname(__DIR__, 2) . '/storage/mock_mongodb';
        $this->mockDb = new MockMongoDB($mockDataDir);
        Logger::logConnection('mock_initialized');
    }
    
    /**
     * Test database connection
     */
    public function testConnection(): bool
    {
        if ($this->useMock) {
            if ($this->shouldRetryRealConnection()) {
                $this->connect();
            }

            if ($this->useMock) {
                return true; // Mock is always "connected"
            }
        }

        try {
            if ($this->database === null) {
                $this->connect();
            }

            if ($this->database) {
                $this->database->command(['ping' => 1]);
            }
            return true;
        } catch (\Exception $e) {
            Logger::error('Connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }
    
    /**
     * Get collection name from configuration
     */
    public function getCollectionName(string $entity): string
    {
        $collections = $this->config['mongodb']['collections'];
        
        if (!isset($collections[$entity])) {
            throw new \InvalidArgumentException("Collection not configured for entity: {$entity}");
        }
        
        return $collections[$entity];
    }

    private function ensureConnection(): void
    {
        if ($this->useMock) {
            if ($this->shouldRetryRealConnection()) {
                $this->connect();
            }

            if ($this->useMock) {
                return;
            }
        }

        if ($this->client === null || ($this->database === null && !$this->useMock)) {
            $this->connect();
        }
    }

    private function shouldRetryRealConnection(): bool
    {
        if (!$this->useMock) {
            return false;
        }

        if ($this->retryIntervalSeconds <= 0) {
            return true;
        }

        $elapsed = time() - $this->lastConnectAttempt;
        return $elapsed >= $this->retryIntervalSeconds;
    }
    
    /**
     * Create database indexes
     */
    public function createIndexes(): void
    {
        $indexes = $this->config['mongodb']['indexes'] ?? [];

        foreach ($indexes as $collectionName => $collectionIndexes) {
            try {
                $collection = $this->getCollection($this->getCollectionName($collectionName));

                foreach ($collectionIndexes as $index) {
                    $collection->createIndex($index['key'], $index);
                    Logger::info('Created index', [
                        'collection' => $collectionName,
                        'index' => $index['key']
                    ]);
                }
            } catch (\Exception $e) {
                Logger::error('Failed to create index', [
                    'collection' => $collectionName,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
    

    
    /**
     * Get database configuration
     */
    public function getConfig(): array
    {
        return $this->config;
    }
    
    /**
     * Check if using mock implementation
     */
    public function isUsingMock(): bool
    {
        return $this->useMock;
    }

    /**
     * Close database connection
     */
    public function close(): void
    {
        $this->client = null;
        $this->database = null;
        $this->mockDb = null;
        Logger::logConnection('closed');
    }
    
    /**
     * Prevent cloning of the instance
     */
    private function __clone() {}
    
    /**
     * Prevent unserialization of the instance
     */
    public function __wakeup()
    {
        throw new \Exception("Cannot unserialize singleton");
    }
}
