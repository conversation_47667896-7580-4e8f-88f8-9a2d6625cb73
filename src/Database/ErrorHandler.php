<?php

namespace RCF\Database;

use MongoDB\Exception\Exception as MongoException;
use MongoDB\Exception\ConnectionException;
use MongoDB\Exception\AuthenticationException;
use MongoDB\Exception\RuntimeException;

/**
 * Database Error Handler
 * 
 * Handles and categorizes database errors
 */
class ErrorHandler
{
    /**
     * Handle MongoDB exceptions
     */
    public static function handle(\Exception $exception, string $operation = '', array $context = []): array
    {
        $errorInfo = [
            'type' => 'unknown',
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'severity' => 'error',
            'recoverable' => false,
            'suggestions' => [],
        ];
        
        // Categorize MongoDB exceptions
        if ($exception instanceof MongoException) {
            $errorInfo = self::handleMongoException($exception, $operation, $context);
        } else {
            $errorInfo = self::handleGenericException($exception, $operation, $context);
        }
        
        // Log the error
        Logger::logError($operation, $context['collection'] ?? 'unknown', $exception, $context);
        
        return $errorInfo;
    }
    
    /**
     * Handle MongoDB specific exceptions
     */
    private static function handleMongoException(MongoException $exception, string $operation, array $context): array
    {
        $errorInfo = [
            'type' => 'mongodb',
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'severity' => 'error',
            'recoverable' => false,
            'suggestions' => [],
        ];
        
        if ($exception instanceof ConnectionException) {
            $errorInfo['type'] = 'connection';
            $errorInfo['recoverable'] = true;
            $errorInfo['suggestions'] = [
                'Check if MongoDB server is running',
                'Verify connection string in configuration',
                'Check network connectivity',
                'Verify firewall settings',
            ];
        } elseif ($exception instanceof AuthenticationException) {
            $errorInfo['type'] = 'authentication';
            $errorInfo['suggestions'] = [
                'Check username and password',
                'Verify database permissions',
                'Check authentication database',
            ];
        } elseif ($exception instanceof RuntimeException) {
            $errorInfo['type'] = 'runtime';
            $errorInfo['recoverable'] = true;
            
            // Check for specific runtime errors
            $message = strtolower($exception->getMessage());
            if (strpos($message, 'duplicate key') !== false) {
                $errorInfo['type'] = 'duplicate_key';
                $errorInfo['severity'] = 'warning';
                $errorInfo['suggestions'] = [
                    'Check for unique constraint violations',
                    'Verify data before insertion',
                    'Use upsert operations if appropriate',
                ];
            } elseif (strpos($message, 'validation') !== false) {
                $errorInfo['type'] = 'validation';
                $errorInfo['severity'] = 'warning';
                $errorInfo['suggestions'] = [
                    'Check data format and types',
                    'Verify required fields are present',
                    'Validate data before database operations',
                ];
            } elseif (strpos($message, 'timeout') !== false) {
                $errorInfo['type'] = 'timeout';
                $errorInfo['recoverable'] = true;
                $errorInfo['suggestions'] = [
                    'Increase timeout settings',
                    'Optimize query performance',
                    'Check server load',
                    'Consider pagination for large datasets',
                ];
            }
        }
        
        return $errorInfo;
    }
    
    /**
     * Handle generic exceptions
     */
    private static function handleGenericException(\Exception $exception, string $operation, array $context): array
    {
        $errorInfo = [
            'type' => 'application',
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'severity' => 'error',
            'recoverable' => false,
            'suggestions' => [],
        ];
        
        // Check for specific exception types
        if ($exception instanceof \InvalidArgumentException) {
            $errorInfo['type'] = 'validation';
            $errorInfo['severity'] = 'warning';
            $errorInfo['recoverable'] = true;
            $errorInfo['suggestions'] = [
                'Check input parameters',
                'Validate data before processing',
                'Review function documentation',
            ];
        } elseif ($exception instanceof \RuntimeException) {
            $errorInfo['type'] = 'runtime';
            $errorInfo['recoverable'] = true;
            $errorInfo['suggestions'] = [
                'Check system resources',
                'Verify file permissions',
                'Review configuration settings',
            ];
        }
        
        return $errorInfo;
    }
    
    /**
     * Format error for user display
     */
    public static function formatUserMessage(array $errorInfo): string
    {
        switch ($errorInfo['type']) {
            case 'connection':
                return 'Database connection failed. Please try again later.';
            case 'authentication':
                return 'Database authentication failed. Please contact support.';
            case 'duplicate_key':
                return 'This record already exists. Please use different values.';
            case 'validation':
                return 'Invalid data provided. Please check your input.';
            case 'timeout':
                return 'Operation timed out. Please try again.';
            default:
                return 'An error occurred while processing your request.';
        }
    }
    
    /**
     * Check if error is recoverable
     */
    public static function isRecoverable(array $errorInfo): bool
    {
        return $errorInfo['recoverable'] ?? false;
    }
    
    /**
     * Get retry strategy for recoverable errors
     */
    public static function getRetryStrategy(array $errorInfo): array
    {
        if (!self::isRecoverable($errorInfo)) {
            return ['retry' => false];
        }
        
        switch ($errorInfo['type']) {
            case 'connection':
                return [
                    'retry' => true,
                    'max_attempts' => 3,
                    'delay' => 1, // seconds
                    'backoff' => 'exponential',
                ];
            case 'timeout':
                return [
                    'retry' => true,
                    'max_attempts' => 2,
                    'delay' => 2,
                    'backoff' => 'linear',
                ];
            case 'runtime':
                return [
                    'retry' => true,
                    'max_attempts' => 2,
                    'delay' => 0.5,
                    'backoff' => 'linear',
                ];
            default:
                return [
                    'retry' => true,
                    'max_attempts' => 1,
                    'delay' => 1,
                    'backoff' => 'none',
                ];
        }
    }
    
    /**
     * Log error summary for monitoring
     */
    public static function logErrorSummary(array $errorInfo, string $operation, array $context = []): void
    {
        $summary = [
            'error_type' => $errorInfo['type'],
            'severity' => $errorInfo['severity'],
            'recoverable' => $errorInfo['recoverable'],
            'operation' => $operation,
        ];
        
        Logger::warning('Database error summary', array_merge($summary, $context));
    }
}
