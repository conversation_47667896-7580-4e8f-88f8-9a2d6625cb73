<?php

namespace RCF\Database;

use RCF\Database\MongoConnection;
use MongoDB\Collection;

/**
 * Database Migration System
 * 
 * Handles database migrations and seeding
 */
class Migration
{
    private MongoConnection $connection;
    private $migrationsCollection;
    
    public function __construct()
    {
        $this->connection = MongoConnection::getInstance();
        $this->migrationsCollection = $this->connection->getCollection('migrations');
    }
    
    /**
     * Run all pending migrations
     */
    public function migrate(): array
    {
        $results = [];
        
        // Create indexes first
        $this->createIndexes();
        $results[] = 'Database indexes created';
        
        // Seed initial data
        $seedResults = $this->seedData();
        $results = array_merge($results, $seedResults);
        
        // Mark migration as completed
        $this->markMigrationCompleted('initial_setup');
        
        return $results;
    }
    
    /**
     * Create database indexes
     */
    private function createIndexes(): void
    {
        $this->connection->createIndexes();
    }
    
    /**
     * Seed initial data for core collections
     */
    private function seedData(): array
    {
        $results = [];
        
        // Seed categories from admin.json
        $categoryResults = $this->seedCategories();
        $results[] = "Categories seeded: {$categoryResults['success']} success, {$categoryResults['failed']} failed";
        
        if (!empty($categoryResults['errors'])) {
            $results = array_merge($results, $categoryResults['errors']);
        }
        
        return $results;
    }
    
    /**
     * Seed categories into MongoDB using built-in fixtures or legacy JSON data when available
     */
    private function seedCategories(): array
    {
        $categoryRepo = new \RCF\Repositories\CategoryRepository();
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];

        $seedCategories = $this->getSeedCategoryData();

        foreach ($seedCategories as $item) {
            try {
                if (empty($item['name']) || empty($item['slug'])) {
                    $results['errors'][] = 'Skipped category with missing name or slug.';
                    $results['failed']++;
                    continue;
                }

                // Skip categories that already exist
                if ($categoryRepo->findBySlug($item['slug'])) {
                    continue;
                }

                $categoryRepo->createCategory([
                    'name' => $item['name'],
                    'slug' => $item['slug'],
                    'description' => $item['description'] ?? '',
                    'status' => $item['status'] ?? 'active',
                ]);

                $results['success']++;

            } catch (\Exception $e) {
                $results['errors'][] = "Failed to create category '{$item['name']}': " . $e->getMessage();
                $results['failed']++;
            }
        }

        return $results;
    }

    /**
     * Retrieve fixture data for seeding categories
     */
    private function getSeedCategoryData(): array
    {
        $legacyPath = __DIR__ . '/../../storage/admin.json';

        if (file_exists($legacyPath)) {
            $legacyData = json_decode(file_get_contents($legacyPath), true);
            if (isset($legacyData['items']) && is_array($legacyData['items']) && !empty($legacyData['items'])) {
                return array_map(function (array $item) {
                    return [
                        'name' => $item['name'] ?? '',
                        'slug' => $item['slug'] ?? '',
                        'description' => $item['description'] ?? '',
                        'status' => $item['status'] ?? 'active',
                    ];
                }, $legacyData['items']);
            }
        }

        return [
            [
                'name' => 'Electronics & Gadgets',
                'slug' => 'electronics-gadgets',
                'description' => 'Electronic devices, gadgets, and tech accessories',
                'status' => 'active',
            ],
            [
                'name' => 'Furniture',
                'slug' => 'furniture',
                'description' => 'Home and office furniture',
                'status' => 'active',
            ],
            [
                'name' => 'Books',
                'slug' => 'books',
                'description' => 'Books and educational materials',
                'status' => 'active',
            ],
        ];
    }
    
    /**
     * Seed sample products
     */
    public function seedSampleProducts(): array
    {
        $productRepo = new \RCF\Repositories\ProductRepository();
        $categoryRepo = new \RCF\Repositories\CategoryRepository();
        
        // Get first category for sample products
        $categories = $categoryRepo->getActiveCategories();
        if (empty($categories)) {
            return ['success' => 0, 'failed' => 0, 'errors' => ['No categories found for products']];
        }
        
        $categoryId = $categories[0]['id'];
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];
        
        $sampleProducts = [
            [
                'name' => 'Nestle Cerelac Mixed Fruits & Wheat with Milk',
                'slug' => 'nestle-cerelac-mixed-fruits',
                'description' => 'Nutritious baby food with mixed fruits and wheat',
                'price' => 36.00,
                'sale_price' => 30.00,
                'stock_quantity' => 100,
                'category_id' => $categoryId,
                'featured' => true,
                'status' => 'active',
                'tags' => ['baby food', 'nutrition', 'fruits'],
                'images' => ['assets/images/grocery/01.jpg'],
            ],
            [
                'name' => 'Peysan Full Fat Fresh Cottage Cheese',
                'slug' => 'peysan-cottage-cheese',
                'description' => 'Fresh and creamy cottage cheese',
                'price' => 25.00,
                'stock_quantity' => 50,
                'category_id' => $categoryId,
                'featured' => false,
                'status' => 'active',
                'tags' => ['dairy', 'cheese', 'fresh'],
                'images' => ['assets/images/grocery/02.jpg'],
            ],
            [
                'name' => 'Tokyo Style Marshmallow',
                'slug' => 'tokyo-style-marshmallow',
                'description' => 'Soft and sweet marshmallows in Tokyo style',
                'price' => 15.00,
                'stock_quantity' => 200,
                'category_id' => $categoryId,
                'featured' => true,
                'status' => 'active',
                'tags' => ['sweets', 'marshmallow', 'tokyo'],
                'images' => ['assets/images/grocery/05.jpg'],
            ],
        ];
        
        foreach ($sampleProducts as $productData) {
            try {
                // Check if product already exists
                if ($productRepo->findBySlug($productData['slug'])) {
                    $results['errors'][] = "Product '{$productData['slug']}' already exists";
                    $results['failed']++;
                    continue;
                }
                
                $productRepo->createProduct($productData);
                $results['success']++;
                
            } catch (\Exception $e) {
                $results['errors'][] = "Failed to create product '{$productData['name']}': " . $e->getMessage();
                $results['failed']++;
            }
        }
        
        return $results;
    }
    
    /**
     * Check if migration has been completed
     */
    private function isMigrationCompleted(string $migrationName): bool
    {
        $migration = $this->migrationsCollection->findOne(['name' => $migrationName]);
        return $migration !== null;
    }
    
    /**
     * Mark migration as completed
     */
    private function markMigrationCompleted(string $migrationName): void
    {
        $this->migrationsCollection->insertOne([
            'name' => $migrationName,
            'completed_at' => new \DateTime(),
        ]);
    }
    
    /**
     * Reset database (for development/testing)
     */
    public function reset(): array
    {
        $results = [];
        
        try {
            $database = $this->connection->getDatabase();
            
            // Drop all collections
            $collections = ['categories', 'products', 'users', 'orders', 'migrations'];
            foreach ($collections as $collectionName) {
                $database->dropCollection($collectionName);
                $results[] = "Dropped collection: {$collectionName}";
            }
            
            $results[] = 'Database reset completed';
            
        } catch (\Exception $e) {
            $results[] = 'Database reset failed: ' . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Get migration status
     */
    public function getStatus(): array
    {
        try {
            $isConnected = $this->connection->testConnection();
            $usingMock = $this->connection->isUsingMock();

            $status = [
                'database_connected' => $isConnected,
                'using_mock' => $usingMock,
                'completed_migrations' => [],
                'collections' => [],
                'data_migrated' => false
            ];

            if ($isConnected) {
                // Get completed migrations
                $cursor = $this->migrationsCollection->find();

                if ($usingMock) {
                    // For mock, cursor is an array
                    foreach ($cursor as $migration) {
                        $status['completed_migrations'][] = [
                            'name' => $migration['name'],
                            'completed_at' => $migration['completed_at'] ?? date('Y-m-d H:i:s')
                        ];
                    }
                } else {
                    // For real MongoDB, cursor is a MongoDB cursor
                    foreach ($cursor as $migration) {
                        $completedAt = $migration['completed_at'] ?? null;

                        // Handle different date formats
                        if ($completedAt instanceof \MongoDB\BSON\UTCDateTime) {
                            $completedAtStr = $completedAt->toDateTime()->format('Y-m-d H:i:s');
                        } elseif ($completedAt instanceof \DateTime) {
                            $completedAtStr = $completedAt->format('Y-m-d H:i:s');
                        } elseif (is_string($completedAt)) {
                            $completedAtStr = $completedAt;
                        } else {
                            $completedAtStr = date('Y-m-d H:i:s');
                        }

                        $status['completed_migrations'][] = [
                            'name' => $migration['name'],
                            'completed_at' => $completedAtStr
                        ];
                    }
                }

                // Check collections
                foreach (['categories', 'products'] as $collectionName) {
                    $collection = $this->connection->getCollection($collectionName);
                    $count = $collection->countDocuments();
                    $status['collections'][$collectionName] = $count;

                    if ($count > 0) {
                        $status['data_migrated'] = true;
                    }
                }
            }

            return $status;

        } catch (\Exception $e) {
            return [
                'database_connected' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
