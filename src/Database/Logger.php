<?php

namespace RCF\Database;

use Monolog\Logger as MonologLogger;
use Monolog\Handler\StreamHandler;
use Monolog\Handler\RotatingFileHandler;
use Monolog\Formatter\LineFormatter;

/**
 * Database Logger
 * 
 * Centralized logging for database operations
 */
class Logger
{
    private static ?MonologLogger $logger = null;
    private static array $config = [];
    private static bool $fallbackInitialised = false;
    
    /**
     * Initialize logger with configuration
     */
    public static function init(array $config): void
    {
        self::$config = $config;
        self::$logger = new MonologLogger('rcf_database');
        self::$fallbackInitialised = false;

        if (!$config['enabled']) {
            return;
        }

        $logLevel = self::getLogLevel($config['level'] ?? 'info');
        $maxFiles = $config['max_files'] ?? 30;

        $formatter = self::createFormatter();

        $handler = self::createRotatingFileHandler($config['file'], $maxFiles, $logLevel, $formatter);

        if ($handler === null) {
            $fallbackFile = dirname(__DIR__, 2) . '/storage/logs/database.log';

            if ($fallbackFile !== $config['file']) {
                $handler = self::createRotatingFileHandler($fallbackFile, $maxFiles, $logLevel, $formatter);

                if ($handler !== null) {
                    self::$config['file'] = $fallbackFile;
                    error_log('[warning] Database logger fell back to ' . $fallbackFile);
                }
            }
        }

        if ($handler === null) {
            $fallbackHandler = new StreamHandler('php://stderr', $logLevel);
            $fallbackHandler->setFormatter($formatter);
            self::$logger->pushHandler($fallbackHandler);
            error_log('[warning] Falling back to stderr logging for database operations');
            return;
        }

        self::$logger->pushHandler($handler);
    }

    private static function createRotatingFileHandler(
        string $file,
        int $maxFiles,
        int $level,
        LineFormatter $formatter
    ): ?RotatingFileHandler {
        try {
            $logDir = dirname($file);

            if (!is_dir($logDir)) {
                if (!mkdir($logDir, 0775, true) && !is_dir($logDir)) {
                    throw new \RuntimeException('Unable to create log directory: ' . $logDir);
                }
            }

            if (!is_writable($logDir)) {
                throw new \RuntimeException('Log directory is not writable: ' . $logDir);
            }

            if (file_exists($file) && !is_writable($file)) {
                throw new \RuntimeException('Log file is not writable: ' . $file);
            }

            $rotatedFile = self::getRotatedLogFilePath($file);
            if ($rotatedFile !== null && file_exists($rotatedFile) && !is_writable($rotatedFile)) {
                throw new \RuntimeException('Log file is not writable: ' . $rotatedFile);
            }

            $handler = new RotatingFileHandler($file, $maxFiles, $level);
            $handler->setFormatter($formatter);

            return $handler;
        } catch (\Throwable $exception) {
            error_log('[error] Database logger initialisation failed for "' . $file . '": ' . $exception->getMessage());
            return null;
        }
    }

    private static function initialiseFallbackHandler(int $level): void
    {
        if (self::$fallbackInitialised) {
            return;
        }

        $formatter = self::createFormatter();
        $fallbackFile = dirname(__DIR__, 2) . '/storage/logs/database.log';
        $maxFiles = self::$config['max_files'] ?? 30;

        $handler = self::createRotatingFileHandler($fallbackFile, $maxFiles, $level, $formatter);

        if ($handler === null) {
            $handler = new StreamHandler('php://stderr', $level);
            $handler->setFormatter($formatter);
            error_log('[warning] Database logger fallback handler using stderr');
        } else {
            error_log('[warning] Database logger switching to fallback handler at ' . $fallbackFile);
        }

        if (self::$logger === null) {
            self::$logger = new MonologLogger('rcf_database');
        }

        self::$logger->setHandlers([$handler]);
        self::$fallbackInitialised = true;
    }

    private static function createFormatter(): LineFormatter
    {
        return new LineFormatter(
            "[%datetime%] %level_name%: %message% %context%\n",
            'Y-m-d H:i:s'
        );
    }

    private static function getRotatedLogFilePath(string $file): ?string
    {
        $info = pathinfo($file);
        if (!isset($info['dirname'], $info['filename'])) {
            return null;
        }

        $extension = isset($info['extension']) && $info['extension'] !== ''
            ? '.' . $info['extension']
            : '';

        return rtrim($info['dirname'], DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR .
            $info['filename'] . '-' . date('Y-m-d') . $extension;
    }
    
    /**
     * Get Monolog log level
     */
    private static function getLogLevel(string $level): int
    {
        $levels = [
            'debug' => MonologLogger::DEBUG,
            'info' => MonologLogger::INFO,
            'warning' => MonologLogger::WARNING,
            'error' => MonologLogger::ERROR,
        ];
        
        return $levels[$level] ?? MonologLogger::INFO;
    }
    
    /**
     * Log debug message
     */
    public static function debug(string $message, array $context = []): void
    {
        self::log('debug', $message, $context);
    }
    
    /**
     * Log info message
     */
    public static function info(string $message, array $context = []): void
    {
        self::log('info', $message, $context);
    }
    
    /**
     * Log warning message
     */
    public static function warning(string $message, array $context = []): void
    {
        self::log('warning', $message, $context);
    }
    
    /**
     * Log error message
     */
    public static function error(string $message, array $context = []): void
    {
        self::log('error', $message, $context);
    }
    
    /**
     * Log database operation
     */
    public static function logOperation(string $operation, string $collection, array $data = [], ?float $executionTime = null): void
    {
        $context = [
            'operation' => $operation,
            'collection' => $collection,
            'data_size' => count($data),
        ];
        
        if ($executionTime !== null) {
            $context['execution_time'] = round($executionTime * 1000, 2) . 'ms';
        }
        
        self::info("Database operation: {$operation} on {$collection}", $context);
    }
    
    /**
     * Log database error
     */
    public static function logError(string $operation, string $collection, \Exception $exception, array $context = []): void
    {
        $errorContext = array_merge($context, [
            'operation' => $operation,
            'collection' => $collection,
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
        ]);
        
        self::error("Database operation failed: {$operation} on {$collection}", $errorContext);
    }
    
    /**
     * Log connection events
     */
    public static function logConnection(string $event, array $context = []): void
    {
        self::info("MongoDB connection: {$event}", $context);
    }
    
    /**
     * Log performance metrics
     */
    public static function logPerformance(string $operation, float $executionTime, int $recordCount = 0): void
    {
        $context = [
            'execution_time' => round($executionTime * 1000, 2) . 'ms',
            'record_count' => $recordCount,
        ];
        
        if ($executionTime > 1.0) {
            self::warning("Slow database operation: {$operation}", $context);
        } else {
            self::debug("Database performance: {$operation}", $context);
        }
    }
    
    /**
     * Generic log method
     */
    private static function log(string $level, string $message, array $context = []): void
    {
        if (self::$logger === null) {
            // Fallback to error_log if logger not initialized
            error_log("[{$level}] {$message} " . json_encode($context));
            return;
        }
        
        if (!self::$config['enabled']) {
            return;
        }

        try {
            self::$logger->log($level, $message, $context);
        } catch (\Throwable $exception) {
            error_log('[error] Database logger write failed: ' . $exception->getMessage());

            $logLevel = self::getLogLevel(self::$config['level'] ?? 'info');
            self::initialiseFallbackHandler($logLevel);

            if (self::$logger !== null) {
                try {
                    self::$logger->log($level, $message, $context);
                } catch (\Throwable $fallbackException) {
                    error_log('[error] Database logger fallback write failed: ' . $fallbackException->getMessage());
                }
            }
        }
    }
    
    /**
     * Get logger instance
     */
    public static function getLogger(): ?MonologLogger
    {
        return self::$logger;
    }
}
