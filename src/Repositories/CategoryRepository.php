<?php

namespace RCF\Repositories;

use RCF\Database\MongoConnection;

/**
 * Category Repository
 * 
 * Handles category-specific database operations
 */
class CategoryRepository extends BaseRepository
{
    private bool $seedAttempted = false;

    public function __construct()
    {
        parent::__construct();
        $this->ensureSeedData();
    }
    /**
     * Get the collection name for categories
     */
    protected function getCollectionName(): string
    {
        return $this->connection->getCollectionName('categories');
    }
    
    /**
     * Validate category data
     */
    protected function validateData(array $data, bool $isUpdate = false): void
    {
        parent::validateData($data, $isUpdate);
        
        if (!$isUpdate) {
            // Required fields for new categories
            $requiredFields = ['name', 'slug'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    throw new \InvalidArgumentException("Field '{$field}' is required");
                }
            }
        }
        
        // Validate slug format
        if (isset($data['slug'])) {
            if (!preg_match('/^[a-z0-9-]+$/', $data['slug'])) {
                throw new \InvalidArgumentException('Slug must contain only lowercase letters, numbers, and hyphens');
            }
        }
        
        // Validate status
        if (isset($data['status'])) {
            $validStatuses = ['active', 'inactive', 'draft'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new \InvalidArgumentException('Status must be one of: ' . implode(', ', $validStatuses));
            }
        }
    }
    
    /**
     * Create a new category
     */
    public function createCategory(array $categoryData): ?string
    {
        // Set default values
        $categoryData['status'] = $categoryData['status'] ?? 'active';

        // Validate data first
        $this->validateData($categoryData);

        // Check if slug already exists
        if ($this->findBySlug($categoryData['slug'])) {
            throw new \InvalidArgumentException('Category with this slug already exists');
        }

        return $this->create($categoryData);
    }
    
    /**
     * Find category by slug
     */
    public function findBySlug(string $slug): ?array
    {
        return $this->findOne(['slug' => $slug]);
    }
    
    /**
     * Get all active categories
     */
    public function getActiveCategories(array $sort = ['name' => 1]): array
    {
        return $this->find(['status' => 'active'], ['sort' => $sort]);
    }
    
    /**
     * Get categories by status
     */
    public function getCategoriesByStatus(string $status, array $sort = ['created_at' => -1]): array
    {
        return $this->find(['status' => $status], ['sort' => $sort]);
    }
    
    /**
     * Search categories by name
     */
    public function searchByName(string $searchTerm, int $limit = 10): array
    {
        $filter = [
            'name' => ['$regex' => $searchTerm, '$options' => 'i']
        ];
        
        return $this->find($filter, ['limit' => $limit, 'sort' => ['name' => 1]]);
    }
    
    /**
     * Update category by slug
     */
    public function updateBySlug(string $slug, array $data): bool
    {
        $category = $this->findBySlug($slug);
        if (!$category) {
            return false;
        }
        
        // If updating slug, check for conflicts
        if (isset($data['slug']) && $data['slug'] !== $slug) {
            if ($this->findBySlug($data['slug'])) {
                throw new \InvalidArgumentException('Category with this slug already exists');
            }
        }
        
        return $this->updateById($category['id'], $data);
    }
    
    /**
     * Delete category by slug
     */
    public function deleteBySlug(string $slug): bool
    {
        $category = $this->findBySlug($slug);
        if (!$category) {
            return false;
        }
        
        return $this->deleteById($category['id']);
    }
    
    /**
     * Get category statistics
     */
    public function getStatistics(): array
    {
        $pipeline = [
            [
                '$group' => [
                    '_id' => '$status',
                    'count' => ['$sum' => 1]
                ]
            ]
        ];
        
        try {
            $cursor = $this->collection->aggregate($pipeline);
            $stats = [];
            
            foreach ($cursor as $result) {
                $stats[$result['_id']] = $result['count'];
            }
            
            // Ensure all statuses are represented
            $defaultStats = ['active' => 0, 'inactive' => 0, 'draft' => 0];
            return array_merge($defaultStats, $stats);
            
        } catch (\Exception $e) {
            $this->handleException('getStatistics', $e);
            return ['active' => 0, 'inactive' => 0, 'draft' => 0];
        }
    }
    
    /**
     * Bulk update category status
     */
    public function bulkUpdateStatus(array $categoryIds, string $status): int
    {
        $validStatuses = ['active', 'inactive', 'draft'];
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('Invalid status');
        }
        
        $objectIds = array_map(function($id) {
            return new \MongoDB\BSON\ObjectId($id);
        }, $categoryIds);
        
        return $this->updateMany(
            ['_id' => ['$in' => $objectIds]],
            ['status' => $status]
        );
    }
    
    /**
     * Get categories with pagination and filtering
     */
    public function getCategoriesWithFilters(array $filters = [], int $page = 1, int $limit = 10): array
    {
        $mongoFilter = [];
        
        // Apply filters
        if (!empty($filters['status'])) {
            $mongoFilter['status'] = $filters['status'];
        }
        
        if (!empty($filters['search'])) {
            $mongoFilter['$or'] = [
                ['name' => ['$regex' => $filters['search'], '$options' => 'i']],
                ['description' => ['$regex' => $filters['search'], '$options' => 'i']]
            ];
        }
        
        // Default sort by creation date (newest first)
        $sort = ['created_at' => -1];
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'name_asc':
                    $sort = ['name' => 1];
                    break;
                case 'name_desc':
                    $sort = ['name' => -1];
                    break;
                case 'created_asc':
                    $sort = ['created_at' => 1];
                    break;
                default:
                    $sort = ['created_at' => -1];
            }
        }
        
        return $this->paginate($mongoFilter, $page, $limit, $sort);
    }
    
    /**
     * Export categories to array format (for migration/backup)
     */
    public function exportCategories(): array
    {
        $categories = $this->find([], ['sort' => ['created_at' => 1]]);
        
        // Format for export
        return array_map(function($category) {
            return [
                'id' => $category['id'],
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'] ?? '',
                'status' => $category['status'],
                'created_at' => $category['created_at'],
                'updated_at' => $category['updated_at']
            ];
        }, $categories);
    }
    
    /**
     * Import categories from array data
     */
    public function importCategories(array $categoriesData): array
    {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];
        
        foreach ($categoriesData as $categoryData) {
            try {
                // Check if category already exists
                if (isset($categoryData['slug']) && $this->findBySlug($categoryData['slug'])) {
                    $results['errors'][] = "Category with slug '{$categoryData['slug']}' already exists";
                    $results['failed']++;
                    continue;
                }
                
                $this->createCategory($categoryData);
                $results['success']++;
                
            } catch (\Exception $e) {
                $results['errors'][] = $e->getMessage();
                $results['failed']++;
            }
        }
        
        return $results;
    }

    private function ensureSeedData(): void
    {
        if ($this->seedAttempted) {
            return;
        }

        $this->seedAttempted = true;

        try {
            if (
                method_exists($this->connection, 'isUsingMock')
                && $this->connection->isUsingMock()
            ) {
                return;
            }

            if ($this->count([]) > 0) {
                return;
            }

            $fallbackCategories = $this->loadFallbackCategories();
            if (empty($fallbackCategories)) {
                return;
            }

            foreach ($fallbackCategories as $category) {
                $payload = [
                    'name' => $category['name'] ?? 'Untitled Category',
                    'slug' => $category['slug'] ?? $this->slugify((string) ($category['name'] ?? 'category')),
                    'description' => $category['description'] ?? null,
                    'status' => $category['status'] ?? 'active',
                    'image_path' => $category['image_path'] ?? null,
                ];

                try {
                    $this->createCategory($payload);
                } catch (\InvalidArgumentException $exception) {
                    // Slug already exists or payload invalid; continue with remaining records.
                    continue;
                } catch (\Throwable $exception) {
                    // Stop seeding on unexpected errors to avoid repeated failures.
                    break;
                }
            }
        } catch (\Throwable $exception) {
            // Ignore seeding issues; normal operations should continue.
        }
    }

    private function loadFallbackCategories(): array
    {
        $fallbackPath = dirname(__DIR__, 2) . '/storage/admin.json';
        if (!is_readable($fallbackPath)) {
            return [];
        }

        $contents = file_get_contents($fallbackPath);
        if ($contents === false || trim($contents) === '') {
            return [];
        }

        $decoded = json_decode($contents, true);
        if (!is_array($decoded)) {
            return [];
        }

        $items = $decoded['items'] ?? [];
        if (!is_array($items)) {
            return [];
        }

        return array_map(function (array $item): array {
            if (!isset($item['slug']) && isset($item['name'])) {
                $item['slug'] = $this->slugify((string) $item['name']);
            }

            if (!isset($item['status']) || $item['status'] === '') {
                $item['status'] = 'active';
            }

            return $item;
        }, $items);
    }

    private function slugify(string $value): string
    {
        $slug = strtolower(trim($value));
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
        $slug = trim((string) $slug, '-');

        if ($slug === '') {
            try {
                $slug = 'category-' . substr(bin2hex(random_bytes(6)), 0, 12);
            } catch (\Throwable $exception) {
                $slug = 'category-' . uniqid('', false);
            }
        }

        return $slug;
    }
}
