<?php

namespace RCF\Repositories;

use RCF\Database\MongoConnection;
use MongoDB\Collection;
use MongoDB\BSON\ObjectId;
use MongoDB\Exception\Exception as MongoException;
use MongoDB\Model\BSONDocument;

/**
 * Base Repository Class
 * 
 * Provides common CRUD operations for MongoDB collections
 */
abstract class BaseRepository
{
    protected MongoConnection $connection;
    protected $collection;
    protected string $collectionName;
    protected bool $usingMock = false;
    
    public function __construct()
    {
        $this->connection = MongoConnection::getInstance();
        $this->collectionName = $this->getCollectionName();
        $this->usingMock = $this->connection->isUsingMock();
        $this->collection = $this->connection->getCollection($this->collectionName);
    }
    
    /**
     * Get the collection name for this repository
     * Must be implemented by child classes
     */
    abstract protected function getCollectionName(): string;
    
    /**
     * Create a new document
     */
    public function create(array $data): ?string
    {
        try {
            $this->ensureCollection();
            // Add timestamps
            $data['created_at'] = new \DateTime();
            $data['updated_at'] = new \DateTime();
            
            // Validate data before insertion
            $this->validateData($data);
            
            $result = $this->collection->insertOne($data);

            // Handle both MongoDB and mock results
            if (method_exists($result, 'getInsertedCount')) {
                // Real MongoDB result
                if ($result->getInsertedCount() > 0) {
                    return (string) $result->getInsertedId();
                }
            } else {
                // Mock result
                return (string) $result->getInsertedId();
            }
            
            return null;
        } catch (MongoException $e) {
            $this->handleException('create', $e, $data);
            return null;
        }
    }
    
    /**
     * Find a document by ID
     */
    public function findById(string $id): ?array
    {
        try {
            $this->ensureCollection();
            $objectId = $this->createObjectId($id);
            $document = $this->collection->findOne(['_id' => $objectId]);
            
            return $document ? $this->documentToArray($document) : null;
        } catch (MongoException $e) {
            $this->handleException('findById', $e, ['id' => $id]);
            return null;
        }
    }
    
    /**
     * Find documents by criteria
     */
    public function find(array $filter = [], array $options = []): array
    {
        try {
            $this->ensureCollection();
            $cursor = $this->collection->find($filter, $options);
            $results = [];
            
            foreach ($cursor as $document) {
                $results[] = $this->documentToArray($document);
            }
            
            return $results;
        } catch (MongoException $e) {
            $this->handleException('find', $e, ['filter' => $filter, 'options' => $options]);
            return [];
        }
    }
    
    /**
     * Find one document by criteria
     */
    public function findOne(array $filter, array $options = []): ?array
    {
        try {
            $this->ensureCollection();
            $document = $this->collection->findOne($filter, $options);
            return $document ? $this->documentToArray($document) : null;
        } catch (MongoException $e) {
            $this->handleException('findOne', $e, ['filter' => $filter, 'options' => $options]);
            return null;
        }
    }
    
    /**
     * Update a document by ID
     */
    public function updateById(string $id, array $data): bool
    {
        try {
            $this->ensureCollection();
            $objectId = $this->createObjectId($id);
            
            // Add updated timestamp
            $data['updated_at'] = new \DateTime();
            
            // Validate data before update
            $this->validateData($data, true);
            
            $result = $this->collection->updateOne(
                ['_id' => $objectId],
                ['$set' => $data]
            );
            
            return method_exists($result, 'getModifiedCount') ?
                $result->getModifiedCount() > 0 :
                $result->getModifiedCount() > 0;
        } catch (MongoException $e) {
            $this->handleException('updateById', $e, ['id' => $id, 'data' => $data]);
            return false;
        }
    }
    
    /**
     * Update documents by criteria
     */
    public function updateMany(array $filter, array $data): int
    {
        try {
            $this->ensureCollection();
            // Add updated timestamp
            $data['updated_at'] = new \DateTime();
            
            $result = $this->collection->updateMany(
                $filter,
                ['$set' => $data]
            );
            
            return method_exists($result, 'getModifiedCount') ?
                $result->getModifiedCount() :
                $result->getModifiedCount();
        } catch (MongoException $e) {
            $this->handleException('updateMany', $e, ['filter' => $filter, 'data' => $data]);
            return 0;
        }
    }
    
    /**
     * Delete a document by ID
     */
    public function deleteById(string $id): bool
    {
        try {
            $this->ensureCollection();
            $objectId = $this->createObjectId($id);
            $result = $this->collection->deleteOne(['_id' => $objectId]);
            
            return method_exists($result, 'getDeletedCount') ?
                $result->getDeletedCount() > 0 :
                $result->getDeletedCount() > 0;
        } catch (MongoException $e) {
            $this->handleException('deleteById', $e, ['id' => $id]);
            return false;
        }
    }
    
    /**
     * Delete documents by criteria
     */
    public function deleteMany(array $filter): int
    {
        try {
            $this->ensureCollection();
            $result = $this->collection->deleteMany($filter);
            return method_exists($result, 'getDeletedCount') ?
                $result->getDeletedCount() :
                $result->getDeletedCount();
        } catch (MongoException $e) {
            $this->handleException('deleteMany', $e, ['filter' => $filter]);
            return 0;
        }
    }
    
    /**
     * Count documents
     */
    public function count(array $filter = []): int
    {
        try {
            $this->ensureCollection();
            return $this->collection->countDocuments($filter);
        } catch (MongoException $e) {
            $this->handleException('count', $e, ['filter' => $filter]);
            return 0;
        }
    }
    
    /**
     * Check if document exists
     */
    public function exists(array $filter): bool
    {
        return $this->count($filter) > 0;
    }
    
    /**
     * Get paginated results
     */
    public function paginate(array $filter = [], int $page = 1, int $limit = 10, array $sort = []): array
    {
        $skip = ($page - 1) * $limit;
        
        $options = [
            'skip' => $skip,
            'limit' => $limit,
        ];
        
        if (!empty($sort)) {
            $options['sort'] = $sort;
        }
        
        $this->ensureCollection();
        $items = $this->find($filter, $options);
        $total = $this->count($filter);
        
        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => ceil($total / $limit),
                'has_next' => $page < ceil($total / $limit),
                'has_prev' => $page > 1,
            ],
        ];
    }
    
    /**
     * Convert MongoDB document to array
     */
    protected function documentToArray($document): array
    {
        if ($document instanceof BSONDocument) {
            // Convert BSONDocument to array using iterator
            $array = [];
            foreach ($document as $key => $value) {
                $array[$key] = $value;
            }
        } else {
            $array = (array) $document;
        }
        
        // Convert ObjectId to string
        if (isset($array['_id']) && $array['_id'] instanceof ObjectId) {
            $array['id'] = (string) $array['_id'];
            unset($array['_id']);
        }
        
        // Convert date values to strings for consistent downstream handling
        foreach ($array as $key => $value) {
            if ($value instanceof \MongoDB\BSON\UTCDateTime) {
                $array[$key] = $value->toDateTime()->format('Y-m-d H:i:s');
            } elseif ($value instanceof \DateTimeInterface) {
                $array[$key] = $value->format('Y-m-d H:i:s');
            } elseif (is_array($value) && isset($value['date'], $value['timezone'])) {
                // Handle DateTime objects that were converted to arrays (e.g. by json serialization)
                $array[$key] = $value['date'];
            }
        }

        return $array;
    }
    
    /**
     * Validate data before database operations
     * Can be overridden by child classes for specific validation
     */
    protected function validateData(array $data, bool $isUpdate = false): void
    {
        // Base validation - can be extended by child classes
        if (!$isUpdate && empty($data)) {
            throw new \InvalidArgumentException('Data cannot be empty for create operation');
        }
    }
    
    /**
     * Handle MongoDB exceptions
     */
    protected function handleException(string $operation, MongoException $e, array $context = []): void
    {
        $message = "MongoDB {$operation} operation failed: " . $e->getMessage();
        
        // Log the error if logging is enabled
        error_log($message . ' Context: ' . json_encode($context));
        
        // You can extend this to use a proper logging system
        // $this->connection->log('error', $message, array_merge($context, ['exception' => $e->getMessage()]));
    }
    
    /**
     * Create ObjectId or string ID for mock
     */
    protected function createObjectId(string $id)
    {
        if ($this->connection->isUsingMock()) {
            return $id; // Mock uses string IDs
        }

        if (class_exists('MongoDB\BSON\ObjectId')) {
            return new \MongoDB\BSON\ObjectId($id);
        }

        return $id; // Fallback to string
    }

    /**
     * Get the underlying MongoDB collection
     */
    public function getCollection()
    {
        $this->ensureCollection();
        return $this->collection;
    }

    protected function ensureCollection(): void
    {
        $currentMockState = $this->connection->isUsingMock();
        if ($this->collection === null || $currentMockState !== $this->usingMock) {
            $this->collection = $this->connection->getCollection($this->collectionName);
            $this->usingMock = $currentMockState;
        }
    }
}
