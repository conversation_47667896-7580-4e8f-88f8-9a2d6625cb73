<?php

namespace RCF\Repositories;

use RCF\Database\MongoConnection;
use MongoDB\BSON\ObjectId;
use MongoDB\Exception\Exception as MongoException;

/**
 * Product Repository
 * 
 * Handles product-specific database operations including inventory management,
 * search, filtering, and product relationships
 */
class ProductRepository extends BaseRepository
{
    /**
     * Get the collection name for products
     */
    protected function getCollectionName(): string
    {
        return $this->connection->getCollectionName('products');
    }
    
    /**
     * Validate product data
     */
    protected function validateData(array $data, bool $isUpdate = false): void
    {
        parent::validateData($data, $isUpdate);
        
        if (!$isUpdate) {
            // Required fields for new products
            $requiredFields = ['name', 'slug', 'price'];
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    throw new \InvalidArgumentException("Field '{$field}' is required");
                }
            }
        }
        
        // Validate slug format
        if (isset($data['slug'])) {
            if (!preg_match('/^[a-z0-9-]+$/', $data['slug'])) {
                throw new \InvalidArgumentException('Slug must contain only lowercase letters, numbers, and hyphens');
            }
        }
        
        // Validate price
        if (isset($data['price'])) {
            if (!is_numeric($data['price']) || $data['price'] < 0) {
                throw new \InvalidArgumentException('Price must be a positive number');
            }
        }
        
        // Validate status
        if (isset($data['status'])) {
            $validStatuses = ['active', 'inactive', 'draft', 'out_of_stock'];
            if (!in_array($data['status'], $validStatuses)) {
                throw new \InvalidArgumentException('Status must be one of: ' . implode(', ', $validStatuses));
            }
        }
        
        // Validate stock quantity
        if (isset($data['stock_quantity'])) {
            if (!is_int($data['stock_quantity']) || $data['stock_quantity'] < 0) {
                throw new \InvalidArgumentException('Stock quantity must be a non-negative integer');
            }
        }
    }
    
    /**
     * Create a new product
     */
    public function createProduct(array $productData): ?string
    {
        // Set default values
        $productData['status'] = $productData['status'] ?? 'active';
        $productData['stock_quantity'] = $productData['stock_quantity'] ?? 0;
        $productData['views'] = 0;
        $productData['sales_count'] = 0;
        
        // Check if slug already exists
        if ($this->findBySlug($productData['slug'])) {
            throw new \InvalidArgumentException('Product with this slug already exists');
        }
        
        return $this->create($productData);
    }
    
    /**
     * Find product by slug
     */
    public function findBySlug(string $slug): ?array
    {
        return $this->findOne(['slug' => $slug]);
    }
    
    /**
     * Get products by category
     */
    public function getProductsByCategory(string $categoryId, array $options = []): array
    {
        $filter = ['category_id' => $categoryId, 'status' => 'active'];
        $mongoOptions = [
            'sort' => $options['sort'] ?? ['created_at' => -1],
            'limit' => $options['limit'] ?? 20
        ];
        
        return $this->find($filter, $mongoOptions);
    }
    
    /**
     * Search products
     */
    public function searchProducts(string $searchTerm, array $filters = [], int $limit = 20): array
    {
        $filter = [
            '$and' => [
                [
                    '$or' => [
                        ['name' => ['$regex' => $searchTerm, '$options' => 'i']],
                        ['description' => ['$regex' => $searchTerm, '$options' => 'i']],
                        ['tags' => ['$regex' => $searchTerm, '$options' => 'i']]
                    ]
                ]
            ]
        ];
        
        // Apply additional filters
        if (!empty($filters['category_id'])) {
            $filter['$and'][] = ['category_id' => $filters['category_id']];
        }
        
        if (!empty($filters['min_price'])) {
            $filter['$and'][] = ['price' => ['$gte' => (float)$filters['min_price']]];
        }
        
        if (!empty($filters['max_price'])) {
            $filter['$and'][] = ['price' => ['$lte' => (float)$filters['max_price']]];
        }
        
        if (!empty($filters['status'])) {
            $filter['$and'][] = ['status' => $filters['status']];
        } else {
            $filter['$and'][] = ['status' => 'active'];
        }
        
        $options = [
            'limit' => $limit,
            'sort' => $filters['sort'] ?? ['name' => 1]
        ];
        
        return $this->find($filter, $options);
    }
    
    /**
     * Get featured products
     */
    public function getFeaturedProducts(int $limit = 10): array
    {
        return $this->find(
            ['featured' => true, 'status' => 'active'],
            ['limit' => $limit, 'sort' => ['created_at' => -1]]
        );
    }
    
    /**
     * Get products on sale
     */
    public function getSaleProducts(int $limit = 10): array
    {
        return $this->find(
            ['sale_price' => ['$exists' => true, '$gt' => 0], 'status' => 'active'],
            ['limit' => $limit, 'sort' => ['created_at' => -1]]
        );
    }
    
    /**
     * Get top selling products
     */
    public function getTopSellingProducts(int $limit = 10): array
    {
        return $this->find(
            ['status' => 'active'],
            ['limit' => $limit, 'sort' => ['sales_count' => -1]]
        );
    }
    
    /**
     * Update product stock
     */
    public function updateStock(string $productId, int $quantity, string $operation = 'set'): bool
    {
        try {
            $objectId = new ObjectId($productId);
            
            switch ($operation) {
                case 'increment':
                    $update = ['$inc' => ['stock_quantity' => $quantity]];
                    break;
                case 'decrement':
                    $update = ['$inc' => ['stock_quantity' => -$quantity]];
                    break;
                case 'set':
                default:
                    $update = ['$set' => ['stock_quantity' => $quantity]];
                    break;
            }
            
            $update['$set']['updated_at'] = new \DateTime();
            
            $result = $this->collection->updateOne(['_id' => $objectId], $update);
            return $result->getModifiedCount() > 0;
            
        } catch (MongoException $e) {
            $this->handleException('updateStock', $e, ['productId' => $productId, 'quantity' => $quantity]);
            return false;
        } catch (\Exception $e) {
            error_log('updateStock operation failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Increment product views
     */
    public function incrementViews(string $productId): bool
    {
        try {
            $objectId = new ObjectId($productId);
            $result = $this->collection->updateOne(
                ['_id' => $objectId],
                ['$inc' => ['views' => 1]]
            );
            return $result->getModifiedCount() > 0;
        } catch (MongoException $e) {
            $this->handleException('incrementViews', $e, ['productId' => $productId]);
            return false;
        } catch (\Exception $e) {
            error_log('incrementViews operation failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get low stock products
     */
    public function getLowStockProducts(int $threshold = 10): array
    {
        return $this->find(
            ['stock_quantity' => ['$lte' => $threshold], 'status' => 'active'],
            ['sort' => ['stock_quantity' => 1]]
        );
    }
    
    /**
     * Get products with filters and pagination
     */
    public function getProductsWithFilters(array $filters = [], int $page = 1, int $limit = 12): array
    {
        $mongoFilter = ['status' => 'active'];
        
        // Apply filters
        if (!empty($filters['category_id'])) {
            $mongoFilter['category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['min_price'])) {
            $mongoFilter['price']['$gte'] = (float)$filters['min_price'];
        }
        
        if (!empty($filters['max_price'])) {
            $mongoFilter['price']['$lte'] = (float)$filters['max_price'];
        }
        
        if (!empty($filters['search'])) {
            $mongoFilter['$or'] = [
                ['name' => ['$regex' => $filters['search'], '$options' => 'i']],
                ['description' => ['$regex' => $filters['search'], '$options' => 'i']]
            ];
        }
        
        if (!empty($filters['featured'])) {
            $mongoFilter['featured'] = true;
        }
        
        if (!empty($filters['on_sale'])) {
            $mongoFilter['sale_price'] = ['$exists' => true, '$gt' => 0];
        }
        
        // Default sort
        $sort = ['created_at' => -1];
        if (!empty($filters['sort'])) {
            switch ($filters['sort']) {
                case 'price_asc':
                    $sort = ['price' => 1];
                    break;
                case 'price_desc':
                    $sort = ['price' => -1];
                    break;
                case 'name_asc':
                    $sort = ['name' => 1];
                    break;
                case 'name_desc':
                    $sort = ['name' => -1];
                    break;
                case 'popularity':
                    $sort = ['views' => -1];
                    break;
                case 'sales':
                    $sort = ['sales_count' => -1];
                    break;
                default:
                    $sort = ['created_at' => -1];
            }
        }
        
        return $this->paginate($mongoFilter, $page, $limit, $sort);
    }

    /**
     * Update product by slug
     */
    public function updateBySlug(string $slug, array $data): bool
    {
        $product = $this->findBySlug($slug);
        if (!$product) {
            return false;
        }

        // If updating slug, check for conflicts
        if (isset($data['slug']) && $data['slug'] !== $slug) {
            if ($this->findBySlug($data['slug'])) {
                throw new \InvalidArgumentException('Product with this slug already exists');
            }
        }

        return $this->updateById($product['id'], $data);
    }

    /**
     * Delete product by slug
     */
    public function deleteBySlug(string $slug): bool
    {
        $product = $this->findBySlug($slug);
        if (!$product) {
            return false;
        }

        return $this->deleteById($product['id']);
    }

    /**
     * Get product statistics
     */
    public function getStatistics(): array
    {
        $pipeline = [
            [
                '$group' => [
                    '_id' => null,
                    'total_products' => ['$sum' => 1],
                    'active_products' => [
                        '$sum' => ['$cond' => [['$eq' => ['$status', 'active']], 1, 0]]
                    ],
                    'out_of_stock' => [
                        '$sum' => ['$cond' => [['$lte' => ['$stock_quantity', 0]], 1, 0]]
                    ],
                    'low_stock' => [
                        '$sum' => ['$cond' => [['$and' => [['$gt' => ['$stock_quantity', 0]], ['$lte' => ['$stock_quantity', 10]]]], 1, 0]]
                    ],
                    'total_value' => ['$sum' => ['$multiply' => ['$price', '$stock_quantity']]],
                    'avg_price' => ['$avg' => '$price']
                ]
            ]
        ];

        try {
            $cursor = $this->collection->aggregate($pipeline);
            $result = [];
            foreach ($cursor as $doc) {
                $result[] = $doc;
            }

            if (empty($result)) {
                return [
                    'total_products' => 0,
                    'active_products' => 0,
                    'out_of_stock' => 0,
                    'low_stock' => 0,
                    'total_value' => 0,
                    'avg_price' => 0
                ];
            }

            $stats = $result[0];
            unset($stats['_id']);

            return $stats;

        } catch (MongoException $e) {
            $this->handleException('getStatistics', $e);
            return [
                'total_products' => 0,
                'active_products' => 0,
                'out_of_stock' => 0,
                'low_stock' => 0,
                'total_value' => 0,
                'avg_price' => 0
            ];
        } catch (\Exception $e) {
            error_log('getStatistics operation failed: ' . $e->getMessage());
            return [
                'total_products' => 0,
                'active_products' => 0,
                'out_of_stock' => 0,
                'low_stock' => 0,
                'total_value' => 0,
                'avg_price' => 0
            ];
        }
    }

    /**
     * Bulk update product status
     */
    public function bulkUpdateStatus(array $productIds, string $status): int
    {
        $validStatuses = ['active', 'inactive', 'draft', 'out_of_stock'];
        if (!in_array($status, $validStatuses)) {
            throw new \InvalidArgumentException('Invalid status');
        }

        $objectIds = array_map(function($id) {
            return new ObjectId($id);
        }, $productIds);

        return $this->updateMany(
            ['_id' => ['$in' => $objectIds]],
            ['status' => $status]
        );
    }

    /**
     * Get related products (by category)
     */
    public function getRelatedProducts(string $productId, int $limit = 4): array
    {
        $product = $this->findById($productId);
        if (!$product || empty($product['category_id'])) {
            return [];
        }

        return $this->find(
            [
                'category_id' => $product['category_id'],
                '_id' => ['$ne' => new ObjectId($productId)],
                'status' => 'active'
            ],
            ['limit' => $limit, 'sort' => ['views' => -1]]
        );
    }
}
