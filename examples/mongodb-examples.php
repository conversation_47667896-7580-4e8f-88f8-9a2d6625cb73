<?php
/**
 * MongoDB Integration Examples
 * 
 * Practical examples of using MongoDB in RC Furnishing
 */

require_once '../bootstrap.php';

echo "<h1>MongoDB Integration Examples</h1>\n";
echo "<pre>\n";

// Check MongoDB availability
echo "=== MongoDB Status ===\n";
$mongoAvailable = is_mongodb_available();
echo "MongoDB Available: " . ($mongoAvailable ? "YES" : "NO") . "\n";

if (!$mongoAvailable) {
    echo "Note: Examples will use fallback methods where possible.\n";
}
echo "\n";

// Example 1: Working with Categories
echo "=== Example 1: Category Operations ===\n";

try {
    // Get all active categories
    $categories = get_categories('active');
    echo "Active categories found: " . count($categories) . "\n";
    
    foreach (array_slice($categories, 0, 3) as $category) {
        echo "- {$category['name']} ({$category['slug']})\n";
    }
    
    if ($mongoAvailable) {
        $repo = getCategoryRepository();
        
        // Get category statistics
        $stats = $repo->getStatistics();
        echo "Category stats: " . json_encode($stats) . "\n";
        
        // Search categories
        $searchResults = $repo->searchByName('elect');
        echo "Search results for 'elect': " . count($searchResults) . " found\n";
    }
    
} catch (Exception $e) {
    echo "Error in category operations: " . $e->getMessage() . "\n";
}
echo "\n";

// Example 2: Product Operations (MongoDB only)
if ($mongoAvailable) {
    echo "=== Example 2: Product Operations ===\n";
    
    try {
        $productRepo = getProductRepository();
        
        // Get featured products
        $featured = $productRepo->getFeaturedProducts(3);
        echo "Featured products: " . count($featured) . "\n";
        
        foreach ($featured as $product) {
            echo "- {$product['name']} - \${$product['price']}\n";
        }
        
        // Search products
        $searchResults = $productRepo->searchProducts('nestle', [], 5);
        echo "Search results for 'nestle': " . count($searchResults) . " found\n";
        
        // Get products with filters
        $filteredProducts = $productRepo->getProductsWithFilters([
            'min_price' => 20,
            'max_price' => 50,
            'sort' => 'price_asc'
        ], 1, 5);
        
        echo "Filtered products (price 20-50): " . count($filteredProducts['items']) . "\n";
        foreach ($filteredProducts['items'] as $product) {
            echo "- {$product['name']} - \${$product['price']}\n";
        }
        
        // Get product statistics
        $productStats = $productRepo->getStatistics();
        echo "Product statistics:\n";
        echo "  Total products: {$productStats['total_products']}\n";
        echo "  Active products: {$productStats['active_products']}\n";
        echo "  Out of stock: {$productStats['out_of_stock']}\n";
        echo "  Average price: $" . number_format($productStats['avg_price'], 2) . "\n";
        
    } catch (Exception $e) {
        echo "Error in product operations: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Example 3: Creating New Data (MongoDB only)
if ($mongoAvailable) {
    echo "=== Example 3: Creating New Data ===\n";
    
    try {
        $categoryRepo = getCategoryRepository();
        
        // Check if example category exists
        $exampleSlug = 'example-category-' . date('His');
        $existingCategory = $categoryRepo->findBySlug($exampleSlug);
        
        if (!$existingCategory) {
            // Create new category
            $categoryId = $categoryRepo->createCategory([
                'name' => 'Example Category ' . date('H:i:s'),
                'slug' => $exampleSlug,
                'description' => 'This is an example category created by the demo',
                'status' => 'active'
            ]);
            
            echo "Created new category with ID: {$categoryId}\n";
            
            // Create a product in this category
            $productRepo = getProductRepository();
            $productSlug = 'example-product-' . date('His');
            
            $productId = $productRepo->createProduct([
                'name' => 'Example Product ' . date('H:i:s'),
                'slug' => $productSlug,
                'description' => 'This is an example product',
                'price' => 29.99,
                'sale_price' => 24.99,
                'stock_quantity' => 10,
                'category_id' => $categoryId,
                'featured' => false,
                'status' => 'active',
                'tags' => ['example', 'demo', 'test']
            ]);
            
            echo "Created new product with ID: {$productId}\n";
            
            // Update the product
            $updated = $productRepo->updateById($productId, [
                'description' => 'Updated description for example product',
                'stock_quantity' => 15
            ]);
            
            echo "Product updated: " . ($updated ? "YES" : "NO") . "\n";
            
            // Increment views
            $productRepo->incrementViews($productId);
            echo "Product views incremented\n";
            
        } else {
            echo "Example category already exists\n";
        }
        
    } catch (Exception $e) {
        echo "Error creating data: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Example 4: Error Handling
echo "=== Example 4: Error Handling ===\n";

try {
    // Attempt to create category with invalid data
    if ($mongoAvailable) {
        $categoryRepo = getCategoryRepository();
        
        // This should fail due to validation
        $categoryRepo->createCategory([
            'name' => '', // Empty name should fail
            'slug' => 'invalid slug with spaces', // Invalid slug format
            'status' => 'invalid_status' // Invalid status
        ]);
    }
} catch (InvalidArgumentException $e) {
    echo "Validation error caught: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "General error caught: " . $e->getMessage() . "\n";
}

// Example of duplicate key error
try {
    if ($mongoAvailable) {
        $categoryRepo = getCategoryRepository();
        
        // Try to create category with existing slug
        $categories = $categoryRepo->find([], ['limit' => 1]);
        if (!empty($categories)) {
            $existingSlug = $categories[0]['slug'];
            $categoryRepo->createCategory([
                'name' => 'Duplicate Test',
                'slug' => $existingSlug, // This should fail
                'status' => 'active'
            ]);
        }
    }
} catch (Exception $e) {
    echo "Duplicate key error caught: " . $e->getMessage() . "\n";
}
echo "\n";

// Example 5: Pagination
if ($mongoAvailable) {
    echo "=== Example 5: Pagination ===\n";
    
    try {
        $categoryRepo = getCategoryRepository();
        
        // Get paginated categories
        $page1 = $categoryRepo->paginate([], 1, 2); // Page 1, 2 items per page
        
        echo "Pagination example:\n";
        echo "  Current page: {$page1['pagination']['current_page']}\n";
        echo "  Per page: {$page1['pagination']['per_page']}\n";
        echo "  Total items: {$page1['pagination']['total']}\n";
        echo "  Total pages: {$page1['pagination']['total_pages']}\n";
        echo "  Has next: " . ($page1['pagination']['has_next'] ? "YES" : "NO") . "\n";
        echo "  Items on this page: " . count($page1['items']) . "\n";
        
        foreach ($page1['items'] as $item) {
            echo "  - {$item['name']}\n";
        }
        
    } catch (Exception $e) {
        echo "Error in pagination: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Example 6: Performance Monitoring
echo "=== Example 6: Performance Monitoring ===\n";

$startTime = microtime(true);

// Perform some operations
$categories = get_categories('active');
if ($mongoAvailable) {
    $productRepo = getProductRepository();
    $products = $productRepo->find(['status' => 'active'], ['limit' => 10]);
}

$endTime = microtime(true);
$executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds

echo "Operations completed in: " . number_format($executionTime, 2) . " ms\n";
echo "Categories loaded: " . count($categories) . "\n";
if ($mongoAvailable && isset($products)) {
    echo "Products loaded: " . count($products) . "\n";
}
echo "\n";

// Example 7: Bulk Operations (MongoDB only)
if ($mongoAvailable) {
    echo "=== Example 7: Bulk Operations ===\n";
    
    try {
        $categoryRepo = getCategoryRepository();
        
        // Get some categories for bulk update
        $categories = $categoryRepo->find(['status' => 'active'], ['limit' => 2]);
        
        if (count($categories) >= 2) {
            $categoryIds = array_column($categories, 'id');
            
            // Bulk update status (just for demo, then revert)
            $updated = $categoryRepo->bulkUpdateStatus($categoryIds, 'inactive');
            echo "Bulk updated {$updated} categories to inactive\n";
            
            // Revert the change
            $reverted = $categoryRepo->bulkUpdateStatus($categoryIds, 'active');
            echo "Reverted {$reverted} categories back to active\n";
        }
        
    } catch (Exception $e) {
        echo "Error in bulk operations: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

echo "=== Examples Complete ===\n";
echo "Check the logs in storage/logs/ for detailed operation logs.\n";
echo "</pre>\n";

echo '<p><a href="../mongodb-demo.php">← Back to Demo</a></p>';
?>
