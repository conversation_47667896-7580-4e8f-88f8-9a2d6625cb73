[2025-09-19 09:19:33] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:19:33] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:19:44] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:19:44] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:20:43] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:20:43] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:28:28] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:28:28] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:30:32] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:30:32] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:30:59] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:30:59] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:36:44] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:36:44] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:46:02] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:46:02] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:54:54] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:54:54] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:55:29] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-19 09:55:29] INFO: MongoDB connection: mock_initialized []
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"categories","keys":{"slug":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"categories","index":{"slug":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"categories","keys":{"status":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"categories","index":{"status":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"categories","keys":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"categories","index":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"products","keys":{"slug":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"products","index":{"slug":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"products","keys":{"category_id":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"products","index":{"category_id":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"products","keys":{"status":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"products","index":{"status":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"products","keys":{"price":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"products","index":{"price":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"products","keys":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"products","index":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"products","keys":{"name":"text","description":"text"}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"products","index":{"name":"text","description":"text"}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"users","keys":{"email":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"users","index":{"email":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"users","keys":{"status":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"users","index":{"status":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"users","keys":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"users","index":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"orders","keys":{"user_id":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"orders","index":{"user_id":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"orders","keys":{"status":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"orders","index":{"status":1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"orders","keys":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"orders","index":{"created_at":-1}}
[2025-09-19 09:55:29] INFO: Mock index created {"collection":"orders","keys":{"order_number":1}}
[2025-09-19 09:55:29] INFO: Created index {"collection":"orders","index":{"order_number":1}}
[2025-09-19 10:32:14] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 10:32:21] INFO: MongoDB connection: success []
[2025-09-19 10:33:18] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 10:33:19] INFO: MongoDB connection: success []
[2025-09-19 10:36:25] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 10:36:31] INFO: MongoDB connection: success []
[2025-09-19 11:07:07] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:07:15] INFO: MongoDB connection: success []
[2025-09-19 11:20:48] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:21:00] INFO: MongoDB connection: success []
[2025-09-19 11:21:08] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:21:09] INFO: MongoDB connection: success []
[2025-09-19 11:21:15] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:21:16] INFO: MongoDB connection: success []
[2025-09-19 11:22:08] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:22:09] INFO: MongoDB connection: success []
[2025-09-19 11:22:21] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:22:22] INFO: MongoDB connection: success []
[2025-09-19 11:22:22] INFO: Created index {"collection":"categories","index":{"slug":1}}
[2025-09-19 11:22:22] INFO: Created index {"collection":"categories","index":{"status":1}}
[2025-09-19 11:22:23] INFO: Created index {"collection":"categories","index":{"created_at":-1}}
[2025-09-19 11:22:23] INFO: Created index {"collection":"products","index":{"slug":1}}
[2025-09-19 11:22:23] INFO: Created index {"collection":"products","index":{"category_id":1}}
[2025-09-19 11:22:23] INFO: Created index {"collection":"products","index":{"status":1}}
[2025-09-19 11:22:23] INFO: Created index {"collection":"products","index":{"price":1}}
[2025-09-19 11:22:23] INFO: Created index {"collection":"products","index":{"created_at":-1}}
[2025-09-19 11:22:24] INFO: Created index {"collection":"products","index":{"name":"text","description":"text"}}
[2025-09-19 11:22:24] INFO: Created index {"collection":"users","index":{"email":1}}
[2025-09-19 11:22:24] INFO: Created index {"collection":"users","index":{"status":1}}
[2025-09-19 11:22:24] INFO: Created index {"collection":"users","index":{"created_at":-1}}
[2025-09-19 11:22:24] INFO: Created index {"collection":"orders","index":{"user_id":1}}
[2025-09-19 11:22:25] INFO: Created index {"collection":"orders","index":{"status":1}}
[2025-09-19 11:22:25] INFO: Created index {"collection":"orders","index":{"created_at":-1}}
[2025-09-19 11:22:25] INFO: Created index {"collection":"orders","index":{"order_number":1}}
[2025-09-19 11:23:29] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:23:31] INFO: MongoDB connection: success []
[2025-09-19 11:24:21] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:24:22] INFO: MongoDB connection: success []
[2025-09-19 11:24:44] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:25:01] INFO: MongoDB connection: success []
[2025-09-19 11:28:19] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:28:20] INFO: MongoDB connection: success []
[2025-09-19 11:30:05] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:30:12] INFO: MongoDB connection: success []
[2025-09-19 11:32:01] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:32:12] INFO: MongoDB connection: success []
[2025-09-19 11:32:17] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:32:20] INFO: MongoDB connection: success []
[2025-09-19 11:32:22] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:32:23] INFO: MongoDB connection: success []
[2025-09-19 11:34:10] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:34:22] INFO: MongoDB connection: success []
[2025-09-19 11:34:34] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:34:35] INFO: MongoDB connection: success []
[2025-09-19 11:34:53] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:34:54] INFO: MongoDB connection: success []
[2025-09-19 11:35:07] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:35:08] INFO: MongoDB connection: success []
[2025-09-19 11:35:23] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:35:30] INFO: MongoDB connection: success []
[2025-09-19 11:35:36] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:35:37] INFO: MongoDB connection: success []
[2025-09-19 11:35:50] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:35:51] INFO: MongoDB connection: success []
[2025-09-19 11:39:04] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:39:10] INFO: MongoDB connection: success []
[2025-09-19 11:39:25] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:39:25] INFO: MongoDB connection: success []
[2025-09-19 11:41:07] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:41:08] INFO: MongoDB connection: success []
[2025-09-19 11:41:59] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:42:01] INFO: MongoDB connection: success []
[2025-09-19 11:42:05] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:42:06] INFO: MongoDB connection: success []
[2025-09-19 11:43:35] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:43:41] INFO: MongoDB connection: success []
[2025-09-19 11:46:21] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:46:42] INFO: MongoDB connection: success []
[2025-09-19 11:46:59] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:47:00] INFO: MongoDB connection: success []
[2025-09-19 11:47:32] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:47:44] INFO: MongoDB connection: success []
[2025-09-19 11:48:00] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:48:01] INFO: MongoDB connection: success []
[2025-09-19 11:48:21] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 11:48:43] INFO: MongoDB connection: success []
[2025-09-19 12:03:45] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:03:51] INFO: MongoDB connection: success []
[2025-09-19 12:04:44] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:04:45] INFO: MongoDB connection: success []
[2025-09-19 12:05:58] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:06:14] INFO: MongoDB connection: success []
[2025-09-19 12:11:00] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:11:02] INFO: MongoDB connection: success []
[2025-09-19 12:14:20] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:14:26] INFO: MongoDB connection: success []
[2025-09-19 12:15:20] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:15:21] INFO: MongoDB connection: success []
[2025-09-19 12:15:53] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:15:54] INFO: MongoDB connection: success []
[2025-09-19 12:19:52] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:19:58] INFO: MongoDB connection: success []
[2025-09-19 12:46:12] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:46:13] INFO: MongoDB connection: success []
[2025-09-19 12:46:17] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:46:18] INFO: MongoDB connection: success []
[2025-09-19 12:46:26] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:46:27] INFO: MongoDB connection: success []
[2025-09-19 12:46:30] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:46:31] INFO: MongoDB connection: success []
[2025-09-19 12:47:25] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:47:26] INFO: MongoDB connection: success []
[2025-09-19 12:48:00] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:48:01] INFO: MongoDB connection: success []
[2025-09-19 12:48:05] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:48:05] INFO: MongoDB connection: success []
[2025-09-19 12:48:49] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:48:50] INFO: MongoDB connection: success []
[2025-09-19 12:50:18] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:50:19] INFO: MongoDB connection: success []
[2025-09-19 12:50:51] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:50:57] INFO: MongoDB connection: success []
[2025-09-19 12:51:28] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:51:29] INFO: MongoDB connection: success []
[2025-09-19 12:52:32] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:52:33] INFO: MongoDB connection: success []
[2025-09-19 12:52:44] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:52:45] INFO: MongoDB connection: success []
[2025-09-19 12:52:51] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:52:52] INFO: MongoDB connection: success []
[2025-09-19 12:52:56] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:52:57] INFO: MongoDB connection: success []
[2025-09-19 12:53:30] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 12:53:31] INFO: MongoDB connection: success []
[2025-09-19 13:43:56] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:44:12] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:44:15] INFO: MongoDB connection: success []
[2025-09-19 13:44:21] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:44:26] INFO: MongoDB connection: success []
[2025-09-19 13:45:31] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:45:32] INFO: MongoDB connection: success []
[2025-09-19 13:45:55] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:45:57] INFO: MongoDB connection: success []
[2025-09-19 13:47:50] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:47:51] INFO: MongoDB connection: success []
[2025-09-19 13:47:57] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:47:58] INFO: MongoDB connection: success []
[2025-09-19 13:48:02] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:48:08] INFO: MongoDB connection: success []
[2025-09-19 13:48:36] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:48:37] INFO: MongoDB connection: success []
[2025-09-19 13:51:03] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:51:04] INFO: MongoDB connection: success []
[2025-09-19 13:51:45] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:51:46] INFO: MongoDB connection: success []
[2025-09-19 13:53:20] INFO: MongoDB connection: mock_initialized []
[2025-09-19 13:53:20] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:53:32] INFO: MongoDB connection: success []
[2025-09-19 13:53:37] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:53:40] INFO: MongoDB connection: success []
[2025-09-19 13:53:45] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:53:46] INFO: MongoDB connection: success []
[2025-09-19 13:53:52] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:53:53] INFO: MongoDB connection: success []
[2025-09-19 13:53:58] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 13:53:59] INFO: MongoDB connection: success []
[2025-09-19 14:09:01] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 14:09:07] INFO: MongoDB connection: success []
[2025-09-19 14:09:17] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 14:09:17] INFO: MongoDB connection: success []
[2025-09-19 14:09:59] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 14:10:05] INFO: MongoDB connection: success []
[2025-09-19 14:10:15] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 14:10:16] INFO: MongoDB connection: success []
[2025-09-19 14:10:27] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 14:10:28] INFO: MongoDB connection: success []
[2025-09-19 14:11:28] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 14:11:29] INFO: MongoDB connection: success []
[2025-09-19 14:21:46] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 14:21:47] INFO: MongoDB connection: success []
[2025-09-19 15:55:05] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 15:55:06] INFO: MongoDB connection: success []
[2025-09-19 15:55:25] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 15:55:27] INFO: MongoDB connection: success []
[2025-09-19 15:59:25] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 15:59:31] INFO: MongoDB connection: success []
[2025-09-19 15:59:54] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 15:59:55] INFO: MongoDB connection: success []
[2025-09-19 16:00:16] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 16:00:18] INFO: MongoDB connection: success []
[2025-09-19 16:55:15] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 16:55:16] INFO: MongoDB connection: success []
[2025-09-19 16:55:18] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 16:55:19] INFO: MongoDB connection: success []
[2025-09-19 17:49:38] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 17:50:06] INFO: MongoDB connection: success []
[2025-09-19 17:50:26] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 17:50:28] INFO: MongoDB connection: success []
[2025-09-19 17:50:40] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 17:50:42] INFO: MongoDB connection: success []
[2025-09-19 17:50:53] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 17:51:02] INFO: MongoDB connection: success []
[2025-09-19 17:51:32] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 17:51:45] INFO: MongoDB connection: success []
[2025-09-19 17:51:50] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 17:52:00] INFO: MongoDB connection: failed {"error":"Failed to parse URI options: Failed to look up SRV record \"_mongodb._tcp.cluster0.ozeblis.mongodb.net\": A temporary error occurred on an authoritative name server. Try again later.","code":0}
[2025-09-19 17:52:00] INFO: MongoDB connection: falling_back_to_mock []
[2025-09-19 17:52:00] INFO: MongoDB connection: mock_initialized []
[2025-09-19 17:52:32] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 17:52:50] INFO: MongoDB connection: success []
[2025-09-19 18:07:10] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 18:07:21] INFO: MongoDB connection: success []
[2025-09-19 18:07:59] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 18:08:05] INFO: MongoDB connection: success []
[2025-09-19 19:28:41] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-19 19:28:42] INFO: MongoDB connection: success []
