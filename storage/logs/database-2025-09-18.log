[2025-09-18 17:24:36] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-18 17:29:23] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-18 17:32:01] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-18 17:36:21] INFO: MongoDB connection: attempting {"uri":"mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"}
[2025-09-18 18:06:36] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:06:36] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:07:56] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:07:56] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:10:28] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:10:28] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:11:53] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:11:53] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:13:48] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:13:48] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:13:56] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:13:56] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"categories","keys":{"slug":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"categories","index":{"slug":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"categories","keys":{"status":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"categories","index":{"status":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"categories","keys":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"categories","index":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"products","keys":{"slug":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"products","index":{"slug":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"products","keys":{"category_id":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"products","index":{"category_id":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"products","keys":{"status":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"products","index":{"status":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"products","keys":{"price":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"products","index":{"price":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"products","keys":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"products","index":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"products","keys":{"name":"text","description":"text"}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"products","index":{"name":"text","description":"text"}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"users","keys":{"email":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"users","index":{"email":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"users","keys":{"status":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"users","index":{"status":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"users","keys":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"users","index":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"orders","keys":{"user_id":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"orders","index":{"user_id":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"orders","keys":{"status":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"orders","index":{"status":1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"orders","keys":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"orders","index":{"created_at":-1}}
[2025-09-18 18:13:56] INFO: Mock index created {"collection":"orders","keys":{"order_number":1}}
[2025-09-18 18:13:56] INFO: Created index {"collection":"orders","index":{"order_number":1}}
[2025-09-18 18:15:15] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:15:15] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"categories","keys":{"slug":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"categories","index":{"slug":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"categories","keys":{"status":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"categories","index":{"status":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"categories","keys":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"categories","index":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"products","keys":{"slug":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"products","index":{"slug":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"products","keys":{"category_id":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"products","index":{"category_id":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"products","keys":{"status":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"products","index":{"status":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"products","keys":{"price":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"products","index":{"price":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"products","keys":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"products","index":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"products","keys":{"name":"text","description":"text"}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"products","index":{"name":"text","description":"text"}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"users","keys":{"email":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"users","index":{"email":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"users","keys":{"status":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"users","index":{"status":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"users","keys":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"users","index":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"orders","keys":{"user_id":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"orders","index":{"user_id":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"orders","keys":{"status":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"orders","index":{"status":1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"orders","keys":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"orders","index":{"created_at":-1}}
[2025-09-18 18:15:15] INFO: Mock index created {"collection":"orders","keys":{"order_number":1}}
[2025-09-18 18:15:15] INFO: Created index {"collection":"orders","index":{"order_number":1}}
[2025-09-18 18:30:12] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:30:12] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"categories","keys":{"slug":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"categories","index":{"slug":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"categories","keys":{"status":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"categories","index":{"status":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"categories","keys":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"categories","index":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"products","keys":{"slug":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"products","index":{"slug":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"products","keys":{"category_id":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"products","index":{"category_id":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"products","keys":{"status":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"products","index":{"status":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"products","keys":{"price":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"products","index":{"price":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"products","keys":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"products","index":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"products","keys":{"name":"text","description":"text"}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"products","index":{"name":"text","description":"text"}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"users","keys":{"email":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"users","index":{"email":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"users","keys":{"status":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"users","index":{"status":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"users","keys":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"users","index":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"orders","keys":{"user_id":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"orders","index":{"user_id":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"orders","keys":{"status":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"orders","index":{"status":1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"orders","keys":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"orders","index":{"created_at":-1}}
[2025-09-18 18:30:12] INFO: Mock index created {"collection":"orders","keys":{"order_number":1}}
[2025-09-18 18:30:12] INFO: Created index {"collection":"orders","index":{"order_number":1}}
[2025-09-18 18:30:19] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:30:19] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:32:53] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:32:53] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:32:59] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:32:59] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:33:13] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:33:13] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:33:16] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:33:16] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:33:22] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:33:22] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:37:07] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:37:07] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:39:47] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:39:47] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:43:31] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:43:31] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:43:53] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:43:53] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:44:01] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:44:01] INFO: MongoDB connection: mock_initialized []
[2025-09-18 18:44:09] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 18:44:09] INFO: MongoDB connection: mock_initialized []
[2025-09-18 19:30:39] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 19:30:39] INFO: MongoDB connection: mock_initialized []
[2025-09-18 19:30:47] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 19:30:47] INFO: MongoDB connection: mock_initialized []
[2025-09-18 19:34:10] INFO: MongoDB connection: mongodb_extension_missing []
[2025-09-18 19:34:10] INFO: MongoDB connection: mock_initialized []
