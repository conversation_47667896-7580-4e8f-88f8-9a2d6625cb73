<?php
// Comprehensive dropdown diagnostic tool
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set proper headers for web server response
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

echo "<!DOCTYPE html><html><head>";
echo "<title>Dropdown Diagnostic Tool</title>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".diagnostic-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }";
echo ".code-block { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; margin: 10px 0; }";
echo ".test-dropdown { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }";
echo "</style>";
echo "</head><body>";

echo "<h1>🔧 Category Dropdown Diagnostic Tool</h1>";
echo "<p><strong>Timestamp:</strong> " . date('Y-m-d H:i:s') . "</p>";

// 1. Server Environment Check
echo "<div class='diagnostic-section'>";
echo "<h2>1. Server Environment Check</h2>";

$serverInfo = [
    'PHP Version' => phpversion(),
    'Server Software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'Document Root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Not set',
    'Script Name' => $_SERVER['SCRIPT_NAME'] ?? 'Not set',
    'Request Method' => $_SERVER['REQUEST_METHOD'] ?? 'Not set',
    'HTTP Host' => $_SERVER['HTTP_HOST'] ?? 'Not set',
    'Request URI' => $_SERVER['REQUEST_URI'] ?? 'Not set'
];

foreach ($serverInfo as $key => $value) {
    echo "<p><strong>$key:</strong> $value</p>";
}
echo "</div>";

// 2. File System and Permissions Check
echo "<div class='diagnostic-section'>";
echo "<h2>2. File System & Permissions Check</h2>";

$rootDir = dirname(__DIR__);
echo "<p><strong>Root Directory:</strong> $rootDir</p>";

$criticalFiles = [
    'bootstrap.php',
    'components/admin/index.php',
    'components/admin/categories/index.php',
    'admin/categories.php',
    'admin/assets/js/main.js',
    'admin/assets/js/plugins.js',
    'admin/assets/css/style.css'
];

foreach ($criticalFiles as $file) {
    $fullPath = $rootDir . '/' . $file;
    $exists = file_exists($fullPath);
    $readable = $exists && is_readable($fullPath);
    $writable = $exists && is_writable($fullPath);
    $perms = $exists ? substr(sprintf('%o', fileperms($fullPath)), -4) : 'N/A';
    
    $status = $exists ? ($readable ? 'success' : 'error') : 'error';
    echo "<p class='$status'>";
    echo "<strong>$file:</strong> ";
    echo $exists ? '✅ Exists' : '❌ Missing';
    echo $readable ? ' | ✅ Readable' : ' | ❌ Not Readable';
    echo " | Permissions: $perms";
    echo "</p>";
}
echo "</div>";

// 3. HTTP Response Test
echo "<div class='diagnostic-section'>";
echo "<h2>3. HTTP Response Test</h2>";

$categoriesUrl = 'http://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/admin/categories.php';
echo "<p><strong>Testing URL:</strong> <a href='$categoriesUrl' target='_blank'>$categoriesUrl</a></p>";

// Use cURL to test the actual HTTP response
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $categoriesUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p class='error'>❌ cURL Error: $error</p>";
    } else {
        $headers = substr($response, 0, $headerSize);
        $body = substr($response, $headerSize);
        
        echo "<p class='success'>✅ HTTP Response Code: $httpCode</p>";
        echo "<div class='code-block'><strong>Response Headers:</strong><br>" . htmlspecialchars($headers) . "</div>";
        
        if ($httpCode === 200) {
            echo "<p class='success'>✅ Page loads successfully</p>";
            echo "<p>Response body length: " . strlen($body) . " characters</p>";
            
            // Check for specific content
            $hasDropdown = strpos($body, 'name="status"') !== false;
            $hasSearch = strpos($body, 'name="search"') !== false;
            $hasJavaScript = strpos($body, 'main.js') !== false;
            $hasErrors = strpos($body, 'Fatal error') !== false || strpos($body, 'Warning') !== false;
            
            echo "<p>Contains status dropdown: " . ($hasDropdown ? '✅ Yes' : '❌ No') . "</p>";
            echo "<p>Contains search input: " . ($hasSearch ? '✅ Yes' : '❌ No') . "</p>";
            echo "<p>Contains JavaScript: " . ($hasJavaScript ? '✅ Yes' : '❌ No') . "</p>";
            echo "<p>Contains PHP errors: " . ($hasErrors ? '❌ Yes' : '✅ No') . "</p>";
        } else {
            echo "<p class='error'>❌ HTTP Error: $httpCode</p>";
            if (strlen($body) > 0) {
                echo "<div class='code-block'><strong>Error Response:</strong><br>" . htmlspecialchars(substr($body, 0, 1000)) . "</div>";
            }
        }
    }
} else {
    echo "<p class='warning'>⚠️ cURL not available for HTTP testing</p>";
}
echo "</div>";

// 4. JavaScript Dependencies Test
echo "<div class='diagnostic-section'>";
echo "<h2>4. JavaScript Dependencies Test</h2>";

$jsFiles = [
    'admin/assets/js/plugins.js',
    'admin/assets/js/main.js'
];

foreach ($jsFiles as $jsFile) {
    $fullPath = $rootDir . '/' . $jsFile;
    if (file_exists($fullPath)) {
        $size = filesize($fullPath);
        $content = file_get_contents($fullPath);
        
        echo "<p class='success'>✅ $jsFile exists ($size bytes)</p>";
        
        // Check for specific JavaScript functions
        $hasMetisMenu = strpos($content, 'metisMenu') !== false;
        $hasMenuParent = strpos($content, 'menu-active-parent') !== false;
        $hasSlideDown = strpos($content, 'slide-down') !== false;
        
        echo "<p>Contains metisMenu: " . ($hasMetisMenu ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p>Contains menu-active-parent: " . ($hasMenuParent ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p>Contains slide-down: " . ($hasSlideDown ? '✅ Yes' : '❌ No') . "</p>";
    } else {
        echo "<p class='error'>❌ $jsFile not found</p>";
    }
}
echo "</div>";

// 5. Database Connection Test
echo "<div class='diagnostic-section'>";
echo "<h2>5. Database Connection Test</h2>";

try {
    require_once $rootDir . '/bootstrap.php';
    echo "<p class='success'>✅ Bootstrap loaded successfully</p>";
    
    if (function_exists('is_mongodb_available')) {
        $mongoAvailable = is_mongodb_available();
        echo "<p class='success'>✅ MongoDB check function available</p>";
        echo "<p>MongoDB status: " . ($mongoAvailable ? '✅ Connected' : '❌ Not available') . "</p>";
    }
    
    // Test category repository
    require_once $rootDir . '/components/admin/index.php';
    require_once $rootDir . '/components/admin/categories/index.php';
    
    if (class_exists('CategoryRepository')) {
        $repo = new CategoryRepository();
        echo "<p class='success'>✅ CategoryRepository created</p>";
        
        $service = new CategoryService($repo);
        echo "<p class='success'>✅ CategoryService created</p>";
        
        $statusOptions = $service->getStatusOptions();
        echo "<p class='success'>✅ Status options retrieved: " . count($statusOptions) . " options</p>";
        
        // Test pagination
        $pagination = $service->paginateCategories(1, 5);
        echo "<p class='success'>✅ Pagination test: " . count($pagination['data']) . " categories found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p class='error'>❌ Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// 6. Interactive Dropdown Test
echo "<div class='diagnostic-section'>";
echo "<h2>6. Interactive Dropdown Test</h2>";
echo "<p>Test the dropdown functionality below:</p>";

echo "<div class='test-dropdown'>";
echo "<h4>Status Filter Dropdown Test</h4>";
echo "<select id='testStatusDropdown' onchange='testDropdownChange(this)'>";
echo "<option value=''>All Statuses</option>";
echo "<option value='active'>Active</option>";
echo "<option value='inactive'>Inactive</option>";
echo "<option value='draft'>Draft</option>";
echo "</select>";
echo "<p id='statusResult'>Select an option to test...</p>";
echo "</div>";

echo "<div class='test-dropdown'>";
echo "<h4>Search Input Test</h4>";
echo "<input type='text' id='testSearchInput' placeholder='Type to test search...' oninput='testSearchInput(this)'>";
echo "<p id='searchResult'>Type something to test...</p>";
echo "</div>";

echo "<div class='test-dropdown'>";
echo "<h4>Navigation Menu Test</h4>";
echo "<button onclick='testNavigationMenu()'>Test Categories Menu Toggle</button>";
echo "<p id='navResult'>Click button to test...</p>";
echo "</div>";

echo "</div>";

// 7. Browser Console Error Check
echo "<div class='diagnostic-section'>";
echo "<h2>7. Browser Console Error Check</h2>";
echo "<p>Check your browser's developer console (F12) for JavaScript errors.</p>";
echo "<p>Common errors to look for:</p>";
echo "<ul>";
echo "<li>404 errors for missing JavaScript/CSS files</li>";
echo "<li>JavaScript syntax errors</li>";
echo "<li>jQuery not defined errors</li>";
echo "<li>MetisMenu initialization errors</li>";
echo "</ul>";
echo "<button onclick='checkConsoleErrors()'>Check for Console Errors</button>";
echo "<div id='consoleErrors'></div>";
echo "</div>";

// JavaScript for testing
echo "<script>";
echo "function testDropdownChange(select) {";
echo "  document.getElementById('statusResult').innerHTML = 'Selected: ' + select.value + ' ✅';";
echo "}";

echo "function testSearchInput(input) {";
echo "  document.getElementById('searchResult').innerHTML = 'Typed: \"' + input.value + '\" ✅';";
echo "}";

echo "function testNavigationMenu() {";
echo "  var result = document.getElementById('navResult');";
echo "  if (typeof $ !== 'undefined') {";
echo "    result.innerHTML = '✅ jQuery is available';";
echo "    if (typeof $.fn.metisMenu !== 'undefined') {";
echo "      result.innerHTML += '<br>✅ MetisMenu plugin is available';";
echo "    } else {";
echo "      result.innerHTML += '<br>❌ MetisMenu plugin NOT available';";
echo "    }";
echo "  } else {";
echo "    result.innerHTML = '❌ jQuery is NOT available';";
echo "  }";
echo "}";

echo "function checkConsoleErrors() {";
echo "  var errorDiv = document.getElementById('consoleErrors');";
echo "  errorDiv.innerHTML = '<p>Check your browser console (F12 → Console tab) for any red error messages.</p>';";
echo "  console.log('🔧 Dropdown Diagnostic: Checking for errors...');";
echo "  console.log('jQuery available:', typeof $ !== 'undefined');";
echo "  console.log('MetisMenu available:', typeof $.fn.metisMenu !== 'undefined');";
echo "}";

echo "// Auto-run some checks";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "  console.log('🔧 Dropdown Diagnostic Tool loaded');";
echo "  setTimeout(function() {";
echo "    testNavigationMenu();";
echo "  }, 1000);";
echo "});";
echo "</script>";

echo "<div class='diagnostic-section'>";
echo "<h2>8. Quick Action Links</h2>";
echo "<p><a href='categories.php' target='_blank'>🔗 Open Categories Page</a></p>";
echo "<p><a href='categories-safe.php' target='_blank'>🔗 Open Safe Categories Page</a></p>";
echo "<p><a href='../mongodb-demo.php' target='_blank'>🔗 Test MongoDB Demo</a></p>";
echo "<p><a href='test-web-access.php' target='_blank'>🔗 Web Access Test</a></p>";
echo "</div>";

echo "</body></html>";
?>
