<?php
// Minimal categories page for debugging 500 errors
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set headers first
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html><html><head><title>Categories - Minimal Test</title></head><body>";
echo "<h1>Categories Page - Minimal Test</h1>";

try {
    echo "<p>✅ Step 1: Basic PHP execution working</p>";
    
    // Test path resolution
    $rootDir = dirname(__DIR__);
    echo "<p>✅ Step 2: Root directory: $rootDir</p>";
    
    // Test file existence
    $bootstrapPath = $rootDir . '/bootstrap.php';
    if (file_exists($bootstrapPath)) {
        echo "<p>✅ Step 3: Bootstrap file exists</p>";
    } else {
        echo "<p>❌ Step 3: Bootstrap file NOT found at: $bootstrapPath</p>";
        
        // Try alternative paths
        $altPaths = [
            __DIR__ . '/../bootstrap.php',
            './bootstrap.php',
            '../bootstrap.php'
        ];
        
        foreach ($altPaths as $altPath) {
            if (file_exists($altPath)) {
                echo "<p>✅ Found bootstrap at: $altPath</p>";
                $bootstrapPath = $altPath;
                break;
            }
        }
    }
    
    // Try to include bootstrap
    if (file_exists($bootstrapPath)) {
        echo "<p>⏳ Step 4: Including bootstrap...</p>";
        require_once $bootstrapPath;
        echo "<p>✅ Step 4: Bootstrap included successfully</p>";
    } else {
        throw new Exception("Cannot find bootstrap.php");
    }
    
    // Test admin components
    $adminIndexPath = $rootDir . '/components/admin/index.php';
    if (file_exists($adminIndexPath)) {
        echo "<p>⏳ Step 5: Including admin components...</p>";
        require_once $adminIndexPath;
        echo "<p>✅ Step 5: Admin components included</p>";
    } else {
        throw new Exception("Cannot find admin components");
    }
    
    // Test category components
    $categoryIndexPath = $rootDir . '/components/admin/categories/index.php';
    if (file_exists($categoryIndexPath)) {
        echo "<p>⏳ Step 6: Including category components...</p>";
        require_once $categoryIndexPath;
        echo "<p>✅ Step 6: Category components included</p>";
    } else {
        throw new Exception("Cannot find category components");
    }
    
    // Test class instantiation
    if (class_exists('CategoryRepository')) {
        echo "<p>⏳ Step 7: Creating CategoryRepository...</p>";
        $repository = new CategoryRepository();
        echo "<p>✅ Step 7: CategoryRepository created</p>";
        
        if (class_exists('CategoryService')) {
            echo "<p>⏳ Step 8: Creating CategoryService...</p>";
            $service = new CategoryService($repository);
            echo "<p>✅ Step 8: CategoryService created</p>";
            
            // Test basic functionality
            echo "<p>⏳ Step 9: Testing basic functionality...</p>";
            $statusOptions = $service->getStatusOptions();
            echo "<p>✅ Step 9: Status options retrieved (" . count($statusOptions) . " options)</p>";
            
            $pagination = $service->paginateCategories(1, 5);
            echo "<p>✅ Step 10: Pagination test completed (" . count($pagination['data']) . " categories)</p>";
        }
    }
    
    echo "<h2>🎉 All tests passed! The issue is not in the core functionality.</h2>";
    echo "<p>The 500 error might be caused by:</p>";
    echo "<ul>";
    echo "<li>Output buffering issues</li>";
    echo "<li>Header conflicts</li>";
    echo "<li>Session management</li>";
    echo "<li>Large HTML output</li>";
    echo "</ul>";
    
    echo "<h3>Quick Links:</h3>";
    echo "<p><a href='categories.php'>🔗 Try Original Categories Page</a></p>";
    echo "<p><a href='dropdown-diagnostic.php'>🔧 Run Full Diagnostic</a></p>";
    echo "<p><a href='test-dropdown-js.html'>🧪 Test JavaScript Functionality</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<p>❌ Fatal Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}

echo "</body></html>";
?>
