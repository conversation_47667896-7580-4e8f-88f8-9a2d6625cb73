<?php
require_once __DIR__ . '/../bootstrap.php';

// Handle product operations
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'delete' && !empty($_POST['product_id'])) {
        try {
            $productRepo = getProductRepository();
            $success = $productRepo->deleteProduct($_POST['product_id']);
            
            if ($success) {
                $message = 'Product deleted successfully!';
                $messageType = 'success';
            } else {
                $message = 'Failed to delete product.';
                $messageType = 'error';
            }
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$status = $_GET['status'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 10;

// Build filters
$filters = [];
if (!empty($search)) {
    $filters['search'] = $search;
}
if (!empty($category)) {
    $filters['category_id'] = $category;
}
if (!empty($status)) {
    $filters['status'] = $status;
}

// Get products and categories
try {
    $productRepo = getProductRepository();
    $categoryRepo = getCategoryRepository();
    
    $products = $productRepo->find($filters, [
        'limit' => $limit,
        'skip' => ($page - 1) * $limit,
        'sort' => ['created_at' => -1]
    ]);
    
    $totalProducts = $productRepo->count($filters);
    $totalPages = ceil($totalProducts / $limit);
    
    $categories = $categoryRepo->getActiveCategories();
    
} catch (Exception $e) {
    $message = 'Database error: ' . $e->getMessage();
    $messageType = 'error';
    $products = [];
    $categories = [];
    $totalProducts = 0;
    $totalPages = 1;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RC Furnishing Admin – Products (MongoDB)</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
    <link rel="stylesheet" href="assets/css/plugins.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .product-page .product-card {
            padding: 24px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 12px 30px rgba(35, 46, 60, 0.08);
        }
        .product-page__filters {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .product-page__filters input[type="text"],
        .product-page__filters select {
            min-width: 160px;
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid #E2E8F0;
        }
        .product-page__alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-weight: 500;
        }
        .product-page__alert--success {
            color: #1b5e20;
            background: #e8f5e9;
        }
        .product-page__alert--error {
            color: #d32f2f;
            background: #fdecea;
        }
        .product-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }
        .price {
            font-weight: bold;
            color: #2e7d32;
        }
        .stock-low {
            color: #d32f2f;
        }
        .stock-good {
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="rcf_dashboard">
        <!-- Sidebar (simplified for demo) -->
        <div class="sidebar_left">
            <a href="index.php" class="logo">
                <img src="assets/images/logo/logo.svg" alt="logo" height="32" width="131">
            </a>
            <ul class="rts-side-nav-area-left">
                <li><a href="index.php">Dashboard</a></li>
                <li><a href="categories.php">Categories</a></li>
                <li><a href="mongodb-products.php" class="active">Products (MongoDB)</a></li>
                <li><a href="add-product.php">Add Product</a></li>
            </ul>
        </div>

        <div class="right-area-body-content">
            <div class="body-root-inner">
                <div class="transection product-page">
                    <div class="row g-5 align-items-start">
                        <div class="col-12">
                            <div class="product-card mb--20">
                                <div class="title-right-actioin-btn-wrapper-product-list">
                                    <h3 class="title">Products (MongoDB)</h3>
                                    <a href="add-product.php" class="rts-btn btn-primary">+ Add Product</a>
                                </div>

                                <?php if ($message): ?>
                                    <div class="product-page__alert product-page__alert--<?= $messageType ?>">
                                        <?= htmlspecialchars($message) ?>
                                    </div>
                                <?php endif; ?>

                                <!-- Filters -->
                                <form method="get" class="product-page__filters">
                                    <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="Search products...">
                                    
                                    <select name="category">
                                        <option value="">All Categories</option>
                                        <?php foreach ($categories as $cat): ?>
                                            <option value="<?= htmlspecialchars($cat['id']) ?>" <?= $category === $cat['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($cat['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    
                                    <select name="status">
                                        <option value="">All Statuses</option>
                                        <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                                        <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        <option value="draft" <?= $status === 'draft' ? 'selected' : '' ?>>Draft</option>
                                    </select>
                                    
                                    <button type="submit" class="rts-btn btn-primary">Filter</button>
                                    <a href="mongodb-products.php" class="rts-btn btn-secondary">Clear</a>
                                </form>

                                <!-- Products Table -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Category</th>
                                                <th>Price</th>
                                                <th>Stock</th>
                                                <th>Status</th>
                                                <th>Featured</th>
                                                <th class="text-end">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($products)): ?>
                                                <tr>
                                                    <td colspan="7" class="text-center">
                                                        <?= empty($message) ? 'No products found.' : 'Unable to load products.' ?>
                                                    </td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($products as $product): ?>
                                                    <tr>
                                                        <td>
                                                            <div style="display: flex; align-items: center; gap: 12px;">
                                                                <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #999;">
                                                                    📦
                                                                </div>
                                                                <div>
                                                                    <strong><?= htmlspecialchars($product['name']) ?></strong>
                                                                    <br>
                                                                    <small style="color: #666;"><?= htmlspecialchars($product['slug'] ?? '') ?></small>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <?php
                                                            $categoryName = 'Unknown';
                                                            foreach ($categories as $cat) {
                                                                if ($cat['id'] === ($product['category_id'] ?? '')) {
                                                                    $categoryName = $cat['name'];
                                                                    break;
                                                                }
                                                            }
                                                            echo htmlspecialchars($categoryName);
                                                            ?>
                                                        </td>
                                                        <td class="price">$<?= number_format($product['price'] ?? 0, 2) ?></td>
                                                        <td>
                                                            <?php
                                                            $stock = $product['stock_quantity'] ?? 0;
                                                            $stockClass = $stock < 10 ? 'stock-low' : 'stock-good';
                                                            ?>
                                                            <span class="<?= $stockClass ?>"><?= $stock ?></span>
                                                        </td>
                                                        <td>
                                                            <span style="display:inline-block; padding:4px 10px; border-radius:999px; font-size:12px; text-transform:capitalize; 
                                                                background:<?= ($product['status'] ?? 'draft') === 'active' ? '#e8f5e9' : '#f5f5f5' ?>; 
                                                                color:<?= ($product['status'] ?? 'draft') === 'active' ? '#1b5e20' : '#666' ?>;">
                                                                <?= htmlspecialchars($product['status'] ?? 'draft') ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <?= !empty($product['featured']) ? '⭐ Yes' : 'No' ?>
                                                        </td>
                                                        <td class="text-end">
                                                            <a class="rts-btn btn-primary bg-transparent" href="add-product.php?action=edit&id=<?= urlencode($product['id']) ?>">Edit</a>
                                                            <button class="rts-btn btn-danger bg-transparent" onclick="deleteProduct('<?= htmlspecialchars($product['id']) ?>', '<?= htmlspecialchars($product['name']) ?>')">Delete</button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if ($totalPages > 1): ?>
                                    <div class="pagination" style="display:flex; justify-content:space-between; align-items:center; margin-top:20px;">
                                        <div>
                                            Showing <?= count($products) ?> of <?= $totalProducts ?> products (Page <?= $page ?> of <?= $totalPages ?>)
                                        </div>
                                        <div style="display:flex; gap:8px;">
                                            <?php if ($page > 1): ?>
                                                <a class="rts-btn btn-primary bg-transparent" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>">Prev</a>
                                            <?php endif; ?>
                                            
                                            <?php if ($page < $totalPages): ?>
                                                <a class="rts-btn btn-primary bg-transparent" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&status=<?= urlencode($status) ?>">Next</a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete confirmation -->
    <form id="deleteForm" method="post" style="display: none;">
        <input type="hidden" name="action" value="delete">
        <input type="hidden" name="product_id" id="deleteProductId">
    </form>

    <script>
        function deleteProduct(productId, productName) {
            if (confirm('Are you sure you want to delete "' + productName + '"? This action cannot be undone.')) {
                document.getElementById('deleteProductId').value = productId;
                document.getElementById('deleteForm').submit();
            }
        }
    </script>
</body>
</html>
