<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown JavaScript Test</title>
    <link rel="stylesheet" href="assets/css/plugins.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
        .test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { color: #28a745; } .error { color: #dc3545; } .warning { color: #ffc107; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 4px; background: #f8f9fa; }
        .sidebar-test { width: 250px; background: #2c3e50; color: white; padding: 20px; border-radius: 8px; }
        .dropdown-test { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>🔧 Dropdown JavaScript Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. JavaScript Library Loading Test</h2>
        <div id="js-loading-results">
            <p>Testing JavaScript libraries...</p>
        </div>
        <button class="btn btn-primary" onclick="testJSLibraries()">Re-test Libraries</button>
    </div>

    <div class="test-section">
        <h2>2. MetisMenu Sidebar Test</h2>
        <div class="sidebar-test">
            <ul class="rts-side-nav-area-left menu-active-parent" id="test-sidebar">
                <li class="single-menu-item">
                    <a href="#" class="with-plus">
                        <span>📁</span>
                        <p>Categories</p>
                    </a>
                    <ul class="submenu mm-collapse parent-nav">
                        <li><a href="#" class="mobile-menu-link">Category List</a></li>
                        <li><a href="#" class="mobile-menu-link">Add Category</a></li>
                    </ul>
                </li>
                <li class="single-menu-item">
                    <a href="#" class="with-plus">
                        <span>📦</span>
                        <p>Products</p>
                    </a>
                    <ul class="submenu mm-collapse parent-nav">
                        <li><a href="#" class="mobile-menu-link">Product List</a></li>
                        <li><a href="#" class="mobile-menu-link">Add Product</a></li>
                    </ul>
                </li>
            </ul>
        </div>
        <div class="test-result" id="metismenu-result">
            <p>Click on the menu items above to test MetisMenu functionality</p>
        </div>
        <button class="btn btn-success" onclick="initializeMetisMenu()">Initialize MetisMenu</button>
    </div>

    <div class="test-section">
        <h2>3. Slide-down Dropdown Test</h2>
        <div class="dropdown-test">
            <div class="single_action__haeader">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path d="M10 12L6 8H14L10 12Z" fill="currentColor"/>
                </svg>
                <span>Click me for dropdown</span>
                <div class="slide-down__click" style="display: none; background: white; border: 1px solid #ddd; padding: 10px; margin-top: 10px; border-radius: 4px;">
                    <p>✅ Slide-down dropdown is working!</p>
                    <p>This dropdown should toggle when you click the arrow above.</p>
                </div>
            </div>
        </div>
        <div class="test-result" id="slidedown-result">
            <p>Click the arrow above to test slide-down functionality</p>
        </div>
    </div>

    <div class="test-section">
        <h2>4. Form Dropdown Test</h2>
        <div class="dropdown-test">
            <label for="test-status-select">Status Filter:</label>
            <select id="test-status-select" name="status" class="mySelect" onchange="testFormDropdown(this)">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="draft">Draft</option>
            </select>
        </div>
        <div class="test-result" id="form-dropdown-result">
            <p>Select an option from the dropdown above</p>
        </div>
    </div>

    <div class="test-section">
        <h2>5. Console Error Check</h2>
        <div class="test-result" id="console-errors">
            <p>Check browser console for errors...</p>
        </div>
        <button class="btn btn-danger" onclick="checkConsoleErrors()">Check Console</button>
    </div>

    <div class="test-section">
        <h2>6. Network Request Test</h2>
        <div class="test-result" id="network-test">
            <p>Testing network requests...</p>
        </div>
        <button class="btn btn-primary" onclick="testNetworkRequest()">Test Categories Page Request</button>
    </div>

    <!-- Load JavaScript in correct order -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/plugins.js"></script>
    <script src="assets/js/main.js"></script>

    <script>
        // Test functions
        function testJSLibraries() {
            const results = document.getElementById('js-loading-results');
            let html = '<h4>JavaScript Library Test Results:</h4>';
            
            // Test jQuery
            if (typeof $ !== 'undefined') {
                html += '<p class="success">✅ jQuery loaded (version: ' + $.fn.jquery + ')</p>';
                
                // Test MetisMenu plugin
                if (typeof $.fn.metisMenu !== 'undefined') {
                    html += '<p class="success">✅ MetisMenu plugin loaded</p>';
                } else {
                    html += '<p class="error">❌ MetisMenu plugin NOT loaded</p>';
                }
                
                // Test other jQuery functions
                if (typeof $.fn.slideToggle !== 'undefined') {
                    html += '<p class="success">✅ jQuery slideToggle available</p>';
                } else {
                    html += '<p class="error">❌ jQuery slideToggle NOT available</p>';
                }
            } else {
                html += '<p class="error">❌ jQuery NOT loaded</p>';
            }
            
            // Test rtsJs object
            if (typeof rtsJs !== 'undefined') {
                html += '<p class="success">✅ rtsJs object loaded</p>';
                
                if (typeof rtsJs.metismenu === 'function') {
                    html += '<p class="success">✅ rtsJs.metismenu function available</p>';
                } else {
                    html += '<p class="error">❌ rtsJs.metismenu function NOT available</p>';
                }
            } else {
                html += '<p class="error">❌ rtsJs object NOT loaded</p>';
            }
            
            results.innerHTML = html;
        }

        function initializeMetisMenu() {
            const result = document.getElementById('metismenu-result');
            
            try {
                if (typeof $ !== 'undefined' && typeof $.fn.metisMenu !== 'undefined') {
                    $('.menu-active-parent').metisMenu();
                    result.innerHTML = '<p class="success">✅ MetisMenu initialized successfully! Try clicking the menu items above.</p>';
                    
                    // Add click event listeners to track interactions
                    $('.single-menu-item > a').on('click', function(e) {
                        e.preventDefault();
                        result.innerHTML += '<p class="success">✅ Menu item clicked: ' + $(this).find('p').text() + '</p>';
                    });
                } else {
                    result.innerHTML = '<p class="error">❌ MetisMenu not available for initialization</p>';
                }
            } catch (error) {
                result.innerHTML = '<p class="error">❌ Error initializing MetisMenu: ' + error.message + '</p>';
            }
        }

        function testFormDropdown(select) {
            const result = document.getElementById('form-dropdown-result');
            result.innerHTML = '<p class="success">✅ Form dropdown working! Selected: "' + select.value + '"</p>';
        }

        function checkConsoleErrors() {
            const result = document.getElementById('console-errors');
            
            // Log test messages to console
            console.log('🔧 Testing console functionality...');
            console.log('jQuery available:', typeof $ !== 'undefined');
            console.log('MetisMenu available:', typeof $.fn.metisMenu !== 'undefined');
            console.log('rtsJs available:', typeof rtsJs !== 'undefined');
            
            result.innerHTML = '<p class="warning">⚠️ Check your browser console (F12 → Console) for any red error messages.</p>';
            result.innerHTML += '<p>Look for errors related to:</p>';
            result.innerHTML += '<ul><li>404 errors for missing files</li><li>JavaScript syntax errors</li><li>jQuery or MetisMenu errors</li></ul>';
        }

        function testNetworkRequest() {
            const result = document.getElementById('network-test');
            result.innerHTML = '<p class="warning">⏳ Testing network request...</p>';
            
            fetch('categories.php')
                .then(response => {
                    if (response.ok) {
                        result.innerHTML = '<p class="success">✅ Categories page loads successfully (HTTP ' + response.status + ')</p>';
                        return response.text();
                    } else {
                        throw new Error('HTTP ' + response.status + ' ' + response.statusText);
                    }
                })
                .then(html => {
                    const hasDropdown = html.includes('name="status"');
                    const hasSearch = html.includes('name="search"');
                    const hasJS = html.includes('main.js');
                    
                    result.innerHTML += '<p>Page contains status dropdown: ' + (hasDropdown ? '✅' : '❌') + '</p>';
                    result.innerHTML += '<p>Page contains search input: ' + (hasSearch ? '✅' : '❌') + '</p>';
                    result.innerHTML += '<p>Page includes JavaScript: ' + (hasJS ? '✅' : '❌') + '</p>';
                })
                .catch(error => {
                    result.innerHTML = '<p class="error">❌ Network request failed: ' + error.message + '</p>';
                    result.innerHTML += '<p>This indicates a server-side issue with the categories.php page.</p>';
                });
        }

        // Auto-initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 Dropdown test page loaded');
            
            // Auto-test libraries
            setTimeout(testJSLibraries, 500);
            
            // Initialize slide-down functionality
            setTimeout(function() {
                if (typeof $ !== 'undefined') {
                    $(".single_action__haeader svg").click(function(e) {
                        e.preventDefault();
                        var $popup = $(this).siblings('.slide-down__click');
                        $popup.slideToggle();
                        
                        const result = document.getElementById('slidedown-result');
                        result.innerHTML = '<p class="success">✅ Slide-down dropdown toggled successfully!</p>';
                    });
                }
            }, 1000);
        });
    </script>
</body>
</html>
