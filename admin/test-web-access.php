<?php
// Simple test file to verify web server access
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html><html><head><title>Web Access Test</title></head><body>";
echo "<h1>🔧 Web Server Access Test</h1>";

echo "<h2>1. Basic PHP Info</h2>";
echo "<p>✅ PHP is working!</p>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

echo "<h2>2. File System Access</h2>";
$currentDir = __DIR__;
echo "<p>Current Directory: $currentDir</p>";

$rootDir = dirname(__DIR__);
echo "<p>Root Directory: $rootDir</p>";

$testFiles = [
    'bootstrap.php',
    'components/admin/index.php',
    'components/admin/categories/index.php',
    'mongodb-demo.php'
];

foreach ($testFiles as $file) {
    $fullPath = $rootDir . '/' . $file;
    $exists = file_exists($fullPath);
    $readable = $exists && is_readable($fullPath);
    echo "<p>$file: " . ($exists ? '✅ Exists' : '❌ Missing') . 
         ($readable ? ' & Readable' : ($exists ? ' but NOT Readable' : '')) . "</p>";
}

echo "<h2>3. MongoDB Connection Test</h2>";
try {
    require_once $rootDir . '/bootstrap.php';
    echo "<p>✅ Bootstrap loaded successfully</p>";
    
    if (function_exists('is_mongodb_available')) {
        $mongoAvailable = is_mongodb_available();
        echo "<p>MongoDB: " . ($mongoAvailable ? '✅ Connected' : '❌ Not Available') . "</p>";
    } else {
        echo "<p>⚠️ MongoDB check function not available</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Bootstrap Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>4. Categories Components Test</h2>";
try {
    require_once $rootDir . '/components/admin/index.php';
    require_once $rootDir . '/components/admin/categories/index.php';
    
    if (class_exists('CategoryRepository')) {
        echo "<p>✅ CategoryRepository class available</p>";
        $repo = new CategoryRepository();
        echo "<p>✅ CategoryRepository instance created</p>";
        
        if (class_exists('CategoryService')) {
            echo "<p>✅ CategoryService class available</p>";
            $service = new CategoryService($repo);
            echo "<p>✅ CategoryService instance created</p>";
        }
    }
} catch (Exception $e) {
    echo "<p>❌ Components Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>5. Quick Links</h2>";
echo "<p><a href='categories.php'>🔗 Test Original Categories Page</a></p>";
echo "<p><a href='categories-safe.php'>🔗 Test Safe Categories Page</a></p>";
echo "<p><a href='../mongodb-demo.php'>🔗 Test MongoDB Demo</a></p>";
echo "<p><a href='../debug-500-error.php'>🔧 Run Full Diagnostic</a></p>";

echo "<h2>6. Server Environment</h2>";
echo "<p>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";
echo "<p>Document Root: " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Not set') . "</p>";
echo "<p>Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "</p>";
echo "<p>Script Name: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";

echo "</body></html>";
?>
