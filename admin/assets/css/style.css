/*=================Scss Indexing=============
1.variables
2.typography
3.spacing
4.reset
5.forms
6.mixins
7.shortcode
8.animations
9.text-animation
10.sal
11.header
12.mobile-menu
13.button
14.nav
15.banner
16.swiper
17.funfacts
18.cta
19.about
20.common
21.service
22.projects
23.working-process
24.blog
25.blog-details
26.footer
27.search-input
28./side-bar
29.team
30.testimonials
31.faq
32.pricing
33.date-picker
34.time-picker
35.appoinment
36.awesome-feedback
37.contact
38.pre-loader.scss
39.back-to-top
40. Print Css



==============================================  */
/* Default  */
@import url("https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
:root {
  --color-primary: #629D23;
  --color-secondary: #1F1F25;
  --color-body: #6E777D;
  --color-heading-1: #2C3C28;
  --color-white:#fff;
  --color-success: #3EB75E;
  --color-danger: #DC2626;
  --color-warning: #FF8F3C;
  --color-info: #1BA2DB;
  --color-facebook: #3B5997;
  --color-twitter: #1BA1F2;
  --color-youtube: #ED4141;
  --color-linkedin: #0077B5;
  --color-pinterest: #E60022;
  --color-instagram: #C231A1;
  --color-vimeo: #00ADEF;
  --color-twitch: #6441A3;
  --color-discord: #7289da;
  --p-light: 300;
  --p-regular: 400;
  --p-medium: 500;
  --p-semi-bold: 600;
  --p-bold: 700;
  --p-extra-bold: 800;
  --p-black: 900;
  --s-light: 300;
  --s-regular: 400;
  --s-medium: 500;
  --s-semi-bold: 600;
  --s-bold: 700;
  --s-extra-bold: 800;
  --s-black: 900;
  --transition: 0.3s;
  --font-primary: "Barlow", sans-serif;
  --font-secondary: "Barlow", sans-serif;
  --font-three: "FontAwesome";
  --font-size-b1: 16px;
  --font-size-b2: 16px;
  --font-size-b3: 14px;
  --line-height-b1: 1.3;
  --line-height-b2: 1.3;
  --line-height-b3: 1.3;
  --h1: 60px;
  --h2: 30px;
  --h3: 26px;
  --h4: 18px;
  --h5: 16px;
  --h6: 15px;
}

* {
  box-sizing: border-box;
}

html {
  margin: 0;
  padding: 0;
  font-size: 10px;
  overflow: hidden;
  overflow-y: auto;
  scroll-behavior: auto !important;
}

body {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: "Barlow", sans-serif;
  color: var(--color-body);
  font-weight: var(--p-regular);
  position: relative;
  overflow-x: hidden;
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  body {
    overflow-x: hidden;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  body {
    overflow-x: hidden;
  }
}
@media only screen and (max-width: 767px) {
  body {
    overflow-x: hidden;
  }
}
body::before {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  opacity: 0.05;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
address,
p,
pre,
blockquote,
menu,
ol,
ul,
table,
hr {
  margin: 0;
  margin-bottom: 20px;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  word-break: break-word;
  font-family: "Barlow", sans-serif;
  line-height: 1.4074;
  color: var(--color-heading-1);
}

h1,
.h1 {
  font-size: var(--h1);
  line-height: 1.1;
  font-weight: 700;
}

h2,
.h2 {
  font-size: var(--h2);
  line-height: 1.23;
}

h3,
.h3 {
  font-size: var(--h3);
  line-height: 54px;
}

h4,
.h4 {
  font-size: var(--h4);
  line-height: 1.25;
}

h5,
.h5 {
  font-size: var(--h5);
  line-height: 1.24;
}

h6,
.h6 {
  font-size: var(--h6);
  line-height: 1.25;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
.h1 a,
.h2 a,
.h3 a,
.h4 a,
.h5 a,
.h6 a {
  color: inherit;
}

.bg-color-tertiary h1,
.bg-color-tertiary h2,
.bg-color-tertiary h3,
.bg-color-tertiary h4,
.bg-color-tertiary h5,
.bg-color-tertiary h6,
.bg-color-tertiary .h1,
.bg-color-tertiary .h2,
.bg-color-tertiary .h3,
.bg-color-tertiary .h4,
.bg-color-tertiary .h5,
.bg-color-tertiary .h6 {
  color: #fff;
}
.bg-color-tertiary p {
  color: #6c7279;
}
.bg-color-tertiary a {
  color: #6c7279;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  h1,
  .h1 {
    font-size: 38px;
  }
  h2,
  .h2 {
    font-size: 32px;
  }
  h3,
  .h3 {
    font-size: 28px;
  }
  h4,
  .h4 {
    font-size: 24px;
  }
  h5,
  .h5 {
    font-size: 18px;
  }
}
@media only screen and (max-width: 767px) {
  h1,
  .h1 {
    font-size: 34px;
  }
  h2,
  .h2 {
    font-size: 28px;
  }
  h3,
  .h3 {
    font-size: 24px;
  }
  h4,
  .h4 {
    font-size: 20px;
  }
  h5,
  .h5 {
    font-size: 20px;
  }
}
h1,
.h1,
h2,
.h2,
h3,
.h3 {
  font-weight: var(--s-bold);
}

h4,
.h4,
h5,
.h5 {
  font-weight: var(--s-bold);
}

h6,
.h6 {
  font-weight: var(--s-bold);
}

p {
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b2);
  font-weight: var(--p-regular);
  color: var(--color-body);
  margin: 0 0 40px;
}
@media only screen and (max-width: 767px) {
  p {
    margin: 0 0 20px;
    font-size: 16px;
    line-height: 28px;
  }
}
p.b1 {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
}
p.b3 {
  font-size: var(--font-size-b3);
  line-height: var(--line-height-b3);
}
p.has-large-font-size {
  line-height: 1.5;
  font-size: 36px;
}
p.has-medium-font-size {
  font-size: 24px;
  line-height: 36px;
}
p.has-small-font-size {
  font-size: 13px;
}
p.has-very-light-gray-color {
  color: var(--color-white);
}
p.has-background {
  padding: 20px 30px;
}
p.b1 {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
}
p.b2 {
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b2);
}
p.b3 {
  font-size: var(--font-size-b3);
  line-height: var(--line-height-b3);
}
p:last-child {
  margin-bottom: 0;
}

.b1 {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
}

.b2 {
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b2);
}

.b3 {
  font-size: var(--font-size-b3);
  line-height: var(--line-height-b3);
}

.b4 {
  font-size: var(--font-size-b4);
  line-height: var(--line-height-b4);
}

table {
  border-collapse: collapse;
  border-spacing: 0;
  margin: 0 0 20px;
  width: 100%;
}

table a,
table a:link,
table a:visited {
  text-decoration: none;
}

cite,
.wp-block-pullquote cite,
.wp-block-pullquote.is-style-solid-color blockquote cite,
.wp-block-quote cite {
  color: var(--color-heading);
}

var {
  font-family: "Exo", sans-serif;
}

/*---------------------------
	List Style 
---------------------------*/
ul,
ol {
  padding-left: 18px;
}

ul {
  list-style: square;
  margin-bottom: 30px;
  padding-left: 20px;
}
ul.liststyle.bullet li {
  font-size: 18px;
  line-height: 30px;
  color: var(--color-body);
  position: relative;
  padding-left: 30px;
}
@media only screen and (max-width: 767px) {
  ul.liststyle.bullet li {
    padding-left: 19px;
  }
}
ul.liststyle.bullet li::before {
  position: absolute;
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 100%;
  background: var(--color-body);
  left: 0;
  top: 10px;
}
ul.liststyle.bullet li + li {
  margin-top: 8px;
}
ul li {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
  margin-top: 10px;
  margin-bottom: 10px;
  color: var(--color-body);
}
ul li a {
  text-decoration: none;
  color: var(--color-gray);
}
ul li a:hover {
  color: var(--color-primary);
}
ul ul {
  margin-bottom: 0;
}

ol {
  margin-bottom: 30px;
}
ol li {
  font-size: var(--font-size-b1);
  line-height: var(--line-height-b1);
  color: var(--color-body);
  margin-top: 10px;
  margin-bottom: 10px;
}
ol li a {
  color: var(--color-heading);
  text-decoration: none;
}
ol li a:hover {
  color: var(--color-primary);
}
ol ul {
  padding-left: 30px;
}

.typo-title-area .title {
  margin-top: 0;
}

.paragraph-area p.disc {
  margin-bottom: 20px;
  color: #fff;
}

@media only screen and (max-width: 1199px) {
  h1 {
    font-size: 64px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  h1 {
    font-size: 54px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  h1 {
    font-size: 40px;
    line-height: 56px;
  }
}
@media only screen and (max-width: 767px) {
  h1 {
    font-size: 30px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 575px) {
  h1 {
    font-size: 28px;
    line-height: 36px;
  }
}
@media only screen and (max-width: 479px) {
  h1 {
    font-size: 26px;
    line-height: 1.3;
  }
}

@media only screen and (max-width: 1199px) {
  h2 {
    font-size: 54px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  h2 {
    font-size: 44px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  h2 {
    font-size: 36px;
    line-height: 56px;
  }
}
@media only screen and (max-width: 767px) {
  h2 {
    font-size: 30px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 575px) {
  h2 {
    font-size: 26px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 479px) {
  h2 {
    font-size: 24px;
    line-height: 1.3;
  }
}

@media only screen and (max-width: 1199px) {
  h3 {
    font-size: 40px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  h3 {
    font-size: 36px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  h3 {
    font-size: 30px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 767px) {
  h3 {
    font-size: 30px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 575px) {
  h3 {
    font-size: 24px;
    line-height: 1.3;
  }
}
@media only screen and (max-width: 479px) {
  h3 {
    font-size: 22px;
    line-height: 1.3;
  }
}

/*=========================
    Section Separation 
==========================*/
.rts-section-gap {
  padding: 60px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap {
    padding: 60px 0;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap {
    padding: 60px 0;
  }
}

.rts-section-gapBottom {
  padding-bottom: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gapBottom {
    padding-bottom: 60px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gapBottom {
    padding-bottom: 60px;
  }
}

.rts-section-gapTop {
  padding-top: 60px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gapTop {
    padding-top: 70px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gapTop {
    padding-top: 60px;
  }
}

.rts-section-gap2 {
  padding: 100px 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap2 {
    padding: 100px 0;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap2 {
    padding: 100px 0;
  }
}

.rts-section-gap2Bottom {
  padding-bottom: 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap2Bottom {
    padding-bottom: 100px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap2Bottom {
    padding-bottom: 100px;
  }
}

.rts-section-gap2Top {
  padding-top: 100px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rts-section-gap2Top {
    padding-top: 100px;
  }
}
@media only screen and (max-width: 767px) {
  .rts-section-gap2Top {
    padding-top: 100px;
  }
}

.pl--0 {
  padding-left: 0 !important;
}

.pr--0 {
  padding-right: 0 !important;
}

.pt--0 {
  padding-top: 0 !important;
}

.pb--0 {
  padding-bottom: 0 !important;
}

.mr--0 {
  margin-right: 0 !important;
}

.ml--0 {
  margin-left: 0 !important;
}

.mt--0 {
  margin-top: 0 !important;
}

.mb--0 {
  margin-bottom: 0 !important;
}

.ptb--5 {
  padding: 5px 0 !important;
}

.plr--5 {
  padding: 0 5px !important;
}

.pt--5 {
  padding-top: 5px !important;
}

.pb--5 {
  padding-bottom: 5px !important;
}

.pl--5 {
  padding-left: 5px !important;
}

.pr--5 {
  padding-right: 5px !important;
}

.mt--5 {
  margin-top: 5px !important;
}

.mb--5 {
  margin-bottom: 5px !important;
}

.mr--5 {
  margin-right: 5px !important;
}

.ml--5 {
  margin-left: 5px !important;
}

.ptb--10 {
  padding: 10px 0 !important;
}

.plr--10 {
  padding: 0 10px !important;
}

.pt--10 {
  padding-top: 10px !important;
}

.pb--10 {
  padding-bottom: 10px !important;
}

.pl--10 {
  padding-left: 10px !important;
}

.pr--10 {
  padding-right: 10px !important;
}

.mt--10 {
  margin-top: 10px !important;
}

.mb--10 {
  margin-bottom: 10px !important;
}

.mr--10 {
  margin-right: 10px !important;
}

.ml--10 {
  margin-left: 10px !important;
}

.ptb--15 {
  padding: 15px 0 !important;
}

.plr--15 {
  padding: 0 15px !important;
}

.pt--15 {
  padding-top: 15px !important;
}

.pb--15 {
  padding-bottom: 15px !important;
}

.pl--15 {
  padding-left: 15px !important;
}

.pr--15 {
  padding-right: 15px !important;
}

.mt--15 {
  margin-top: 15px !important;
}

.mb--15 {
  margin-bottom: 15px !important;
}

.mr--15 {
  margin-right: 15px !important;
}

.ml--15 {
  margin-left: 15px !important;
}

.ptb--20 {
  padding: 20px 0 !important;
}

.plr--20 {
  padding: 0 20px !important;
}

.pt--20 {
  padding-top: 20px !important;
}

.pb--20 {
  padding-bottom: 20px !important;
}

.pl--20 {
  padding-left: 20px !important;
}

.pr--20 {
  padding-right: 20px !important;
}

.mt--20 {
  margin-top: 20px !important;
}

.mb--20 {
  margin-bottom: 20px !important;
}

.mr--20 {
  margin-right: 20px !important;
}

.ml--20 {
  margin-left: 20px !important;
}

.ptb--25 {
  padding: 25px 0 !important;
}

.plr--25 {
  padding: 0 25px !important;
}

.pt--25 {
  padding-top: 25px !important;
}

.pb--25 {
  padding-bottom: 25px !important;
}

.pl--25 {
  padding-left: 25px !important;
}

.pr--25 {
  padding-right: 25px !important;
}

.mt--25 {
  margin-top: 25px !important;
}

.mb--25 {
  margin-bottom: 25px !important;
}

.mr--25 {
  margin-right: 25px !important;
}

.ml--25 {
  margin-left: 25px !important;
}

.ptb--30 {
  padding: 30px 0 !important;
}

.plr--30 {
  padding: 0 30px !important;
}

.pt--30 {
  padding-top: 30px !important;
}

.pb--30 {
  padding-bottom: 30px !important;
}

.pl--30 {
  padding-left: 30px !important;
}

.pr--30 {
  padding-right: 30px !important;
}

.mt--30 {
  margin-top: 30px !important;
}

.mb--30 {
  margin-bottom: 30px !important;
}

.mr--30 {
  margin-right: 30px !important;
}

.ml--30 {
  margin-left: 30px !important;
}

.ptb--35 {
  padding: 35px 0 !important;
}

.plr--35 {
  padding: 0 35px !important;
}

.pt--35 {
  padding-top: 35px !important;
}

.pb--35 {
  padding-bottom: 35px !important;
}

.pl--35 {
  padding-left: 35px !important;
}

.pr--35 {
  padding-right: 35px !important;
}

.mt--35 {
  margin-top: 35px !important;
}

.mb--35 {
  margin-bottom: 35px !important;
}

.mr--35 {
  margin-right: 35px !important;
}

.ml--35 {
  margin-left: 35px !important;
}

.ptb--40 {
  padding: 40px 0 !important;
}

.plr--40 {
  padding: 0 40px !important;
}

.pt--40 {
  padding-top: 40px !important;
}

.pb--40 {
  padding-bottom: 40px !important;
}

.pl--40 {
  padding-left: 40px !important;
}

.pr--40 {
  padding-right: 40px !important;
}

.mt--40 {
  margin-top: 40px !important;
}

.mb--40 {
  margin-bottom: 40px !important;
}

.mr--40 {
  margin-right: 40px !important;
}

.ml--40 {
  margin-left: 40px !important;
}

.ptb--45 {
  padding: 45px 0 !important;
}

.plr--45 {
  padding: 0 45px !important;
}

.pt--45 {
  padding-top: 45px !important;
}

.pb--45 {
  padding-bottom: 45px !important;
}

.pl--45 {
  padding-left: 45px !important;
}

.pr--45 {
  padding-right: 45px !important;
}

.mt--45 {
  margin-top: 45px !important;
}

.mb--45 {
  margin-bottom: 45px !important;
}

.mr--45 {
  margin-right: 45px !important;
}

.ml--45 {
  margin-left: 45px !important;
}

.ptb--50 {
  padding: 50px 0 !important;
}

.plr--50 {
  padding: 0 50px !important;
}

.pt--50 {
  padding-top: 50px !important;
}

.pb--50 {
  padding-bottom: 50px !important;
}

.pl--50 {
  padding-left: 50px !important;
}

.pr--50 {
  padding-right: 50px !important;
}

.mt--50 {
  margin-top: 50px !important;
}

.mb--50 {
  margin-bottom: 50px !important;
}

.mr--50 {
  margin-right: 50px !important;
}

.ml--50 {
  margin-left: 50px !important;
}

.ptb--55 {
  padding: 55px 0 !important;
}

.plr--55 {
  padding: 0 55px !important;
}

.pt--55 {
  padding-top: 55px !important;
}

.pb--55 {
  padding-bottom: 55px !important;
}

.pl--55 {
  padding-left: 55px !important;
}

.pr--55 {
  padding-right: 55px !important;
}

.mt--55 {
  margin-top: 55px !important;
}

.mb--55 {
  margin-bottom: 55px !important;
}

.mr--55 {
  margin-right: 55px !important;
}

.ml--55 {
  margin-left: 55px !important;
}

.ptb--60 {
  padding: 60px 0 !important;
}

.plr--60 {
  padding: 0 60px !important;
}

.pt--60 {
  padding-top: 60px !important;
}

.pb--60 {
  padding-bottom: 60px !important;
}

.pl--60 {
  padding-left: 60px !important;
}

.pr--60 {
  padding-right: 60px !important;
}

.mt--60 {
  margin-top: 60px !important;
}

.mb--60 {
  margin-bottom: 60px !important;
}

.mr--60 {
  margin-right: 60px !important;
}

.ml--60 {
  margin-left: 60px !important;
}

.ptb--65 {
  padding: 65px 0 !important;
}

.plr--65 {
  padding: 0 65px !important;
}

.pt--65 {
  padding-top: 65px !important;
}

.pb--65 {
  padding-bottom: 65px !important;
}

.pl--65 {
  padding-left: 65px !important;
}

.pr--65 {
  padding-right: 65px !important;
}

.mt--65 {
  margin-top: 65px !important;
}

.mb--65 {
  margin-bottom: 65px !important;
}

.mr--65 {
  margin-right: 65px !important;
}

.ml--65 {
  margin-left: 65px !important;
}

.ptb--70 {
  padding: 70px 0 !important;
}

.plr--70 {
  padding: 0 70px !important;
}

.pt--70 {
  padding-top: 70px !important;
}

.pb--70 {
  padding-bottom: 70px !important;
}

.pl--70 {
  padding-left: 70px !important;
}

.pr--70 {
  padding-right: 70px !important;
}

.mt--70 {
  margin-top: 70px !important;
}

.mb--70 {
  margin-bottom: 70px !important;
}

.mr--70 {
  margin-right: 70px !important;
}

.ml--70 {
  margin-left: 70px !important;
}

.ptb--75 {
  padding: 75px 0 !important;
}

.plr--75 {
  padding: 0 75px !important;
}

.pt--75 {
  padding-top: 75px !important;
}

.pb--75 {
  padding-bottom: 75px !important;
}

.pl--75 {
  padding-left: 75px !important;
}

.pr--75 {
  padding-right: 75px !important;
}

.mt--75 {
  margin-top: 75px !important;
}

.mb--75 {
  margin-bottom: 75px !important;
}

.mr--75 {
  margin-right: 75px !important;
}

.ml--75 {
  margin-left: 75px !important;
}

.ptb--80 {
  padding: 80px 0 !important;
}

.plr--80 {
  padding: 0 80px !important;
}

.pt--80 {
  padding-top: 80px !important;
}

.pb--80 {
  padding-bottom: 80px !important;
}

.pl--80 {
  padding-left: 80px !important;
}

.pr--80 {
  padding-right: 80px !important;
}

.mt--80 {
  margin-top: 80px !important;
}

.mb--80 {
  margin-bottom: 80px !important;
}

.mr--80 {
  margin-right: 80px !important;
}

.ml--80 {
  margin-left: 80px !important;
}

.ptb--85 {
  padding: 85px 0 !important;
}

.plr--85 {
  padding: 0 85px !important;
}

.pt--85 {
  padding-top: 85px !important;
}

.pb--85 {
  padding-bottom: 85px !important;
}

.pl--85 {
  padding-left: 85px !important;
}

.pr--85 {
  padding-right: 85px !important;
}

.mt--85 {
  margin-top: 85px !important;
}

.mb--85 {
  margin-bottom: 85px !important;
}

.mr--85 {
  margin-right: 85px !important;
}

.ml--85 {
  margin-left: 85px !important;
}

.ptb--90 {
  padding: 90px 0 !important;
}

.plr--90 {
  padding: 0 90px !important;
}

.pt--90 {
  padding-top: 90px !important;
}

.pb--90 {
  padding-bottom: 90px !important;
}

.pl--90 {
  padding-left: 90px !important;
}

.pr--90 {
  padding-right: 90px !important;
}

.mt--90 {
  margin-top: 90px !important;
}

.mb--90 {
  margin-bottom: 90px !important;
}

.mr--90 {
  margin-right: 90px !important;
}

.ml--90 {
  margin-left: 90px !important;
}

.ptb--95 {
  padding: 95px 0 !important;
}

.plr--95 {
  padding: 0 95px !important;
}

.pt--95 {
  padding-top: 95px !important;
}

.pb--95 {
  padding-bottom: 95px !important;
}

.pl--95 {
  padding-left: 95px !important;
}

.pr--95 {
  padding-right: 95px !important;
}

.mt--95 {
  margin-top: 95px !important;
}

.mb--95 {
  margin-bottom: 95px !important;
}

.mr--95 {
  margin-right: 95px !important;
}

.ml--95 {
  margin-left: 95px !important;
}

.ptb--100 {
  padding: 100px 0 !important;
}

.plr--100 {
  padding: 0 100px !important;
}

.pt--100 {
  padding-top: 100px !important;
}

.pb--100 {
  padding-bottom: 100px !important;
}

.pl--100 {
  padding-left: 100px !important;
}

.pr--100 {
  padding-right: 100px !important;
}

.mt--100 {
  margin-top: 100px !important;
}

.mb--100 {
  margin-bottom: 100px !important;
}

.mr--100 {
  margin-right: 100px !important;
}

.ml--100 {
  margin-left: 100px !important;
}

.ptb--105 {
  padding: 105px 0 !important;
}

.plr--105 {
  padding: 0 105px !important;
}

.pt--105 {
  padding-top: 105px !important;
}

.pb--105 {
  padding-bottom: 105px !important;
}

.pl--105 {
  padding-left: 105px !important;
}

.pr--105 {
  padding-right: 105px !important;
}

.mt--105 {
  margin-top: 105px !important;
}

.mb--105 {
  margin-bottom: 105px !important;
}

.mr--105 {
  margin-right: 105px !important;
}

.ml--105 {
  margin-left: 105px !important;
}

.ptb--110 {
  padding: 110px 0 !important;
}

.plr--110 {
  padding: 0 110px !important;
}

.pt--110 {
  padding-top: 110px !important;
}

.pb--110 {
  padding-bottom: 110px !important;
}

.pl--110 {
  padding-left: 110px !important;
}

.pr--110 {
  padding-right: 110px !important;
}

.mt--110 {
  margin-top: 110px !important;
}

.mb--110 {
  margin-bottom: 110px !important;
}

.mr--110 {
  margin-right: 110px !important;
}

.ml--110 {
  margin-left: 110px !important;
}

.ptb--115 {
  padding: 115px 0 !important;
}

.plr--115 {
  padding: 0 115px !important;
}

.pt--115 {
  padding-top: 115px !important;
}

.pb--115 {
  padding-bottom: 115px !important;
}

.pl--115 {
  padding-left: 115px !important;
}

.pr--115 {
  padding-right: 115px !important;
}

.mt--115 {
  margin-top: 115px !important;
}

.mb--115 {
  margin-bottom: 115px !important;
}

.mr--115 {
  margin-right: 115px !important;
}

.ml--115 {
  margin-left: 115px !important;
}

.ptb--120 {
  padding: 120px 0 !important;
}

.plr--120 {
  padding: 0 120px !important;
}

.pt--120 {
  padding-top: 120px !important;
}

.pb--120 {
  padding-bottom: 120px !important;
}

.pl--120 {
  padding-left: 120px !important;
}

.pr--120 {
  padding-right: 120px !important;
}

.mt--120 {
  margin-top: 120px !important;
}

.mb--120 {
  margin-bottom: 120px !important;
}

.mr--120 {
  margin-right: 120px !important;
}

.ml--120 {
  margin-left: 120px !important;
}

.ptb--125 {
  padding: 125px 0 !important;
}

.plr--125 {
  padding: 0 125px !important;
}

.pt--125 {
  padding-top: 125px !important;
}

.pb--125 {
  padding-bottom: 125px !important;
}

.pl--125 {
  padding-left: 125px !important;
}

.pr--125 {
  padding-right: 125px !important;
}

.mt--125 {
  margin-top: 125px !important;
}

.mb--125 {
  margin-bottom: 125px !important;
}

.mr--125 {
  margin-right: 125px !important;
}

.ml--125 {
  margin-left: 125px !important;
}

.ptb--130 {
  padding: 130px 0 !important;
}

.plr--130 {
  padding: 0 130px !important;
}

.pt--130 {
  padding-top: 130px !important;
}

.pb--130 {
  padding-bottom: 130px !important;
}

.pl--130 {
  padding-left: 130px !important;
}

.pr--130 {
  padding-right: 130px !important;
}

.mt--130 {
  margin-top: 130px !important;
}

.mb--130 {
  margin-bottom: 130px !important;
}

.mr--130 {
  margin-right: 130px !important;
}

.ml--130 {
  margin-left: 130px !important;
}

.ptb--135 {
  padding: 135px 0 !important;
}

.plr--135 {
  padding: 0 135px !important;
}

.pt--135 {
  padding-top: 135px !important;
}

.pb--135 {
  padding-bottom: 135px !important;
}

.pl--135 {
  padding-left: 135px !important;
}

.pr--135 {
  padding-right: 135px !important;
}

.mt--135 {
  margin-top: 135px !important;
}

.mb--135 {
  margin-bottom: 135px !important;
}

.mr--135 {
  margin-right: 135px !important;
}

.ml--135 {
  margin-left: 135px !important;
}

.ptb--140 {
  padding: 140px 0 !important;
}

.plr--140 {
  padding: 0 140px !important;
}

.pt--140 {
  padding-top: 140px !important;
}

.pb--140 {
  padding-bottom: 140px !important;
}

.pl--140 {
  padding-left: 140px !important;
}

.pr--140 {
  padding-right: 140px !important;
}

.mt--140 {
  margin-top: 140px !important;
}

.mb--140 {
  margin-bottom: 140px !important;
}

.mr--140 {
  margin-right: 140px !important;
}

.ml--140 {
  margin-left: 140px !important;
}

.ptb--145 {
  padding: 145px 0 !important;
}

.plr--145 {
  padding: 0 145px !important;
}

.pt--145 {
  padding-top: 145px !important;
}

.pb--145 {
  padding-bottom: 145px !important;
}

.pl--145 {
  padding-left: 145px !important;
}

.pr--145 {
  padding-right: 145px !important;
}

.mt--145 {
  margin-top: 145px !important;
}

.mb--145 {
  margin-bottom: 145px !important;
}

.mr--145 {
  margin-right: 145px !important;
}

.ml--145 {
  margin-left: 145px !important;
}

.ptb--150 {
  padding: 150px 0 !important;
}

.plr--150 {
  padding: 0 150px !important;
}

.pt--150 {
  padding-top: 150px !important;
}

.pb--150 {
  padding-bottom: 150px !important;
}

.pl--150 {
  padding-left: 150px !important;
}

.pr--150 {
  padding-right: 150px !important;
}

.mt--150 {
  margin-top: 150px !important;
}

.mb--150 {
  margin-bottom: 150px !important;
}

.mr--150 {
  margin-right: 150px !important;
}

.ml--150 {
  margin-left: 150px !important;
}

.ptb--155 {
  padding: 155px 0 !important;
}

.plr--155 {
  padding: 0 155px !important;
}

.pt--155 {
  padding-top: 155px !important;
}

.pb--155 {
  padding-bottom: 155px !important;
}

.pl--155 {
  padding-left: 155px !important;
}

.pr--155 {
  padding-right: 155px !important;
}

.mt--155 {
  margin-top: 155px !important;
}

.mb--155 {
  margin-bottom: 155px !important;
}

.mr--155 {
  margin-right: 155px !important;
}

.ml--155 {
  margin-left: 155px !important;
}

.ptb--160 {
  padding: 160px 0 !important;
}

.plr--160 {
  padding: 0 160px !important;
}

.pt--160 {
  padding-top: 160px !important;
}

.pb--160 {
  padding-bottom: 160px !important;
}

.pl--160 {
  padding-left: 160px !important;
}

.pr--160 {
  padding-right: 160px !important;
}

.mt--160 {
  margin-top: 160px !important;
}

.mb--160 {
  margin-bottom: 160px !important;
}

.mr--160 {
  margin-right: 160px !important;
}

.ml--160 {
  margin-left: 160px !important;
}

.ptb--165 {
  padding: 165px 0 !important;
}

.plr--165 {
  padding: 0 165px !important;
}

.pt--165 {
  padding-top: 165px !important;
}

.pb--165 {
  padding-bottom: 165px !important;
}

.pl--165 {
  padding-left: 165px !important;
}

.pr--165 {
  padding-right: 165px !important;
}

.mt--165 {
  margin-top: 165px !important;
}

.mb--165 {
  margin-bottom: 165px !important;
}

.mr--165 {
  margin-right: 165px !important;
}

.ml--165 {
  margin-left: 165px !important;
}

.ptb--170 {
  padding: 170px 0 !important;
}

.plr--170 {
  padding: 0 170px !important;
}

.pt--170 {
  padding-top: 170px !important;
}

.pb--170 {
  padding-bottom: 170px !important;
}

.pl--170 {
  padding-left: 170px !important;
}

.pr--170 {
  padding-right: 170px !important;
}

.mt--170 {
  margin-top: 170px !important;
}

.mb--170 {
  margin-bottom: 170px !important;
}

.mr--170 {
  margin-right: 170px !important;
}

.ml--170 {
  margin-left: 170px !important;
}

.ptb--175 {
  padding: 175px 0 !important;
}

.plr--175 {
  padding: 0 175px !important;
}

.pt--175 {
  padding-top: 175px !important;
}

.pb--175 {
  padding-bottom: 175px !important;
}

.pl--175 {
  padding-left: 175px !important;
}

.pr--175 {
  padding-right: 175px !important;
}

.mt--175 {
  margin-top: 175px !important;
}

.mb--175 {
  margin-bottom: 175px !important;
}

.mr--175 {
  margin-right: 175px !important;
}

.ml--175 {
  margin-left: 175px !important;
}

.ptb--180 {
  padding: 180px 0 !important;
}

.plr--180 {
  padding: 0 180px !important;
}

.pt--180 {
  padding-top: 180px !important;
}

.pb--180 {
  padding-bottom: 180px !important;
}

.pl--180 {
  padding-left: 180px !important;
}

.pr--180 {
  padding-right: 180px !important;
}

.mt--180 {
  margin-top: 180px !important;
}

.mb--180 {
  margin-bottom: 180px !important;
}

.mr--180 {
  margin-right: 180px !important;
}

.ml--180 {
  margin-left: 180px !important;
}

.ptb--185 {
  padding: 185px 0 !important;
}

.plr--185 {
  padding: 0 185px !important;
}

.pt--185 {
  padding-top: 185px !important;
}

.pb--185 {
  padding-bottom: 185px !important;
}

.pl--185 {
  padding-left: 185px !important;
}

.pr--185 {
  padding-right: 185px !important;
}

.mt--185 {
  margin-top: 185px !important;
}

.mb--185 {
  margin-bottom: 185px !important;
}

.mr--185 {
  margin-right: 185px !important;
}

.ml--185 {
  margin-left: 185px !important;
}

.ptb--190 {
  padding: 190px 0 !important;
}

.plr--190 {
  padding: 0 190px !important;
}

.pt--190 {
  padding-top: 190px !important;
}

.pb--190 {
  padding-bottom: 190px !important;
}

.pl--190 {
  padding-left: 190px !important;
}

.pr--190 {
  padding-right: 190px !important;
}

.mt--190 {
  margin-top: 190px !important;
}

.mb--190 {
  margin-bottom: 190px !important;
}

.mr--190 {
  margin-right: 190px !important;
}

.ml--190 {
  margin-left: 190px !important;
}

.ptb--195 {
  padding: 195px 0 !important;
}

.plr--195 {
  padding: 0 195px !important;
}

.pt--195 {
  padding-top: 195px !important;
}

.pb--195 {
  padding-bottom: 195px !important;
}

.pl--195 {
  padding-left: 195px !important;
}

.pr--195 {
  padding-right: 195px !important;
}

.mt--195 {
  margin-top: 195px !important;
}

.mb--195 {
  margin-bottom: 195px !important;
}

.mr--195 {
  margin-right: 195px !important;
}

.ml--195 {
  margin-left: 195px !important;
}

.ptb--200 {
  padding: 200px 0 !important;
}

.plr--200 {
  padding: 0 200px !important;
}

.pt--200 {
  padding-top: 200px !important;
}

.pb--200 {
  padding-bottom: 200px !important;
}

.pl--200 {
  padding-left: 200px !important;
}

.pr--200 {
  padding-right: 200px !important;
}

.mt--200 {
  margin-top: 200px !important;
}

.mb--200 {
  margin-bottom: 200px !important;
}

.mr--200 {
  margin-right: 200px !important;
}

.ml--200 {
  margin-left: 200px !important;
}

@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .ptb_lp--5 {
    padding: 5px 0;
  }
  .plr_lp--5 {
    padding: 0 5px;
  }
  .pt_lp--5 {
    padding-top: 5px;
  }
  .pb_lp--5 {
    padding-bottom: 5px;
  }
  .pl_lp--5 {
    padding-left: 5px;
  }
  .pr_lp--5 {
    padding-right: 5px;
  }
  .mt_lp--5 {
    margin-top: 5px;
  }
  .mb_lp--5 {
    margin-bottom: 5px;
  }
  .ptb_lp--10 {
    padding: 10px 0;
  }
  .plr_lp--10 {
    padding: 0 10px;
  }
  .pt_lp--10 {
    padding-top: 10px;
  }
  .pb_lp--10 {
    padding-bottom: 10px;
  }
  .pl_lp--10 {
    padding-left: 10px;
  }
  .pr_lp--10 {
    padding-right: 10px;
  }
  .mt_lp--10 {
    margin-top: 10px;
  }
  .mb_lp--10 {
    margin-bottom: 10px;
  }
  .ptb_lp--15 {
    padding: 15px 0;
  }
  .plr_lp--15 {
    padding: 0 15px;
  }
  .pt_lp--15 {
    padding-top: 15px;
  }
  .pb_lp--15 {
    padding-bottom: 15px;
  }
  .pl_lp--15 {
    padding-left: 15px;
  }
  .pr_lp--15 {
    padding-right: 15px;
  }
  .mt_lp--15 {
    margin-top: 15px;
  }
  .mb_lp--15 {
    margin-bottom: 15px;
  }
  .ptb_lp--20 {
    padding: 20px 0;
  }
  .plr_lp--20 {
    padding: 0 20px;
  }
  .pt_lp--20 {
    padding-top: 20px;
  }
  .pb_lp--20 {
    padding-bottom: 20px;
  }
  .pl_lp--20 {
    padding-left: 20px;
  }
  .pr_lp--20 {
    padding-right: 20px;
  }
  .mt_lp--20 {
    margin-top: 20px;
  }
  .mb_lp--20 {
    margin-bottom: 20px;
  }
  .ptb_lp--25 {
    padding: 25px 0;
  }
  .plr_lp--25 {
    padding: 0 25px;
  }
  .pt_lp--25 {
    padding-top: 25px;
  }
  .pb_lp--25 {
    padding-bottom: 25px;
  }
  .pl_lp--25 {
    padding-left: 25px;
  }
  .pr_lp--25 {
    padding-right: 25px;
  }
  .mt_lp--25 {
    margin-top: 25px;
  }
  .mb_lp--25 {
    margin-bottom: 25px;
  }
  .ptb_lp--30 {
    padding: 30px 0;
  }
  .plr_lp--30 {
    padding: 0 30px;
  }
  .pt_lp--30 {
    padding-top: 30px;
  }
  .pb_lp--30 {
    padding-bottom: 30px;
  }
  .pl_lp--30 {
    padding-left: 30px;
  }
  .pr_lp--30 {
    padding-right: 30px;
  }
  .mt_lp--30 {
    margin-top: 30px;
  }
  .mb_lp--30 {
    margin-bottom: 30px;
  }
  .ptb_lp--35 {
    padding: 35px 0;
  }
  .plr_lp--35 {
    padding: 0 35px;
  }
  .pt_lp--35 {
    padding-top: 35px;
  }
  .pb_lp--35 {
    padding-bottom: 35px;
  }
  .pl_lp--35 {
    padding-left: 35px;
  }
  .pr_lp--35 {
    padding-right: 35px;
  }
  .mt_lp--35 {
    margin-top: 35px;
  }
  .mb_lp--35 {
    margin-bottom: 35px;
  }
  .ptb_lp--40 {
    padding: 40px 0;
  }
  .plr_lp--40 {
    padding: 0 40px;
  }
  .pt_lp--40 {
    padding-top: 40px;
  }
  .pb_lp--40 {
    padding-bottom: 40px;
  }
  .pl_lp--40 {
    padding-left: 40px;
  }
  .pr_lp--40 {
    padding-right: 40px;
  }
  .mt_lp--40 {
    margin-top: 40px;
  }
  .mb_lp--40 {
    margin-bottom: 40px;
  }
  .ptb_lp--45 {
    padding: 45px 0;
  }
  .plr_lp--45 {
    padding: 0 45px;
  }
  .pt_lp--45 {
    padding-top: 45px;
  }
  .pb_lp--45 {
    padding-bottom: 45px;
  }
  .pl_lp--45 {
    padding-left: 45px;
  }
  .pr_lp--45 {
    padding-right: 45px;
  }
  .mt_lp--45 {
    margin-top: 45px;
  }
  .mb_lp--45 {
    margin-bottom: 45px;
  }
  .ptb_lp--50 {
    padding: 50px 0;
  }
  .plr_lp--50 {
    padding: 0 50px;
  }
  .pt_lp--50 {
    padding-top: 50px;
  }
  .pb_lp--50 {
    padding-bottom: 50px;
  }
  .pl_lp--50 {
    padding-left: 50px;
  }
  .pr_lp--50 {
    padding-right: 50px;
  }
  .mt_lp--50 {
    margin-top: 50px;
  }
  .mb_lp--50 {
    margin-bottom: 50px;
  }
  .ptb_lp--55 {
    padding: 55px 0;
  }
  .plr_lp--55 {
    padding: 0 55px;
  }
  .pt_lp--55 {
    padding-top: 55px;
  }
  .pb_lp--55 {
    padding-bottom: 55px;
  }
  .pl_lp--55 {
    padding-left: 55px;
  }
  .pr_lp--55 {
    padding-right: 55px;
  }
  .mt_lp--55 {
    margin-top: 55px;
  }
  .mb_lp--55 {
    margin-bottom: 55px;
  }
  .ptb_lp--60 {
    padding: 60px 0;
  }
  .plr_lp--60 {
    padding: 0 60px;
  }
  .pt_lp--60 {
    padding-top: 60px;
  }
  .pb_lp--60 {
    padding-bottom: 60px;
  }
  .pl_lp--60 {
    padding-left: 60px;
  }
  .pr_lp--60 {
    padding-right: 60px;
  }
  .mt_lp--60 {
    margin-top: 60px;
  }
  .mb_lp--60 {
    margin-bottom: 60px;
  }
  .ptb_lp--65 {
    padding: 65px 0;
  }
  .plr_lp--65 {
    padding: 0 65px;
  }
  .pt_lp--65 {
    padding-top: 65px;
  }
  .pb_lp--65 {
    padding-bottom: 65px;
  }
  .pl_lp--65 {
    padding-left: 65px;
  }
  .pr_lp--65 {
    padding-right: 65px;
  }
  .mt_lp--65 {
    margin-top: 65px;
  }
  .mb_lp--65 {
    margin-bottom: 65px;
  }
  .ptb_lp--70 {
    padding: 70px 0;
  }
  .plr_lp--70 {
    padding: 0 70px;
  }
  .pt_lp--70 {
    padding-top: 70px;
  }
  .pb_lp--70 {
    padding-bottom: 70px;
  }
  .pl_lp--70 {
    padding-left: 70px;
  }
  .pr_lp--70 {
    padding-right: 70px;
  }
  .mt_lp--70 {
    margin-top: 70px;
  }
  .mb_lp--70 {
    margin-bottom: 70px;
  }
  .ptb_lp--75 {
    padding: 75px 0;
  }
  .plr_lp--75 {
    padding: 0 75px;
  }
  .pt_lp--75 {
    padding-top: 75px;
  }
  .pb_lp--75 {
    padding-bottom: 75px;
  }
  .pl_lp--75 {
    padding-left: 75px;
  }
  .pr_lp--75 {
    padding-right: 75px;
  }
  .mt_lp--75 {
    margin-top: 75px;
  }
  .mb_lp--75 {
    margin-bottom: 75px;
  }
  .ptb_lp--80 {
    padding: 80px 0;
  }
  .plr_lp--80 {
    padding: 0 80px;
  }
  .pt_lp--80 {
    padding-top: 80px;
  }
  .pb_lp--80 {
    padding-bottom: 80px;
  }
  .pl_lp--80 {
    padding-left: 80px;
  }
  .pr_lp--80 {
    padding-right: 80px;
  }
  .mt_lp--80 {
    margin-top: 80px;
  }
  .mb_lp--80 {
    margin-bottom: 80px;
  }
  .ptb_lp--85 {
    padding: 85px 0;
  }
  .plr_lp--85 {
    padding: 0 85px;
  }
  .pt_lp--85 {
    padding-top: 85px;
  }
  .pb_lp--85 {
    padding-bottom: 85px;
  }
  .pl_lp--85 {
    padding-left: 85px;
  }
  .pr_lp--85 {
    padding-right: 85px;
  }
  .mt_lp--85 {
    margin-top: 85px;
  }
  .mb_lp--85 {
    margin-bottom: 85px;
  }
  .ptb_lp--90 {
    padding: 90px 0;
  }
  .plr_lp--90 {
    padding: 0 90px;
  }
  .pt_lp--90 {
    padding-top: 90px;
  }
  .pb_lp--90 {
    padding-bottom: 90px;
  }
  .pl_lp--90 {
    padding-left: 90px;
  }
  .pr_lp--90 {
    padding-right: 90px;
  }
  .mt_lp--90 {
    margin-top: 90px;
  }
  .mb_lp--90 {
    margin-bottom: 90px;
  }
  .ptb_lp--95 {
    padding: 95px 0;
  }
  .plr_lp--95 {
    padding: 0 95px;
  }
  .pt_lp--95 {
    padding-top: 95px;
  }
  .pb_lp--95 {
    padding-bottom: 95px;
  }
  .pl_lp--95 {
    padding-left: 95px;
  }
  .pr_lp--95 {
    padding-right: 95px;
  }
  .mt_lp--95 {
    margin-top: 95px;
  }
  .mb_lp--95 {
    margin-bottom: 95px;
  }
  .ptb_lp--100 {
    padding: 100px 0;
  }
  .plr_lp--100 {
    padding: 0 100px;
  }
  .pt_lp--100 {
    padding-top: 100px;
  }
  .pb_lp--100 {
    padding-bottom: 100px;
  }
  .pl_lp--100 {
    padding-left: 100px;
  }
  .pr_lp--100 {
    padding-right: 100px;
  }
  .mt_lp--100 {
    margin-top: 100px;
  }
  .mb_lp--100 {
    margin-bottom: 100px;
  }
  .ptb_lp--105 {
    padding: 105px 0;
  }
  .plr_lp--105 {
    padding: 0 105px;
  }
  .pt_lp--105 {
    padding-top: 105px;
  }
  .pb_lp--105 {
    padding-bottom: 105px;
  }
  .pl_lp--105 {
    padding-left: 105px;
  }
  .pr_lp--105 {
    padding-right: 105px;
  }
  .mt_lp--105 {
    margin-top: 105px;
  }
  .mb_lp--105 {
    margin-bottom: 105px;
  }
  .ptb_lp--110 {
    padding: 110px 0;
  }
  .plr_lp--110 {
    padding: 0 110px;
  }
  .pt_lp--110 {
    padding-top: 110px;
  }
  .pb_lp--110 {
    padding-bottom: 110px;
  }
  .pl_lp--110 {
    padding-left: 110px;
  }
  .pr_lp--110 {
    padding-right: 110px;
  }
  .mt_lp--110 {
    margin-top: 110px;
  }
  .mb_lp--110 {
    margin-bottom: 110px;
  }
  .ptb_lp--115 {
    padding: 115px 0;
  }
  .plr_lp--115 {
    padding: 0 115px;
  }
  .pt_lp--115 {
    padding-top: 115px;
  }
  .pb_lp--115 {
    padding-bottom: 115px;
  }
  .pl_lp--115 {
    padding-left: 115px;
  }
  .pr_lp--115 {
    padding-right: 115px;
  }
  .mt_lp--115 {
    margin-top: 115px;
  }
  .mb_lp--115 {
    margin-bottom: 115px;
  }
  .ptb_lp--120 {
    padding: 120px 0;
  }
  .plr_lp--120 {
    padding: 0 120px;
  }
  .pt_lp--120 {
    padding-top: 120px;
  }
  .pb_lp--120 {
    padding-bottom: 120px;
  }
  .pl_lp--120 {
    padding-left: 120px;
  }
  .pr_lp--120 {
    padding-right: 120px;
  }
  .mt_lp--120 {
    margin-top: 120px;
  }
  .mb_lp--120 {
    margin-bottom: 120px;
  }
  .ptb_lp--125 {
    padding: 125px 0;
  }
  .plr_lp--125 {
    padding: 0 125px;
  }
  .pt_lp--125 {
    padding-top: 125px;
  }
  .pb_lp--125 {
    padding-bottom: 125px;
  }
  .pl_lp--125 {
    padding-left: 125px;
  }
  .pr_lp--125 {
    padding-right: 125px;
  }
  .mt_lp--125 {
    margin-top: 125px;
  }
  .mb_lp--125 {
    margin-bottom: 125px;
  }
  .ptb_lp--130 {
    padding: 130px 0;
  }
  .plr_lp--130 {
    padding: 0 130px;
  }
  .pt_lp--130 {
    padding-top: 130px;
  }
  .pb_lp--130 {
    padding-bottom: 130px;
  }
  .pl_lp--130 {
    padding-left: 130px;
  }
  .pr_lp--130 {
    padding-right: 130px;
  }
  .mt_lp--130 {
    margin-top: 130px;
  }
  .mb_lp--130 {
    margin-bottom: 130px;
  }
  .ptb_lp--135 {
    padding: 135px 0;
  }
  .plr_lp--135 {
    padding: 0 135px;
  }
  .pt_lp--135 {
    padding-top: 135px;
  }
  .pb_lp--135 {
    padding-bottom: 135px;
  }
  .pl_lp--135 {
    padding-left: 135px;
  }
  .pr_lp--135 {
    padding-right: 135px;
  }
  .mt_lp--135 {
    margin-top: 135px;
  }
  .mb_lp--135 {
    margin-bottom: 135px;
  }
  .ptb_lp--140 {
    padding: 140px 0;
  }
  .plr_lp--140 {
    padding: 0 140px;
  }
  .pt_lp--140 {
    padding-top: 140px;
  }
  .pb_lp--140 {
    padding-bottom: 140px;
  }
  .pl_lp--140 {
    padding-left: 140px;
  }
  .pr_lp--140 {
    padding-right: 140px;
  }
  .mt_lp--140 {
    margin-top: 140px;
  }
  .mb_lp--140 {
    margin-bottom: 140px;
  }
  .ptb_lp--145 {
    padding: 145px 0;
  }
  .plr_lp--145 {
    padding: 0 145px;
  }
  .pt_lp--145 {
    padding-top: 145px;
  }
  .pb_lp--145 {
    padding-bottom: 145px;
  }
  .pl_lp--145 {
    padding-left: 145px;
  }
  .pr_lp--145 {
    padding-right: 145px;
  }
  .mt_lp--145 {
    margin-top: 145px;
  }
  .mb_lp--145 {
    margin-bottom: 145px;
  }
  .ptb_lp--150 {
    padding: 150px 0;
  }
  .plr_lp--150 {
    padding: 0 150px;
  }
  .pt_lp--150 {
    padding-top: 150px;
  }
  .pb_lp--150 {
    padding-bottom: 150px;
  }
  .pl_lp--150 {
    padding-left: 150px;
  }
  .pr_lp--150 {
    padding-right: 150px;
  }
  .mt_lp--150 {
    margin-top: 150px;
  }
  .mb_lp--150 {
    margin-bottom: 150px;
  }
  .ptb_lp--155 {
    padding: 155px 0;
  }
  .plr_lp--155 {
    padding: 0 155px;
  }
  .pt_lp--155 {
    padding-top: 155px;
  }
  .pb_lp--155 {
    padding-bottom: 155px;
  }
  .pl_lp--155 {
    padding-left: 155px;
  }
  .pr_lp--155 {
    padding-right: 155px;
  }
  .mt_lp--155 {
    margin-top: 155px;
  }
  .mb_lp--155 {
    margin-bottom: 155px;
  }
  .ptb_lp--160 {
    padding: 160px 0;
  }
  .plr_lp--160 {
    padding: 0 160px;
  }
  .pt_lp--160 {
    padding-top: 160px;
  }
  .pb_lp--160 {
    padding-bottom: 160px;
  }
  .pl_lp--160 {
    padding-left: 160px;
  }
  .pr_lp--160 {
    padding-right: 160px;
  }
  .mt_lp--160 {
    margin-top: 160px;
  }
  .mb_lp--160 {
    margin-bottom: 160px;
  }
  .ptb_lp--165 {
    padding: 165px 0;
  }
  .plr_lp--165 {
    padding: 0 165px;
  }
  .pt_lp--165 {
    padding-top: 165px;
  }
  .pb_lp--165 {
    padding-bottom: 165px;
  }
  .pl_lp--165 {
    padding-left: 165px;
  }
  .pr_lp--165 {
    padding-right: 165px;
  }
  .mt_lp--165 {
    margin-top: 165px;
  }
  .mb_lp--165 {
    margin-bottom: 165px;
  }
  .ptb_lp--170 {
    padding: 170px 0;
  }
  .plr_lp--170 {
    padding: 0 170px;
  }
  .pt_lp--170 {
    padding-top: 170px;
  }
  .pb_lp--170 {
    padding-bottom: 170px;
  }
  .pl_lp--170 {
    padding-left: 170px;
  }
  .pr_lp--170 {
    padding-right: 170px;
  }
  .mt_lp--170 {
    margin-top: 170px;
  }
  .mb_lp--170 {
    margin-bottom: 170px;
  }
  .ptb_lp--175 {
    padding: 175px 0;
  }
  .plr_lp--175 {
    padding: 0 175px;
  }
  .pt_lp--175 {
    padding-top: 175px;
  }
  .pb_lp--175 {
    padding-bottom: 175px;
  }
  .pl_lp--175 {
    padding-left: 175px;
  }
  .pr_lp--175 {
    padding-right: 175px;
  }
  .mt_lp--175 {
    margin-top: 175px;
  }
  .mb_lp--175 {
    margin-bottom: 175px;
  }
  .ptb_lp--180 {
    padding: 180px 0;
  }
  .plr_lp--180 {
    padding: 0 180px;
  }
  .pt_lp--180 {
    padding-top: 180px;
  }
  .pb_lp--180 {
    padding-bottom: 180px;
  }
  .pl_lp--180 {
    padding-left: 180px;
  }
  .pr_lp--180 {
    padding-right: 180px;
  }
  .mt_lp--180 {
    margin-top: 180px;
  }
  .mb_lp--180 {
    margin-bottom: 180px;
  }
  .ptb_lp--185 {
    padding: 185px 0;
  }
  .plr_lp--185 {
    padding: 0 185px;
  }
  .pt_lp--185 {
    padding-top: 185px;
  }
  .pb_lp--185 {
    padding-bottom: 185px;
  }
  .pl_lp--185 {
    padding-left: 185px;
  }
  .pr_lp--185 {
    padding-right: 185px;
  }
  .mt_lp--185 {
    margin-top: 185px;
  }
  .mb_lp--185 {
    margin-bottom: 185px;
  }
  .ptb_lp--190 {
    padding: 190px 0;
  }
  .plr_lp--190 {
    padding: 0 190px;
  }
  .pt_lp--190 {
    padding-top: 190px;
  }
  .pb_lp--190 {
    padding-bottom: 190px;
  }
  .pl_lp--190 {
    padding-left: 190px;
  }
  .pr_lp--190 {
    padding-right: 190px;
  }
  .mt_lp--190 {
    margin-top: 190px;
  }
  .mb_lp--190 {
    margin-bottom: 190px;
  }
  .ptb_lp--195 {
    padding: 195px 0;
  }
  .plr_lp--195 {
    padding: 0 195px;
  }
  .pt_lp--195 {
    padding-top: 195px;
  }
  .pb_lp--195 {
    padding-bottom: 195px;
  }
  .pl_lp--195 {
    padding-left: 195px;
  }
  .pr_lp--195 {
    padding-right: 195px;
  }
  .mt_lp--195 {
    margin-top: 195px;
  }
  .mb_lp--195 {
    margin-bottom: 195px;
  }
  .ptb_lp--200 {
    padding: 200px 0;
  }
  .plr_lp--200 {
    padding: 0 200px;
  }
  .pt_lp--200 {
    padding-top: 200px;
  }
  .pb_lp--200 {
    padding-bottom: 200px;
  }
  .pl_lp--200 {
    padding-left: 200px;
  }
  .pr_lp--200 {
    padding-right: 200px;
  }
  .mt_lp--200 {
    margin-top: 200px;
  }
  .mb_lp--200 {
    margin-bottom: 200px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .ptb_lg--5 {
    padding: 5px 0 !important;
  }
  .plr_lg--5 {
    padding: 0 5px !important;
  }
  .pt_lg--5 {
    padding-top: 5px !important;
  }
  .pb_lg--5 {
    padding-bottom: 5px !important;
  }
  .pl_lg--5 {
    padding-left: 5px !important;
  }
  .pr_lg--5 {
    padding-right: 5px !important;
  }
  .mt_lg--5 {
    margin-top: 5px !important;
  }
  .mb_lg--5 {
    margin-bottom: 5px !important;
  }
  .ml_lg--5 {
    margin-left: 5px !important;
  }
  .ptb_lg--10 {
    padding: 10px 0 !important;
  }
  .plr_lg--10 {
    padding: 0 10px !important;
  }
  .pt_lg--10 {
    padding-top: 10px !important;
  }
  .pb_lg--10 {
    padding-bottom: 10px !important;
  }
  .pl_lg--10 {
    padding-left: 10px !important;
  }
  .pr_lg--10 {
    padding-right: 10px !important;
  }
  .mt_lg--10 {
    margin-top: 10px !important;
  }
  .mb_lg--10 {
    margin-bottom: 10px !important;
  }
  .ml_lg--10 {
    margin-left: 10px !important;
  }
  .ptb_lg--15 {
    padding: 15px 0 !important;
  }
  .plr_lg--15 {
    padding: 0 15px !important;
  }
  .pt_lg--15 {
    padding-top: 15px !important;
  }
  .pb_lg--15 {
    padding-bottom: 15px !important;
  }
  .pl_lg--15 {
    padding-left: 15px !important;
  }
  .pr_lg--15 {
    padding-right: 15px !important;
  }
  .mt_lg--15 {
    margin-top: 15px !important;
  }
  .mb_lg--15 {
    margin-bottom: 15px !important;
  }
  .ml_lg--15 {
    margin-left: 15px !important;
  }
  .ptb_lg--20 {
    padding: 20px 0 !important;
  }
  .plr_lg--20 {
    padding: 0 20px !important;
  }
  .pt_lg--20 {
    padding-top: 20px !important;
  }
  .pb_lg--20 {
    padding-bottom: 20px !important;
  }
  .pl_lg--20 {
    padding-left: 20px !important;
  }
  .pr_lg--20 {
    padding-right: 20px !important;
  }
  .mt_lg--20 {
    margin-top: 20px !important;
  }
  .mb_lg--20 {
    margin-bottom: 20px !important;
  }
  .ml_lg--20 {
    margin-left: 20px !important;
  }
  .ptb_lg--25 {
    padding: 25px 0 !important;
  }
  .plr_lg--25 {
    padding: 0 25px !important;
  }
  .pt_lg--25 {
    padding-top: 25px !important;
  }
  .pb_lg--25 {
    padding-bottom: 25px !important;
  }
  .pl_lg--25 {
    padding-left: 25px !important;
  }
  .pr_lg--25 {
    padding-right: 25px !important;
  }
  .mt_lg--25 {
    margin-top: 25px !important;
  }
  .mb_lg--25 {
    margin-bottom: 25px !important;
  }
  .ml_lg--25 {
    margin-left: 25px !important;
  }
  .ptb_lg--30 {
    padding: 30px 0 !important;
  }
  .plr_lg--30 {
    padding: 0 30px !important;
  }
  .pt_lg--30 {
    padding-top: 30px !important;
  }
  .pb_lg--30 {
    padding-bottom: 30px !important;
  }
  .pl_lg--30 {
    padding-left: 30px !important;
  }
  .pr_lg--30 {
    padding-right: 30px !important;
  }
  .mt_lg--30 {
    margin-top: 30px !important;
  }
  .mb_lg--30 {
    margin-bottom: 30px !important;
  }
  .ml_lg--30 {
    margin-left: 30px !important;
  }
  .ptb_lg--35 {
    padding: 35px 0 !important;
  }
  .plr_lg--35 {
    padding: 0 35px !important;
  }
  .pt_lg--35 {
    padding-top: 35px !important;
  }
  .pb_lg--35 {
    padding-bottom: 35px !important;
  }
  .pl_lg--35 {
    padding-left: 35px !important;
  }
  .pr_lg--35 {
    padding-right: 35px !important;
  }
  .mt_lg--35 {
    margin-top: 35px !important;
  }
  .mb_lg--35 {
    margin-bottom: 35px !important;
  }
  .ml_lg--35 {
    margin-left: 35px !important;
  }
  .ptb_lg--40 {
    padding: 40px 0 !important;
  }
  .plr_lg--40 {
    padding: 0 40px !important;
  }
  .pt_lg--40 {
    padding-top: 40px !important;
  }
  .pb_lg--40 {
    padding-bottom: 40px !important;
  }
  .pl_lg--40 {
    padding-left: 40px !important;
  }
  .pr_lg--40 {
    padding-right: 40px !important;
  }
  .mt_lg--40 {
    margin-top: 40px !important;
  }
  .mb_lg--40 {
    margin-bottom: 40px !important;
  }
  .ml_lg--40 {
    margin-left: 40px !important;
  }
  .ptb_lg--45 {
    padding: 45px 0 !important;
  }
  .plr_lg--45 {
    padding: 0 45px !important;
  }
  .pt_lg--45 {
    padding-top: 45px !important;
  }
  .pb_lg--45 {
    padding-bottom: 45px !important;
  }
  .pl_lg--45 {
    padding-left: 45px !important;
  }
  .pr_lg--45 {
    padding-right: 45px !important;
  }
  .mt_lg--45 {
    margin-top: 45px !important;
  }
  .mb_lg--45 {
    margin-bottom: 45px !important;
  }
  .ml_lg--45 {
    margin-left: 45px !important;
  }
  .ptb_lg--50 {
    padding: 50px 0 !important;
  }
  .plr_lg--50 {
    padding: 0 50px !important;
  }
  .pt_lg--50 {
    padding-top: 50px !important;
  }
  .pb_lg--50 {
    padding-bottom: 50px !important;
  }
  .pl_lg--50 {
    padding-left: 50px !important;
  }
  .pr_lg--50 {
    padding-right: 50px !important;
  }
  .mt_lg--50 {
    margin-top: 50px !important;
  }
  .mb_lg--50 {
    margin-bottom: 50px !important;
  }
  .ml_lg--50 {
    margin-left: 50px !important;
  }
  .ptb_lg--55 {
    padding: 55px 0 !important;
  }
  .plr_lg--55 {
    padding: 0 55px !important;
  }
  .pt_lg--55 {
    padding-top: 55px !important;
  }
  .pb_lg--55 {
    padding-bottom: 55px !important;
  }
  .pl_lg--55 {
    padding-left: 55px !important;
  }
  .pr_lg--55 {
    padding-right: 55px !important;
  }
  .mt_lg--55 {
    margin-top: 55px !important;
  }
  .mb_lg--55 {
    margin-bottom: 55px !important;
  }
  .ml_lg--55 {
    margin-left: 55px !important;
  }
  .ptb_lg--60 {
    padding: 60px 0 !important;
  }
  .plr_lg--60 {
    padding: 0 60px !important;
  }
  .pt_lg--60 {
    padding-top: 60px !important;
  }
  .pb_lg--60 {
    padding-bottom: 60px !important;
  }
  .pl_lg--60 {
    padding-left: 60px !important;
  }
  .pr_lg--60 {
    padding-right: 60px !important;
  }
  .mt_lg--60 {
    margin-top: 60px !important;
  }
  .mb_lg--60 {
    margin-bottom: 60px !important;
  }
  .ml_lg--60 {
    margin-left: 60px !important;
  }
  .ptb_lg--65 {
    padding: 65px 0 !important;
  }
  .plr_lg--65 {
    padding: 0 65px !important;
  }
  .pt_lg--65 {
    padding-top: 65px !important;
  }
  .pb_lg--65 {
    padding-bottom: 65px !important;
  }
  .pl_lg--65 {
    padding-left: 65px !important;
  }
  .pr_lg--65 {
    padding-right: 65px !important;
  }
  .mt_lg--65 {
    margin-top: 65px !important;
  }
  .mb_lg--65 {
    margin-bottom: 65px !important;
  }
  .ml_lg--65 {
    margin-left: 65px !important;
  }
  .ptb_lg--70 {
    padding: 70px 0 !important;
  }
  .plr_lg--70 {
    padding: 0 70px !important;
  }
  .pt_lg--70 {
    padding-top: 70px !important;
  }
  .pb_lg--70 {
    padding-bottom: 70px !important;
  }
  .pl_lg--70 {
    padding-left: 70px !important;
  }
  .pr_lg--70 {
    padding-right: 70px !important;
  }
  .mt_lg--70 {
    margin-top: 70px !important;
  }
  .mb_lg--70 {
    margin-bottom: 70px !important;
  }
  .ml_lg--70 {
    margin-left: 70px !important;
  }
  .ptb_lg--75 {
    padding: 75px 0 !important;
  }
  .plr_lg--75 {
    padding: 0 75px !important;
  }
  .pt_lg--75 {
    padding-top: 75px !important;
  }
  .pb_lg--75 {
    padding-bottom: 75px !important;
  }
  .pl_lg--75 {
    padding-left: 75px !important;
  }
  .pr_lg--75 {
    padding-right: 75px !important;
  }
  .mt_lg--75 {
    margin-top: 75px !important;
  }
  .mb_lg--75 {
    margin-bottom: 75px !important;
  }
  .ml_lg--75 {
    margin-left: 75px !important;
  }
  .ptb_lg--80 {
    padding: 80px 0 !important;
  }
  .plr_lg--80 {
    padding: 0 80px !important;
  }
  .pt_lg--80 {
    padding-top: 80px !important;
  }
  .pb_lg--80 {
    padding-bottom: 80px !important;
  }
  .pl_lg--80 {
    padding-left: 80px !important;
  }
  .pr_lg--80 {
    padding-right: 80px !important;
  }
  .mt_lg--80 {
    margin-top: 80px !important;
  }
  .mb_lg--80 {
    margin-bottom: 80px !important;
  }
  .ml_lg--80 {
    margin-left: 80px !important;
  }
  .ptb_lg--85 {
    padding: 85px 0 !important;
  }
  .plr_lg--85 {
    padding: 0 85px !important;
  }
  .pt_lg--85 {
    padding-top: 85px !important;
  }
  .pb_lg--85 {
    padding-bottom: 85px !important;
  }
  .pl_lg--85 {
    padding-left: 85px !important;
  }
  .pr_lg--85 {
    padding-right: 85px !important;
  }
  .mt_lg--85 {
    margin-top: 85px !important;
  }
  .mb_lg--85 {
    margin-bottom: 85px !important;
  }
  .ml_lg--85 {
    margin-left: 85px !important;
  }
  .ptb_lg--90 {
    padding: 90px 0 !important;
  }
  .plr_lg--90 {
    padding: 0 90px !important;
  }
  .pt_lg--90 {
    padding-top: 90px !important;
  }
  .pb_lg--90 {
    padding-bottom: 90px !important;
  }
  .pl_lg--90 {
    padding-left: 90px !important;
  }
  .pr_lg--90 {
    padding-right: 90px !important;
  }
  .mt_lg--90 {
    margin-top: 90px !important;
  }
  .mb_lg--90 {
    margin-bottom: 90px !important;
  }
  .ml_lg--90 {
    margin-left: 90px !important;
  }
  .ptb_lg--95 {
    padding: 95px 0 !important;
  }
  .plr_lg--95 {
    padding: 0 95px !important;
  }
  .pt_lg--95 {
    padding-top: 95px !important;
  }
  .pb_lg--95 {
    padding-bottom: 95px !important;
  }
  .pl_lg--95 {
    padding-left: 95px !important;
  }
  .pr_lg--95 {
    padding-right: 95px !important;
  }
  .mt_lg--95 {
    margin-top: 95px !important;
  }
  .mb_lg--95 {
    margin-bottom: 95px !important;
  }
  .ml_lg--95 {
    margin-left: 95px !important;
  }
  .ptb_lg--100 {
    padding: 100px 0 !important;
  }
  .plr_lg--100 {
    padding: 0 100px !important;
  }
  .pt_lg--100 {
    padding-top: 100px !important;
  }
  .pb_lg--100 {
    padding-bottom: 100px !important;
  }
  .pl_lg--100 {
    padding-left: 100px !important;
  }
  .pr_lg--100 {
    padding-right: 100px !important;
  }
  .mt_lg--100 {
    margin-top: 100px !important;
  }
  .mb_lg--100 {
    margin-bottom: 100px !important;
  }
  .ml_lg--100 {
    margin-left: 100px !important;
  }
  .ptb_lg--105 {
    padding: 105px 0 !important;
  }
  .plr_lg--105 {
    padding: 0 105px !important;
  }
  .pt_lg--105 {
    padding-top: 105px !important;
  }
  .pb_lg--105 {
    padding-bottom: 105px !important;
  }
  .pl_lg--105 {
    padding-left: 105px !important;
  }
  .pr_lg--105 {
    padding-right: 105px !important;
  }
  .mt_lg--105 {
    margin-top: 105px !important;
  }
  .mb_lg--105 {
    margin-bottom: 105px !important;
  }
  .ml_lg--105 {
    margin-left: 105px !important;
  }
  .ptb_lg--110 {
    padding: 110px 0 !important;
  }
  .plr_lg--110 {
    padding: 0 110px !important;
  }
  .pt_lg--110 {
    padding-top: 110px !important;
  }
  .pb_lg--110 {
    padding-bottom: 110px !important;
  }
  .pl_lg--110 {
    padding-left: 110px !important;
  }
  .pr_lg--110 {
    padding-right: 110px !important;
  }
  .mt_lg--110 {
    margin-top: 110px !important;
  }
  .mb_lg--110 {
    margin-bottom: 110px !important;
  }
  .ml_lg--110 {
    margin-left: 110px !important;
  }
  .ptb_lg--115 {
    padding: 115px 0 !important;
  }
  .plr_lg--115 {
    padding: 0 115px !important;
  }
  .pt_lg--115 {
    padding-top: 115px !important;
  }
  .pb_lg--115 {
    padding-bottom: 115px !important;
  }
  .pl_lg--115 {
    padding-left: 115px !important;
  }
  .pr_lg--115 {
    padding-right: 115px !important;
  }
  .mt_lg--115 {
    margin-top: 115px !important;
  }
  .mb_lg--115 {
    margin-bottom: 115px !important;
  }
  .ml_lg--115 {
    margin-left: 115px !important;
  }
  .ptb_lg--120 {
    padding: 120px 0 !important;
  }
  .plr_lg--120 {
    padding: 0 120px !important;
  }
  .pt_lg--120 {
    padding-top: 120px !important;
  }
  .pb_lg--120 {
    padding-bottom: 120px !important;
  }
  .pl_lg--120 {
    padding-left: 120px !important;
  }
  .pr_lg--120 {
    padding-right: 120px !important;
  }
  .mt_lg--120 {
    margin-top: 120px !important;
  }
  .mb_lg--120 {
    margin-bottom: 120px !important;
  }
  .ml_lg--120 {
    margin-left: 120px !important;
  }
  .ptb_lg--125 {
    padding: 125px 0 !important;
  }
  .plr_lg--125 {
    padding: 0 125px !important;
  }
  .pt_lg--125 {
    padding-top: 125px !important;
  }
  .pb_lg--125 {
    padding-bottom: 125px !important;
  }
  .pl_lg--125 {
    padding-left: 125px !important;
  }
  .pr_lg--125 {
    padding-right: 125px !important;
  }
  .mt_lg--125 {
    margin-top: 125px !important;
  }
  .mb_lg--125 {
    margin-bottom: 125px !important;
  }
  .ml_lg--125 {
    margin-left: 125px !important;
  }
  .ptb_lg--130 {
    padding: 130px 0 !important;
  }
  .plr_lg--130 {
    padding: 0 130px !important;
  }
  .pt_lg--130 {
    padding-top: 130px !important;
  }
  .pb_lg--130 {
    padding-bottom: 130px !important;
  }
  .pl_lg--130 {
    padding-left: 130px !important;
  }
  .pr_lg--130 {
    padding-right: 130px !important;
  }
  .mt_lg--130 {
    margin-top: 130px !important;
  }
  .mb_lg--130 {
    margin-bottom: 130px !important;
  }
  .ml_lg--130 {
    margin-left: 130px !important;
  }
  .ptb_lg--135 {
    padding: 135px 0 !important;
  }
  .plr_lg--135 {
    padding: 0 135px !important;
  }
  .pt_lg--135 {
    padding-top: 135px !important;
  }
  .pb_lg--135 {
    padding-bottom: 135px !important;
  }
  .pl_lg--135 {
    padding-left: 135px !important;
  }
  .pr_lg--135 {
    padding-right: 135px !important;
  }
  .mt_lg--135 {
    margin-top: 135px !important;
  }
  .mb_lg--135 {
    margin-bottom: 135px !important;
  }
  .ml_lg--135 {
    margin-left: 135px !important;
  }
  .ptb_lg--140 {
    padding: 140px 0 !important;
  }
  .plr_lg--140 {
    padding: 0 140px !important;
  }
  .pt_lg--140 {
    padding-top: 140px !important;
  }
  .pb_lg--140 {
    padding-bottom: 140px !important;
  }
  .pl_lg--140 {
    padding-left: 140px !important;
  }
  .pr_lg--140 {
    padding-right: 140px !important;
  }
  .mt_lg--140 {
    margin-top: 140px !important;
  }
  .mb_lg--140 {
    margin-bottom: 140px !important;
  }
  .ml_lg--140 {
    margin-left: 140px !important;
  }
  .ptb_lg--145 {
    padding: 145px 0 !important;
  }
  .plr_lg--145 {
    padding: 0 145px !important;
  }
  .pt_lg--145 {
    padding-top: 145px !important;
  }
  .pb_lg--145 {
    padding-bottom: 145px !important;
  }
  .pl_lg--145 {
    padding-left: 145px !important;
  }
  .pr_lg--145 {
    padding-right: 145px !important;
  }
  .mt_lg--145 {
    margin-top: 145px !important;
  }
  .mb_lg--145 {
    margin-bottom: 145px !important;
  }
  .ml_lg--145 {
    margin-left: 145px !important;
  }
  .ptb_lg--150 {
    padding: 150px 0 !important;
  }
  .plr_lg--150 {
    padding: 0 150px !important;
  }
  .pt_lg--150 {
    padding-top: 150px !important;
  }
  .pb_lg--150 {
    padding-bottom: 150px !important;
  }
  .pl_lg--150 {
    padding-left: 150px !important;
  }
  .pr_lg--150 {
    padding-right: 150px !important;
  }
  .mt_lg--150 {
    margin-top: 150px !important;
  }
  .mb_lg--150 {
    margin-bottom: 150px !important;
  }
  .ml_lg--150 {
    margin-left: 150px !important;
  }
  .ptb_lg--155 {
    padding: 155px 0 !important;
  }
  .plr_lg--155 {
    padding: 0 155px !important;
  }
  .pt_lg--155 {
    padding-top: 155px !important;
  }
  .pb_lg--155 {
    padding-bottom: 155px !important;
  }
  .pl_lg--155 {
    padding-left: 155px !important;
  }
  .pr_lg--155 {
    padding-right: 155px !important;
  }
  .mt_lg--155 {
    margin-top: 155px !important;
  }
  .mb_lg--155 {
    margin-bottom: 155px !important;
  }
  .ml_lg--155 {
    margin-left: 155px !important;
  }
  .ptb_lg--160 {
    padding: 160px 0 !important;
  }
  .plr_lg--160 {
    padding: 0 160px !important;
  }
  .pt_lg--160 {
    padding-top: 160px !important;
  }
  .pb_lg--160 {
    padding-bottom: 160px !important;
  }
  .pl_lg--160 {
    padding-left: 160px !important;
  }
  .pr_lg--160 {
    padding-right: 160px !important;
  }
  .mt_lg--160 {
    margin-top: 160px !important;
  }
  .mb_lg--160 {
    margin-bottom: 160px !important;
  }
  .ml_lg--160 {
    margin-left: 160px !important;
  }
  .ptb_lg--165 {
    padding: 165px 0 !important;
  }
  .plr_lg--165 {
    padding: 0 165px !important;
  }
  .pt_lg--165 {
    padding-top: 165px !important;
  }
  .pb_lg--165 {
    padding-bottom: 165px !important;
  }
  .pl_lg--165 {
    padding-left: 165px !important;
  }
  .pr_lg--165 {
    padding-right: 165px !important;
  }
  .mt_lg--165 {
    margin-top: 165px !important;
  }
  .mb_lg--165 {
    margin-bottom: 165px !important;
  }
  .ml_lg--165 {
    margin-left: 165px !important;
  }
  .ptb_lg--170 {
    padding: 170px 0 !important;
  }
  .plr_lg--170 {
    padding: 0 170px !important;
  }
  .pt_lg--170 {
    padding-top: 170px !important;
  }
  .pb_lg--170 {
    padding-bottom: 170px !important;
  }
  .pl_lg--170 {
    padding-left: 170px !important;
  }
  .pr_lg--170 {
    padding-right: 170px !important;
  }
  .mt_lg--170 {
    margin-top: 170px !important;
  }
  .mb_lg--170 {
    margin-bottom: 170px !important;
  }
  .ml_lg--170 {
    margin-left: 170px !important;
  }
  .ptb_lg--175 {
    padding: 175px 0 !important;
  }
  .plr_lg--175 {
    padding: 0 175px !important;
  }
  .pt_lg--175 {
    padding-top: 175px !important;
  }
  .pb_lg--175 {
    padding-bottom: 175px !important;
  }
  .pl_lg--175 {
    padding-left: 175px !important;
  }
  .pr_lg--175 {
    padding-right: 175px !important;
  }
  .mt_lg--175 {
    margin-top: 175px !important;
  }
  .mb_lg--175 {
    margin-bottom: 175px !important;
  }
  .ml_lg--175 {
    margin-left: 175px !important;
  }
  .ptb_lg--180 {
    padding: 180px 0 !important;
  }
  .plr_lg--180 {
    padding: 0 180px !important;
  }
  .pt_lg--180 {
    padding-top: 180px !important;
  }
  .pb_lg--180 {
    padding-bottom: 180px !important;
  }
  .pl_lg--180 {
    padding-left: 180px !important;
  }
  .pr_lg--180 {
    padding-right: 180px !important;
  }
  .mt_lg--180 {
    margin-top: 180px !important;
  }
  .mb_lg--180 {
    margin-bottom: 180px !important;
  }
  .ml_lg--180 {
    margin-left: 180px !important;
  }
  .ptb_lg--185 {
    padding: 185px 0 !important;
  }
  .plr_lg--185 {
    padding: 0 185px !important;
  }
  .pt_lg--185 {
    padding-top: 185px !important;
  }
  .pb_lg--185 {
    padding-bottom: 185px !important;
  }
  .pl_lg--185 {
    padding-left: 185px !important;
  }
  .pr_lg--185 {
    padding-right: 185px !important;
  }
  .mt_lg--185 {
    margin-top: 185px !important;
  }
  .mb_lg--185 {
    margin-bottom: 185px !important;
  }
  .ml_lg--185 {
    margin-left: 185px !important;
  }
  .ptb_lg--190 {
    padding: 190px 0 !important;
  }
  .plr_lg--190 {
    padding: 0 190px !important;
  }
  .pt_lg--190 {
    padding-top: 190px !important;
  }
  .pb_lg--190 {
    padding-bottom: 190px !important;
  }
  .pl_lg--190 {
    padding-left: 190px !important;
  }
  .pr_lg--190 {
    padding-right: 190px !important;
  }
  .mt_lg--190 {
    margin-top: 190px !important;
  }
  .mb_lg--190 {
    margin-bottom: 190px !important;
  }
  .ml_lg--190 {
    margin-left: 190px !important;
  }
  .ptb_lg--195 {
    padding: 195px 0 !important;
  }
  .plr_lg--195 {
    padding: 0 195px !important;
  }
  .pt_lg--195 {
    padding-top: 195px !important;
  }
  .pb_lg--195 {
    padding-bottom: 195px !important;
  }
  .pl_lg--195 {
    padding-left: 195px !important;
  }
  .pr_lg--195 {
    padding-right: 195px !important;
  }
  .mt_lg--195 {
    margin-top: 195px !important;
  }
  .mb_lg--195 {
    margin-bottom: 195px !important;
  }
  .ml_lg--195 {
    margin-left: 195px !important;
  }
  .ptb_lg--200 {
    padding: 200px 0 !important;
  }
  .plr_lg--200 {
    padding: 0 200px !important;
  }
  .pt_lg--200 {
    padding-top: 200px !important;
  }
  .pb_lg--200 {
    padding-bottom: 200px !important;
  }
  .pl_lg--200 {
    padding-left: 200px !important;
  }
  .pr_lg--200 {
    padding-right: 200px !important;
  }
  .mt_lg--200 {
    margin-top: 200px !important;
  }
  .mb_lg--200 {
    margin-bottom: 200px !important;
  }
  .ml_lg--200 {
    margin-left: 200px !important;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ptb_md--0 {
    padding: 0 !important;
  }
  .pl_md--0 {
    padding-left: 0 !important;
  }
  .pr_md--0 {
    padding-right: 0 !important;
  }
  .pt_md--0 {
    padding-top: 0 !important;
  }
  .pb_md--0 {
    padding-bottom: 0 !important;
  }
  .mr_md--0 {
    margin-right: 0 !important;
  }
  .ml_md--0 {
    margin-left: 0 !important;
  }
  .mt_md--0 {
    margin-top: 0 !important;
  }
  .mb_md--0 {
    margin-bottom: 0 !important;
  }
  .ptb_md--250 {
    padding: 250px 0 !important;
  }
  .ptb_md--5 {
    padding: 5px 0 !important;
  }
  .plr_md--5 {
    padding: 0 5px !important;
  }
  .pt_md--5 {
    padding-top: 5px !important;
  }
  .pb_md--5 {
    padding-bottom: 5px !important;
  }
  .pl_md--5 {
    padding-left: 5px !important;
  }
  .pr_md--5 {
    padding-right: 5px !important;
  }
  .mt_md--5 {
    margin-top: 5px !important;
  }
  .mb_md--5 {
    margin-bottom: 5px !important;
  }
  .ptb_md--10 {
    padding: 10px 0 !important;
  }
  .plr_md--10 {
    padding: 0 10px !important;
  }
  .pt_md--10 {
    padding-top: 10px !important;
  }
  .pb_md--10 {
    padding-bottom: 10px !important;
  }
  .pl_md--10 {
    padding-left: 10px !important;
  }
  .pr_md--10 {
    padding-right: 10px !important;
  }
  .mt_md--10 {
    margin-top: 10px !important;
  }
  .mb_md--10 {
    margin-bottom: 10px !important;
  }
  .ptb_md--15 {
    padding: 15px 0 !important;
  }
  .plr_md--15 {
    padding: 0 15px !important;
  }
  .pt_md--15 {
    padding-top: 15px !important;
  }
  .pb_md--15 {
    padding-bottom: 15px !important;
  }
  .pl_md--15 {
    padding-left: 15px !important;
  }
  .pr_md--15 {
    padding-right: 15px !important;
  }
  .mt_md--15 {
    margin-top: 15px !important;
  }
  .mb_md--15 {
    margin-bottom: 15px !important;
  }
  .ptb_md--20 {
    padding: 20px 0 !important;
  }
  .plr_md--20 {
    padding: 0 20px !important;
  }
  .pt_md--20 {
    padding-top: 20px !important;
  }
  .pb_md--20 {
    padding-bottom: 20px !important;
  }
  .pl_md--20 {
    padding-left: 20px !important;
  }
  .pr_md--20 {
    padding-right: 20px !important;
  }
  .mt_md--20 {
    margin-top: 20px !important;
  }
  .mb_md--20 {
    margin-bottom: 20px !important;
  }
  .ptb_md--25 {
    padding: 25px 0 !important;
  }
  .plr_md--25 {
    padding: 0 25px !important;
  }
  .pt_md--25 {
    padding-top: 25px !important;
  }
  .pb_md--25 {
    padding-bottom: 25px !important;
  }
  .pl_md--25 {
    padding-left: 25px !important;
  }
  .pr_md--25 {
    padding-right: 25px !important;
  }
  .mt_md--25 {
    margin-top: 25px !important;
  }
  .mb_md--25 {
    margin-bottom: 25px !important;
  }
  .ptb_md--30 {
    padding: 30px 0 !important;
  }
  .plr_md--30 {
    padding: 0 30px !important;
  }
  .pt_md--30 {
    padding-top: 30px !important;
  }
  .pb_md--30 {
    padding-bottom: 30px !important;
  }
  .pl_md--30 {
    padding-left: 30px !important;
  }
  .pr_md--30 {
    padding-right: 30px !important;
  }
  .mt_md--30 {
    margin-top: 30px !important;
  }
  .mb_md--30 {
    margin-bottom: 30px !important;
  }
  .ptb_md--35 {
    padding: 35px 0 !important;
  }
  .plr_md--35 {
    padding: 0 35px !important;
  }
  .pt_md--35 {
    padding-top: 35px !important;
  }
  .pb_md--35 {
    padding-bottom: 35px !important;
  }
  .pl_md--35 {
    padding-left: 35px !important;
  }
  .pr_md--35 {
    padding-right: 35px !important;
  }
  .mt_md--35 {
    margin-top: 35px !important;
  }
  .mb_md--35 {
    margin-bottom: 35px !important;
  }
  .ptb_md--40 {
    padding: 40px 0 !important;
  }
  .plr_md--40 {
    padding: 0 40px !important;
  }
  .pt_md--40 {
    padding-top: 40px !important;
  }
  .pb_md--40 {
    padding-bottom: 40px !important;
  }
  .pl_md--40 {
    padding-left: 40px !important;
  }
  .pr_md--40 {
    padding-right: 40px !important;
  }
  .mt_md--40 {
    margin-top: 40px !important;
  }
  .mb_md--40 {
    margin-bottom: 40px !important;
  }
  .ptb_md--45 {
    padding: 45px 0 !important;
  }
  .plr_md--45 {
    padding: 0 45px !important;
  }
  .pt_md--45 {
    padding-top: 45px !important;
  }
  .pb_md--45 {
    padding-bottom: 45px !important;
  }
  .pl_md--45 {
    padding-left: 45px !important;
  }
  .pr_md--45 {
    padding-right: 45px !important;
  }
  .mt_md--45 {
    margin-top: 45px !important;
  }
  .mb_md--45 {
    margin-bottom: 45px !important;
  }
  .ptb_md--50 {
    padding: 50px 0 !important;
  }
  .plr_md--50 {
    padding: 0 50px !important;
  }
  .pt_md--50 {
    padding-top: 50px !important;
  }
  .pb_md--50 {
    padding-bottom: 50px !important;
  }
  .pl_md--50 {
    padding-left: 50px !important;
  }
  .pr_md--50 {
    padding-right: 50px !important;
  }
  .mt_md--50 {
    margin-top: 50px !important;
  }
  .mb_md--50 {
    margin-bottom: 50px !important;
  }
  .ptb_md--55 {
    padding: 55px 0 !important;
  }
  .plr_md--55 {
    padding: 0 55px !important;
  }
  .pt_md--55 {
    padding-top: 55px !important;
  }
  .pb_md--55 {
    padding-bottom: 55px !important;
  }
  .pl_md--55 {
    padding-left: 55px !important;
  }
  .pr_md--55 {
    padding-right: 55px !important;
  }
  .mt_md--55 {
    margin-top: 55px !important;
  }
  .mb_md--55 {
    margin-bottom: 55px !important;
  }
  .ptb_md--60 {
    padding: 60px 0 !important;
  }
  .plr_md--60 {
    padding: 0 60px !important;
  }
  .pt_md--60 {
    padding-top: 60px !important;
  }
  .pb_md--60 {
    padding-bottom: 60px !important;
  }
  .pl_md--60 {
    padding-left: 60px !important;
  }
  .pr_md--60 {
    padding-right: 60px !important;
  }
  .mt_md--60 {
    margin-top: 60px !important;
  }
  .mb_md--60 {
    margin-bottom: 60px !important;
  }
  .ptb_md--65 {
    padding: 65px 0 !important;
  }
  .plr_md--65 {
    padding: 0 65px !important;
  }
  .pt_md--65 {
    padding-top: 65px !important;
  }
  .pb_md--65 {
    padding-bottom: 65px !important;
  }
  .pl_md--65 {
    padding-left: 65px !important;
  }
  .pr_md--65 {
    padding-right: 65px !important;
  }
  .mt_md--65 {
    margin-top: 65px !important;
  }
  .mb_md--65 {
    margin-bottom: 65px !important;
  }
  .ptb_md--70 {
    padding: 70px 0 !important;
  }
  .plr_md--70 {
    padding: 0 70px !important;
  }
  .pt_md--70 {
    padding-top: 70px !important;
  }
  .pb_md--70 {
    padding-bottom: 70px !important;
  }
  .pl_md--70 {
    padding-left: 70px !important;
  }
  .pr_md--70 {
    padding-right: 70px !important;
  }
  .mt_md--70 {
    margin-top: 70px !important;
  }
  .mb_md--70 {
    margin-bottom: 70px !important;
  }
  .ptb_md--75 {
    padding: 75px 0 !important;
  }
  .plr_md--75 {
    padding: 0 75px !important;
  }
  .pt_md--75 {
    padding-top: 75px !important;
  }
  .pb_md--75 {
    padding-bottom: 75px !important;
  }
  .pl_md--75 {
    padding-left: 75px !important;
  }
  .pr_md--75 {
    padding-right: 75px !important;
  }
  .mt_md--75 {
    margin-top: 75px !important;
  }
  .mb_md--75 {
    margin-bottom: 75px !important;
  }
  .ptb_md--80 {
    padding: 80px 0 !important;
  }
  .plr_md--80 {
    padding: 0 80px !important;
  }
  .pt_md--80 {
    padding-top: 80px !important;
  }
  .pb_md--80 {
    padding-bottom: 80px !important;
  }
  .pl_md--80 {
    padding-left: 80px !important;
  }
  .pr_md--80 {
    padding-right: 80px !important;
  }
  .mt_md--80 {
    margin-top: 80px !important;
  }
  .mb_md--80 {
    margin-bottom: 80px !important;
  }
  .ptb_md--85 {
    padding: 85px 0 !important;
  }
  .plr_md--85 {
    padding: 0 85px !important;
  }
  .pt_md--85 {
    padding-top: 85px !important;
  }
  .pb_md--85 {
    padding-bottom: 85px !important;
  }
  .pl_md--85 {
    padding-left: 85px !important;
  }
  .pr_md--85 {
    padding-right: 85px !important;
  }
  .mt_md--85 {
    margin-top: 85px !important;
  }
  .mb_md--85 {
    margin-bottom: 85px !important;
  }
  .ptb_md--90 {
    padding: 90px 0 !important;
  }
  .plr_md--90 {
    padding: 0 90px !important;
  }
  .pt_md--90 {
    padding-top: 90px !important;
  }
  .pb_md--90 {
    padding-bottom: 90px !important;
  }
  .pl_md--90 {
    padding-left: 90px !important;
  }
  .pr_md--90 {
    padding-right: 90px !important;
  }
  .mt_md--90 {
    margin-top: 90px !important;
  }
  .mb_md--90 {
    margin-bottom: 90px !important;
  }
  .ptb_md--95 {
    padding: 95px 0 !important;
  }
  .plr_md--95 {
    padding: 0 95px !important;
  }
  .pt_md--95 {
    padding-top: 95px !important;
  }
  .pb_md--95 {
    padding-bottom: 95px !important;
  }
  .pl_md--95 {
    padding-left: 95px !important;
  }
  .pr_md--95 {
    padding-right: 95px !important;
  }
  .mt_md--95 {
    margin-top: 95px !important;
  }
  .mb_md--95 {
    margin-bottom: 95px !important;
  }
  .ptb_md--100 {
    padding: 100px 0 !important;
  }
  .plr_md--100 {
    padding: 0 100px !important;
  }
  .pt_md--100 {
    padding-top: 100px !important;
  }
  .pb_md--100 {
    padding-bottom: 100px !important;
  }
  .pl_md--100 {
    padding-left: 100px !important;
  }
  .pr_md--100 {
    padding-right: 100px !important;
  }
  .mt_md--100 {
    margin-top: 100px !important;
  }
  .mb_md--100 {
    margin-bottom: 100px !important;
  }
  .ptb_md--105 {
    padding: 105px 0 !important;
  }
  .plr_md--105 {
    padding: 0 105px !important;
  }
  .pt_md--105 {
    padding-top: 105px !important;
  }
  .pb_md--105 {
    padding-bottom: 105px !important;
  }
  .pl_md--105 {
    padding-left: 105px !important;
  }
  .pr_md--105 {
    padding-right: 105px !important;
  }
  .mt_md--105 {
    margin-top: 105px !important;
  }
  .mb_md--105 {
    margin-bottom: 105px !important;
  }
  .ptb_md--110 {
    padding: 110px 0 !important;
  }
  .plr_md--110 {
    padding: 0 110px !important;
  }
  .pt_md--110 {
    padding-top: 110px !important;
  }
  .pb_md--110 {
    padding-bottom: 110px !important;
  }
  .pl_md--110 {
    padding-left: 110px !important;
  }
  .pr_md--110 {
    padding-right: 110px !important;
  }
  .mt_md--110 {
    margin-top: 110px !important;
  }
  .mb_md--110 {
    margin-bottom: 110px !important;
  }
  .ptb_md--115 {
    padding: 115px 0 !important;
  }
  .plr_md--115 {
    padding: 0 115px !important;
  }
  .pt_md--115 {
    padding-top: 115px !important;
  }
  .pb_md--115 {
    padding-bottom: 115px !important;
  }
  .pl_md--115 {
    padding-left: 115px !important;
  }
  .pr_md--115 {
    padding-right: 115px !important;
  }
  .mt_md--115 {
    margin-top: 115px !important;
  }
  .mb_md--115 {
    margin-bottom: 115px !important;
  }
  .ptb_md--120 {
    padding: 120px 0 !important;
  }
  .plr_md--120 {
    padding: 0 120px !important;
  }
  .pt_md--120 {
    padding-top: 120px !important;
  }
  .pb_md--120 {
    padding-bottom: 120px !important;
  }
  .pl_md--120 {
    padding-left: 120px !important;
  }
  .pr_md--120 {
    padding-right: 120px !important;
  }
  .mt_md--120 {
    margin-top: 120px !important;
  }
  .mb_md--120 {
    margin-bottom: 120px !important;
  }
  .ptb_md--125 {
    padding: 125px 0 !important;
  }
  .plr_md--125 {
    padding: 0 125px !important;
  }
  .pt_md--125 {
    padding-top: 125px !important;
  }
  .pb_md--125 {
    padding-bottom: 125px !important;
  }
  .pl_md--125 {
    padding-left: 125px !important;
  }
  .pr_md--125 {
    padding-right: 125px !important;
  }
  .mt_md--125 {
    margin-top: 125px !important;
  }
  .mb_md--125 {
    margin-bottom: 125px !important;
  }
  .ptb_md--130 {
    padding: 130px 0 !important;
  }
  .plr_md--130 {
    padding: 0 130px !important;
  }
  .pt_md--130 {
    padding-top: 130px !important;
  }
  .pb_md--130 {
    padding-bottom: 130px !important;
  }
  .pl_md--130 {
    padding-left: 130px !important;
  }
  .pr_md--130 {
    padding-right: 130px !important;
  }
  .mt_md--130 {
    margin-top: 130px !important;
  }
  .mb_md--130 {
    margin-bottom: 130px !important;
  }
  .ptb_md--135 {
    padding: 135px 0 !important;
  }
  .plr_md--135 {
    padding: 0 135px !important;
  }
  .pt_md--135 {
    padding-top: 135px !important;
  }
  .pb_md--135 {
    padding-bottom: 135px !important;
  }
  .pl_md--135 {
    padding-left: 135px !important;
  }
  .pr_md--135 {
    padding-right: 135px !important;
  }
  .mt_md--135 {
    margin-top: 135px !important;
  }
  .mb_md--135 {
    margin-bottom: 135px !important;
  }
  .ptb_md--140 {
    padding: 140px 0 !important;
  }
  .plr_md--140 {
    padding: 0 140px !important;
  }
  .pt_md--140 {
    padding-top: 140px !important;
  }
  .pb_md--140 {
    padding-bottom: 140px !important;
  }
  .pl_md--140 {
    padding-left: 140px !important;
  }
  .pr_md--140 {
    padding-right: 140px !important;
  }
  .mt_md--140 {
    margin-top: 140px !important;
  }
  .mb_md--140 {
    margin-bottom: 140px !important;
  }
  .ptb_md--145 {
    padding: 145px 0 !important;
  }
  .plr_md--145 {
    padding: 0 145px !important;
  }
  .pt_md--145 {
    padding-top: 145px !important;
  }
  .pb_md--145 {
    padding-bottom: 145px !important;
  }
  .pl_md--145 {
    padding-left: 145px !important;
  }
  .pr_md--145 {
    padding-right: 145px !important;
  }
  .mt_md--145 {
    margin-top: 145px !important;
  }
  .mb_md--145 {
    margin-bottom: 145px !important;
  }
  .ptb_md--150 {
    padding: 150px 0 !important;
  }
  .plr_md--150 {
    padding: 0 150px !important;
  }
  .pt_md--150 {
    padding-top: 150px !important;
  }
  .pb_md--150 {
    padding-bottom: 150px !important;
  }
  .pl_md--150 {
    padding-left: 150px !important;
  }
  .pr_md--150 {
    padding-right: 150px !important;
  }
  .mt_md--150 {
    margin-top: 150px !important;
  }
  .mb_md--150 {
    margin-bottom: 150px !important;
  }
  .ptb_md--155 {
    padding: 155px 0 !important;
  }
  .plr_md--155 {
    padding: 0 155px !important;
  }
  .pt_md--155 {
    padding-top: 155px !important;
  }
  .pb_md--155 {
    padding-bottom: 155px !important;
  }
  .pl_md--155 {
    padding-left: 155px !important;
  }
  .pr_md--155 {
    padding-right: 155px !important;
  }
  .mt_md--155 {
    margin-top: 155px !important;
  }
  .mb_md--155 {
    margin-bottom: 155px !important;
  }
  .ptb_md--160 {
    padding: 160px 0 !important;
  }
  .plr_md--160 {
    padding: 0 160px !important;
  }
  .pt_md--160 {
    padding-top: 160px !important;
  }
  .pb_md--160 {
    padding-bottom: 160px !important;
  }
  .pl_md--160 {
    padding-left: 160px !important;
  }
  .pr_md--160 {
    padding-right: 160px !important;
  }
  .mt_md--160 {
    margin-top: 160px !important;
  }
  .mb_md--160 {
    margin-bottom: 160px !important;
  }
  .ptb_md--165 {
    padding: 165px 0 !important;
  }
  .plr_md--165 {
    padding: 0 165px !important;
  }
  .pt_md--165 {
    padding-top: 165px !important;
  }
  .pb_md--165 {
    padding-bottom: 165px !important;
  }
  .pl_md--165 {
    padding-left: 165px !important;
  }
  .pr_md--165 {
    padding-right: 165px !important;
  }
  .mt_md--165 {
    margin-top: 165px !important;
  }
  .mb_md--165 {
    margin-bottom: 165px !important;
  }
  .ptb_md--170 {
    padding: 170px 0 !important;
  }
  .plr_md--170 {
    padding: 0 170px !important;
  }
  .pt_md--170 {
    padding-top: 170px !important;
  }
  .pb_md--170 {
    padding-bottom: 170px !important;
  }
  .pl_md--170 {
    padding-left: 170px !important;
  }
  .pr_md--170 {
    padding-right: 170px !important;
  }
  .mt_md--170 {
    margin-top: 170px !important;
  }
  .mb_md--170 {
    margin-bottom: 170px !important;
  }
  .ptb_md--175 {
    padding: 175px 0 !important;
  }
  .plr_md--175 {
    padding: 0 175px !important;
  }
  .pt_md--175 {
    padding-top: 175px !important;
  }
  .pb_md--175 {
    padding-bottom: 175px !important;
  }
  .pl_md--175 {
    padding-left: 175px !important;
  }
  .pr_md--175 {
    padding-right: 175px !important;
  }
  .mt_md--175 {
    margin-top: 175px !important;
  }
  .mb_md--175 {
    margin-bottom: 175px !important;
  }
  .ptb_md--180 {
    padding: 180px 0 !important;
  }
  .plr_md--180 {
    padding: 0 180px !important;
  }
  .pt_md--180 {
    padding-top: 180px !important;
  }
  .pb_md--180 {
    padding-bottom: 180px !important;
  }
  .pl_md--180 {
    padding-left: 180px !important;
  }
  .pr_md--180 {
    padding-right: 180px !important;
  }
  .mt_md--180 {
    margin-top: 180px !important;
  }
  .mb_md--180 {
    margin-bottom: 180px !important;
  }
  .ptb_md--185 {
    padding: 185px 0 !important;
  }
  .plr_md--185 {
    padding: 0 185px !important;
  }
  .pt_md--185 {
    padding-top: 185px !important;
  }
  .pb_md--185 {
    padding-bottom: 185px !important;
  }
  .pl_md--185 {
    padding-left: 185px !important;
  }
  .pr_md--185 {
    padding-right: 185px !important;
  }
  .mt_md--185 {
    margin-top: 185px !important;
  }
  .mb_md--185 {
    margin-bottom: 185px !important;
  }
  .ptb_md--190 {
    padding: 190px 0 !important;
  }
  .plr_md--190 {
    padding: 0 190px !important;
  }
  .pt_md--190 {
    padding-top: 190px !important;
  }
  .pb_md--190 {
    padding-bottom: 190px !important;
  }
  .pl_md--190 {
    padding-left: 190px !important;
  }
  .pr_md--190 {
    padding-right: 190px !important;
  }
  .mt_md--190 {
    margin-top: 190px !important;
  }
  .mb_md--190 {
    margin-bottom: 190px !important;
  }
  .ptb_md--195 {
    padding: 195px 0 !important;
  }
  .plr_md--195 {
    padding: 0 195px !important;
  }
  .pt_md--195 {
    padding-top: 195px !important;
  }
  .pb_md--195 {
    padding-bottom: 195px !important;
  }
  .pl_md--195 {
    padding-left: 195px !important;
  }
  .pr_md--195 {
    padding-right: 195px !important;
  }
  .mt_md--195 {
    margin-top: 195px !important;
  }
  .mb_md--195 {
    margin-bottom: 195px !important;
  }
  .ptb_md--200 {
    padding: 200px 0 !important;
  }
  .plr_md--200 {
    padding: 0 200px !important;
  }
  .pt_md--200 {
    padding-top: 200px !important;
  }
  .pb_md--200 {
    padding-bottom: 200px !important;
  }
  .pl_md--200 {
    padding-left: 200px !important;
  }
  .pr_md--200 {
    padding-right: 200px !important;
  }
  .mt_md--200 {
    margin-top: 200px !important;
  }
  .mb_md--200 {
    margin-bottom: 200px !important;
  }
}
@media only screen and (max-width: 767px) {
  .ptb_sm--250 {
    padding: 250px 0 !important;
  }
  .ptb_sm--0 {
    padding: 0 !important;
  }
  .pl_sm--0 {
    padding-left: 0 !important;
  }
  .pr_sm--0 {
    padding-right: 0 !important;
  }
  .pt_sm--0 {
    padding-top: 0 !important;
  }
  .pb_sm--0 {
    padding-bottom: 0 !important;
  }
  .mr_sm--0 {
    margin-right: 0 !important;
  }
  .ml_sm--0 {
    margin-left: 0 !important;
  }
  .mt_sm--0 {
    margin-top: 0 !important;
  }
  .mb_sm--0 {
    margin-bottom: 0 !important;
  }
  .pt_sm--150 {
    padding-top: 150px !important;
  }
  .pb_sm--110 {
    padding-bottom: 110px !important;
  }
  .ptb_sm--5 {
    padding: 5px 0 !important;
  }
  .plr_sm--5 {
    padding: 0 5px !important;
  }
  .pt_sm--5 {
    padding-top: 5px !important;
  }
  .pb_sm--5 {
    padding-bottom: 5px !important;
  }
  .pl_sm--5 {
    padding-left: 5px !important;
  }
  .pr_sm--5 {
    padding-right: 5px !important;
  }
  .mt_sm--5 {
    margin-top: 5px !important;
  }
  .ml_sm--5 {
    margin-left: 5px !important;
  }
  .mr_sm--5 {
    margin-right: 5px !important;
  }
  .mb_sm--5 {
    margin-bottom: 5px !important;
  }
  .ptb_sm--10 {
    padding: 10px 0 !important;
  }
  .plr_sm--10 {
    padding: 0 10px !important;
  }
  .pt_sm--10 {
    padding-top: 10px !important;
  }
  .pb_sm--10 {
    padding-bottom: 10px !important;
  }
  .pl_sm--10 {
    padding-left: 10px !important;
  }
  .pr_sm--10 {
    padding-right: 10px !important;
  }
  .mt_sm--10 {
    margin-top: 10px !important;
  }
  .ml_sm--10 {
    margin-left: 10px !important;
  }
  .mr_sm--10 {
    margin-right: 10px !important;
  }
  .mb_sm--10 {
    margin-bottom: 10px !important;
  }
  .ptb_sm--15 {
    padding: 15px 0 !important;
  }
  .plr_sm--15 {
    padding: 0 15px !important;
  }
  .pt_sm--15 {
    padding-top: 15px !important;
  }
  .pb_sm--15 {
    padding-bottom: 15px !important;
  }
  .pl_sm--15 {
    padding-left: 15px !important;
  }
  .pr_sm--15 {
    padding-right: 15px !important;
  }
  .mt_sm--15 {
    margin-top: 15px !important;
  }
  .ml_sm--15 {
    margin-left: 15px !important;
  }
  .mr_sm--15 {
    margin-right: 15px !important;
  }
  .mb_sm--15 {
    margin-bottom: 15px !important;
  }
  .ptb_sm--20 {
    padding: 20px 0 !important;
  }
  .plr_sm--20 {
    padding: 0 20px !important;
  }
  .pt_sm--20 {
    padding-top: 20px !important;
  }
  .pb_sm--20 {
    padding-bottom: 20px !important;
  }
  .pl_sm--20 {
    padding-left: 20px !important;
  }
  .pr_sm--20 {
    padding-right: 20px !important;
  }
  .mt_sm--20 {
    margin-top: 20px !important;
  }
  .ml_sm--20 {
    margin-left: 20px !important;
  }
  .mr_sm--20 {
    margin-right: 20px !important;
  }
  .mb_sm--20 {
    margin-bottom: 20px !important;
  }
  .ptb_sm--25 {
    padding: 25px 0 !important;
  }
  .plr_sm--25 {
    padding: 0 25px !important;
  }
  .pt_sm--25 {
    padding-top: 25px !important;
  }
  .pb_sm--25 {
    padding-bottom: 25px !important;
  }
  .pl_sm--25 {
    padding-left: 25px !important;
  }
  .pr_sm--25 {
    padding-right: 25px !important;
  }
  .mt_sm--25 {
    margin-top: 25px !important;
  }
  .ml_sm--25 {
    margin-left: 25px !important;
  }
  .mr_sm--25 {
    margin-right: 25px !important;
  }
  .mb_sm--25 {
    margin-bottom: 25px !important;
  }
  .ptb_sm--30 {
    padding: 30px 0 !important;
  }
  .plr_sm--30 {
    padding: 0 30px !important;
  }
  .pt_sm--30 {
    padding-top: 30px !important;
  }
  .pb_sm--30 {
    padding-bottom: 30px !important;
  }
  .pl_sm--30 {
    padding-left: 30px !important;
  }
  .pr_sm--30 {
    padding-right: 30px !important;
  }
  .mt_sm--30 {
    margin-top: 30px !important;
  }
  .ml_sm--30 {
    margin-left: 30px !important;
  }
  .mr_sm--30 {
    margin-right: 30px !important;
  }
  .mb_sm--30 {
    margin-bottom: 30px !important;
  }
  .ptb_sm--35 {
    padding: 35px 0 !important;
  }
  .plr_sm--35 {
    padding: 0 35px !important;
  }
  .pt_sm--35 {
    padding-top: 35px !important;
  }
  .pb_sm--35 {
    padding-bottom: 35px !important;
  }
  .pl_sm--35 {
    padding-left: 35px !important;
  }
  .pr_sm--35 {
    padding-right: 35px !important;
  }
  .mt_sm--35 {
    margin-top: 35px !important;
  }
  .ml_sm--35 {
    margin-left: 35px !important;
  }
  .mr_sm--35 {
    margin-right: 35px !important;
  }
  .mb_sm--35 {
    margin-bottom: 35px !important;
  }
  .ptb_sm--40 {
    padding: 40px 0 !important;
  }
  .plr_sm--40 {
    padding: 0 40px !important;
  }
  .pt_sm--40 {
    padding-top: 40px !important;
  }
  .pb_sm--40 {
    padding-bottom: 40px !important;
  }
  .pl_sm--40 {
    padding-left: 40px !important;
  }
  .pr_sm--40 {
    padding-right: 40px !important;
  }
  .mt_sm--40 {
    margin-top: 40px !important;
  }
  .ml_sm--40 {
    margin-left: 40px !important;
  }
  .mr_sm--40 {
    margin-right: 40px !important;
  }
  .mb_sm--40 {
    margin-bottom: 40px !important;
  }
  .ptb_sm--45 {
    padding: 45px 0 !important;
  }
  .plr_sm--45 {
    padding: 0 45px !important;
  }
  .pt_sm--45 {
    padding-top: 45px !important;
  }
  .pb_sm--45 {
    padding-bottom: 45px !important;
  }
  .pl_sm--45 {
    padding-left: 45px !important;
  }
  .pr_sm--45 {
    padding-right: 45px !important;
  }
  .mt_sm--45 {
    margin-top: 45px !important;
  }
  .ml_sm--45 {
    margin-left: 45px !important;
  }
  .mr_sm--45 {
    margin-right: 45px !important;
  }
  .mb_sm--45 {
    margin-bottom: 45px !important;
  }
  .ptb_sm--50 {
    padding: 50px 0 !important;
  }
  .plr_sm--50 {
    padding: 0 50px !important;
  }
  .pt_sm--50 {
    padding-top: 50px !important;
  }
  .pb_sm--50 {
    padding-bottom: 50px !important;
  }
  .pl_sm--50 {
    padding-left: 50px !important;
  }
  .pr_sm--50 {
    padding-right: 50px !important;
  }
  .mt_sm--50 {
    margin-top: 50px !important;
  }
  .ml_sm--50 {
    margin-left: 50px !important;
  }
  .mr_sm--50 {
    margin-right: 50px !important;
  }
  .mb_sm--50 {
    margin-bottom: 50px !important;
  }
  .ptb_sm--55 {
    padding: 55px 0 !important;
  }
  .plr_sm--55 {
    padding: 0 55px !important;
  }
  .pt_sm--55 {
    padding-top: 55px !important;
  }
  .pb_sm--55 {
    padding-bottom: 55px !important;
  }
  .pl_sm--55 {
    padding-left: 55px !important;
  }
  .pr_sm--55 {
    padding-right: 55px !important;
  }
  .mt_sm--55 {
    margin-top: 55px !important;
  }
  .ml_sm--55 {
    margin-left: 55px !important;
  }
  .mr_sm--55 {
    margin-right: 55px !important;
  }
  .mb_sm--55 {
    margin-bottom: 55px !important;
  }
  .ptb_sm--60 {
    padding: 60px 0 !important;
  }
  .plr_sm--60 {
    padding: 0 60px !important;
  }
  .pt_sm--60 {
    padding-top: 60px !important;
  }
  .pb_sm--60 {
    padding-bottom: 60px !important;
  }
  .pl_sm--60 {
    padding-left: 60px !important;
  }
  .pr_sm--60 {
    padding-right: 60px !important;
  }
  .mt_sm--60 {
    margin-top: 60px !important;
  }
  .ml_sm--60 {
    margin-left: 60px !important;
  }
  .mr_sm--60 {
    margin-right: 60px !important;
  }
  .mb_sm--60 {
    margin-bottom: 60px !important;
  }
  .ptb_sm--65 {
    padding: 65px 0 !important;
  }
  .plr_sm--65 {
    padding: 0 65px !important;
  }
  .pt_sm--65 {
    padding-top: 65px !important;
  }
  .pb_sm--65 {
    padding-bottom: 65px !important;
  }
  .pl_sm--65 {
    padding-left: 65px !important;
  }
  .pr_sm--65 {
    padding-right: 65px !important;
  }
  .mt_sm--65 {
    margin-top: 65px !important;
  }
  .ml_sm--65 {
    margin-left: 65px !important;
  }
  .mr_sm--65 {
    margin-right: 65px !important;
  }
  .mb_sm--65 {
    margin-bottom: 65px !important;
  }
  .ptb_sm--70 {
    padding: 70px 0 !important;
  }
  .plr_sm--70 {
    padding: 0 70px !important;
  }
  .pt_sm--70 {
    padding-top: 70px !important;
  }
  .pb_sm--70 {
    padding-bottom: 70px !important;
  }
  .pl_sm--70 {
    padding-left: 70px !important;
  }
  .pr_sm--70 {
    padding-right: 70px !important;
  }
  .mt_sm--70 {
    margin-top: 70px !important;
  }
  .ml_sm--70 {
    margin-left: 70px !important;
  }
  .mr_sm--70 {
    margin-right: 70px !important;
  }
  .mb_sm--70 {
    margin-bottom: 70px !important;
  }
  .ptb_sm--75 {
    padding: 75px 0 !important;
  }
  .plr_sm--75 {
    padding: 0 75px !important;
  }
  .pt_sm--75 {
    padding-top: 75px !important;
  }
  .pb_sm--75 {
    padding-bottom: 75px !important;
  }
  .pl_sm--75 {
    padding-left: 75px !important;
  }
  .pr_sm--75 {
    padding-right: 75px !important;
  }
  .mt_sm--75 {
    margin-top: 75px !important;
  }
  .ml_sm--75 {
    margin-left: 75px !important;
  }
  .mr_sm--75 {
    margin-right: 75px !important;
  }
  .mb_sm--75 {
    margin-bottom: 75px !important;
  }
  .ptb_sm--80 {
    padding: 80px 0 !important;
  }
  .plr_sm--80 {
    padding: 0 80px !important;
  }
  .pt_sm--80 {
    padding-top: 80px !important;
  }
  .pb_sm--80 {
    padding-bottom: 80px !important;
  }
  .pl_sm--80 {
    padding-left: 80px !important;
  }
  .pr_sm--80 {
    padding-right: 80px !important;
  }
  .mt_sm--80 {
    margin-top: 80px !important;
  }
  .ml_sm--80 {
    margin-left: 80px !important;
  }
  .mr_sm--80 {
    margin-right: 80px !important;
  }
  .mb_sm--80 {
    margin-bottom: 80px !important;
  }
  .ptb_sm--85 {
    padding: 85px 0 !important;
  }
  .plr_sm--85 {
    padding: 0 85px !important;
  }
  .pt_sm--85 {
    padding-top: 85px !important;
  }
  .pb_sm--85 {
    padding-bottom: 85px !important;
  }
  .pl_sm--85 {
    padding-left: 85px !important;
  }
  .pr_sm--85 {
    padding-right: 85px !important;
  }
  .mt_sm--85 {
    margin-top: 85px !important;
  }
  .ml_sm--85 {
    margin-left: 85px !important;
  }
  .mr_sm--85 {
    margin-right: 85px !important;
  }
  .mb_sm--85 {
    margin-bottom: 85px !important;
  }
  .ptb_sm--90 {
    padding: 90px 0 !important;
  }
  .plr_sm--90 {
    padding: 0 90px !important;
  }
  .pt_sm--90 {
    padding-top: 90px !important;
  }
  .pb_sm--90 {
    padding-bottom: 90px !important;
  }
  .pl_sm--90 {
    padding-left: 90px !important;
  }
  .pr_sm--90 {
    padding-right: 90px !important;
  }
  .mt_sm--90 {
    margin-top: 90px !important;
  }
  .ml_sm--90 {
    margin-left: 90px !important;
  }
  .mr_sm--90 {
    margin-right: 90px !important;
  }
  .mb_sm--90 {
    margin-bottom: 90px !important;
  }
  .ptb_sm--95 {
    padding: 95px 0 !important;
  }
  .plr_sm--95 {
    padding: 0 95px !important;
  }
  .pt_sm--95 {
    padding-top: 95px !important;
  }
  .pb_sm--95 {
    padding-bottom: 95px !important;
  }
  .pl_sm--95 {
    padding-left: 95px !important;
  }
  .pr_sm--95 {
    padding-right: 95px !important;
  }
  .mt_sm--95 {
    margin-top: 95px !important;
  }
  .ml_sm--95 {
    margin-left: 95px !important;
  }
  .mr_sm--95 {
    margin-right: 95px !important;
  }
  .mb_sm--95 {
    margin-bottom: 95px !important;
  }
  .ptb_sm--100 {
    padding: 100px 0 !important;
  }
  .plr_sm--100 {
    padding: 0 100px !important;
  }
  .pt_sm--100 {
    padding-top: 100px !important;
  }
  .pb_sm--100 {
    padding-bottom: 100px !important;
  }
  .pl_sm--100 {
    padding-left: 100px !important;
  }
  .pr_sm--100 {
    padding-right: 100px !important;
  }
  .mt_sm--100 {
    margin-top: 100px !important;
  }
  .ml_sm--100 {
    margin-left: 100px !important;
  }
  .mr_sm--100 {
    margin-right: 100px !important;
  }
  .mb_sm--100 {
    margin-bottom: 100px !important;
  }
  .ptb_sm--105 {
    padding: 105px 0 !important;
  }
  .plr_sm--105 {
    padding: 0 105px !important;
  }
  .pt_sm--105 {
    padding-top: 105px !important;
  }
  .pb_sm--105 {
    padding-bottom: 105px !important;
  }
  .pl_sm--105 {
    padding-left: 105px !important;
  }
  .pr_sm--105 {
    padding-right: 105px !important;
  }
  .mt_sm--105 {
    margin-top: 105px !important;
  }
  .ml_sm--105 {
    margin-left: 105px !important;
  }
  .mr_sm--105 {
    margin-right: 105px !important;
  }
  .mb_sm--105 {
    margin-bottom: 105px !important;
  }
  .ptb_sm--110 {
    padding: 110px 0 !important;
  }
  .plr_sm--110 {
    padding: 0 110px !important;
  }
  .pt_sm--110 {
    padding-top: 110px !important;
  }
  .pb_sm--110 {
    padding-bottom: 110px !important;
  }
  .pl_sm--110 {
    padding-left: 110px !important;
  }
  .pr_sm--110 {
    padding-right: 110px !important;
  }
  .mt_sm--110 {
    margin-top: 110px !important;
  }
  .ml_sm--110 {
    margin-left: 110px !important;
  }
  .mr_sm--110 {
    margin-right: 110px !important;
  }
  .mb_sm--110 {
    margin-bottom: 110px !important;
  }
  .ptb_sm--115 {
    padding: 115px 0 !important;
  }
  .plr_sm--115 {
    padding: 0 115px !important;
  }
  .pt_sm--115 {
    padding-top: 115px !important;
  }
  .pb_sm--115 {
    padding-bottom: 115px !important;
  }
  .pl_sm--115 {
    padding-left: 115px !important;
  }
  .pr_sm--115 {
    padding-right: 115px !important;
  }
  .mt_sm--115 {
    margin-top: 115px !important;
  }
  .ml_sm--115 {
    margin-left: 115px !important;
  }
  .mr_sm--115 {
    margin-right: 115px !important;
  }
  .mb_sm--115 {
    margin-bottom: 115px !important;
  }
  .ptb_sm--120 {
    padding: 120px 0 !important;
  }
  .plr_sm--120 {
    padding: 0 120px !important;
  }
  .pt_sm--120 {
    padding-top: 120px !important;
  }
  .pb_sm--120 {
    padding-bottom: 120px !important;
  }
  .pl_sm--120 {
    padding-left: 120px !important;
  }
  .pr_sm--120 {
    padding-right: 120px !important;
  }
  .mt_sm--120 {
    margin-top: 120px !important;
  }
  .ml_sm--120 {
    margin-left: 120px !important;
  }
  .mr_sm--120 {
    margin-right: 120px !important;
  }
  .mb_sm--120 {
    margin-bottom: 120px !important;
  }
  .ptb_sm--125 {
    padding: 125px 0 !important;
  }
  .plr_sm--125 {
    padding: 0 125px !important;
  }
  .pt_sm--125 {
    padding-top: 125px !important;
  }
  .pb_sm--125 {
    padding-bottom: 125px !important;
  }
  .pl_sm--125 {
    padding-left: 125px !important;
  }
  .pr_sm--125 {
    padding-right: 125px !important;
  }
  .mt_sm--125 {
    margin-top: 125px !important;
  }
  .ml_sm--125 {
    margin-left: 125px !important;
  }
  .mr_sm--125 {
    margin-right: 125px !important;
  }
  .mb_sm--125 {
    margin-bottom: 125px !important;
  }
  .ptb_sm--130 {
    padding: 130px 0 !important;
  }
  .plr_sm--130 {
    padding: 0 130px !important;
  }
  .pt_sm--130 {
    padding-top: 130px !important;
  }
  .pb_sm--130 {
    padding-bottom: 130px !important;
  }
  .pl_sm--130 {
    padding-left: 130px !important;
  }
  .pr_sm--130 {
    padding-right: 130px !important;
  }
  .mt_sm--130 {
    margin-top: 130px !important;
  }
  .ml_sm--130 {
    margin-left: 130px !important;
  }
  .mr_sm--130 {
    margin-right: 130px !important;
  }
  .mb_sm--130 {
    margin-bottom: 130px !important;
  }
  .ptb_sm--135 {
    padding: 135px 0 !important;
  }
  .plr_sm--135 {
    padding: 0 135px !important;
  }
  .pt_sm--135 {
    padding-top: 135px !important;
  }
  .pb_sm--135 {
    padding-bottom: 135px !important;
  }
  .pl_sm--135 {
    padding-left: 135px !important;
  }
  .pr_sm--135 {
    padding-right: 135px !important;
  }
  .mt_sm--135 {
    margin-top: 135px !important;
  }
  .ml_sm--135 {
    margin-left: 135px !important;
  }
  .mr_sm--135 {
    margin-right: 135px !important;
  }
  .mb_sm--135 {
    margin-bottom: 135px !important;
  }
  .ptb_sm--140 {
    padding: 140px 0 !important;
  }
  .plr_sm--140 {
    padding: 0 140px !important;
  }
  .pt_sm--140 {
    padding-top: 140px !important;
  }
  .pb_sm--140 {
    padding-bottom: 140px !important;
  }
  .pl_sm--140 {
    padding-left: 140px !important;
  }
  .pr_sm--140 {
    padding-right: 140px !important;
  }
  .mt_sm--140 {
    margin-top: 140px !important;
  }
  .ml_sm--140 {
    margin-left: 140px !important;
  }
  .mr_sm--140 {
    margin-right: 140px !important;
  }
  .mb_sm--140 {
    margin-bottom: 140px !important;
  }
  .ptb_sm--145 {
    padding: 145px 0 !important;
  }
  .plr_sm--145 {
    padding: 0 145px !important;
  }
  .pt_sm--145 {
    padding-top: 145px !important;
  }
  .pb_sm--145 {
    padding-bottom: 145px !important;
  }
  .pl_sm--145 {
    padding-left: 145px !important;
  }
  .pr_sm--145 {
    padding-right: 145px !important;
  }
  .mt_sm--145 {
    margin-top: 145px !important;
  }
  .ml_sm--145 {
    margin-left: 145px !important;
  }
  .mr_sm--145 {
    margin-right: 145px !important;
  }
  .mb_sm--145 {
    margin-bottom: 145px !important;
  }
  .ptb_sm--150 {
    padding: 150px 0 !important;
  }
  .plr_sm--150 {
    padding: 0 150px !important;
  }
  .pt_sm--150 {
    padding-top: 150px !important;
  }
  .pb_sm--150 {
    padding-bottom: 150px !important;
  }
  .pl_sm--150 {
    padding-left: 150px !important;
  }
  .pr_sm--150 {
    padding-right: 150px !important;
  }
  .mt_sm--150 {
    margin-top: 150px !important;
  }
  .ml_sm--150 {
    margin-left: 150px !important;
  }
  .mr_sm--150 {
    margin-right: 150px !important;
  }
  .mb_sm--150 {
    margin-bottom: 150px !important;
  }
  .ptb_sm--155 {
    padding: 155px 0 !important;
  }
  .plr_sm--155 {
    padding: 0 155px !important;
  }
  .pt_sm--155 {
    padding-top: 155px !important;
  }
  .pb_sm--155 {
    padding-bottom: 155px !important;
  }
  .pl_sm--155 {
    padding-left: 155px !important;
  }
  .pr_sm--155 {
    padding-right: 155px !important;
  }
  .mt_sm--155 {
    margin-top: 155px !important;
  }
  .ml_sm--155 {
    margin-left: 155px !important;
  }
  .mr_sm--155 {
    margin-right: 155px !important;
  }
  .mb_sm--155 {
    margin-bottom: 155px !important;
  }
  .ptb_sm--160 {
    padding: 160px 0 !important;
  }
  .plr_sm--160 {
    padding: 0 160px !important;
  }
  .pt_sm--160 {
    padding-top: 160px !important;
  }
  .pb_sm--160 {
    padding-bottom: 160px !important;
  }
  .pl_sm--160 {
    padding-left: 160px !important;
  }
  .pr_sm--160 {
    padding-right: 160px !important;
  }
  .mt_sm--160 {
    margin-top: 160px !important;
  }
  .ml_sm--160 {
    margin-left: 160px !important;
  }
  .mr_sm--160 {
    margin-right: 160px !important;
  }
  .mb_sm--160 {
    margin-bottom: 160px !important;
  }
  .ptb_sm--165 {
    padding: 165px 0 !important;
  }
  .plr_sm--165 {
    padding: 0 165px !important;
  }
  .pt_sm--165 {
    padding-top: 165px !important;
  }
  .pb_sm--165 {
    padding-bottom: 165px !important;
  }
  .pl_sm--165 {
    padding-left: 165px !important;
  }
  .pr_sm--165 {
    padding-right: 165px !important;
  }
  .mt_sm--165 {
    margin-top: 165px !important;
  }
  .ml_sm--165 {
    margin-left: 165px !important;
  }
  .mr_sm--165 {
    margin-right: 165px !important;
  }
  .mb_sm--165 {
    margin-bottom: 165px !important;
  }
  .ptb_sm--170 {
    padding: 170px 0 !important;
  }
  .plr_sm--170 {
    padding: 0 170px !important;
  }
  .pt_sm--170 {
    padding-top: 170px !important;
  }
  .pb_sm--170 {
    padding-bottom: 170px !important;
  }
  .pl_sm--170 {
    padding-left: 170px !important;
  }
  .pr_sm--170 {
    padding-right: 170px !important;
  }
  .mt_sm--170 {
    margin-top: 170px !important;
  }
  .ml_sm--170 {
    margin-left: 170px !important;
  }
  .mr_sm--170 {
    margin-right: 170px !important;
  }
  .mb_sm--170 {
    margin-bottom: 170px !important;
  }
  .ptb_sm--175 {
    padding: 175px 0 !important;
  }
  .plr_sm--175 {
    padding: 0 175px !important;
  }
  .pt_sm--175 {
    padding-top: 175px !important;
  }
  .pb_sm--175 {
    padding-bottom: 175px !important;
  }
  .pl_sm--175 {
    padding-left: 175px !important;
  }
  .pr_sm--175 {
    padding-right: 175px !important;
  }
  .mt_sm--175 {
    margin-top: 175px !important;
  }
  .ml_sm--175 {
    margin-left: 175px !important;
  }
  .mr_sm--175 {
    margin-right: 175px !important;
  }
  .mb_sm--175 {
    margin-bottom: 175px !important;
  }
  .ptb_sm--180 {
    padding: 180px 0 !important;
  }
  .plr_sm--180 {
    padding: 0 180px !important;
  }
  .pt_sm--180 {
    padding-top: 180px !important;
  }
  .pb_sm--180 {
    padding-bottom: 180px !important;
  }
  .pl_sm--180 {
    padding-left: 180px !important;
  }
  .pr_sm--180 {
    padding-right: 180px !important;
  }
  .mt_sm--180 {
    margin-top: 180px !important;
  }
  .ml_sm--180 {
    margin-left: 180px !important;
  }
  .mr_sm--180 {
    margin-right: 180px !important;
  }
  .mb_sm--180 {
    margin-bottom: 180px !important;
  }
  .ptb_sm--185 {
    padding: 185px 0 !important;
  }
  .plr_sm--185 {
    padding: 0 185px !important;
  }
  .pt_sm--185 {
    padding-top: 185px !important;
  }
  .pb_sm--185 {
    padding-bottom: 185px !important;
  }
  .pl_sm--185 {
    padding-left: 185px !important;
  }
  .pr_sm--185 {
    padding-right: 185px !important;
  }
  .mt_sm--185 {
    margin-top: 185px !important;
  }
  .ml_sm--185 {
    margin-left: 185px !important;
  }
  .mr_sm--185 {
    margin-right: 185px !important;
  }
  .mb_sm--185 {
    margin-bottom: 185px !important;
  }
  .ptb_sm--190 {
    padding: 190px 0 !important;
  }
  .plr_sm--190 {
    padding: 0 190px !important;
  }
  .pt_sm--190 {
    padding-top: 190px !important;
  }
  .pb_sm--190 {
    padding-bottom: 190px !important;
  }
  .pl_sm--190 {
    padding-left: 190px !important;
  }
  .pr_sm--190 {
    padding-right: 190px !important;
  }
  .mt_sm--190 {
    margin-top: 190px !important;
  }
  .ml_sm--190 {
    margin-left: 190px !important;
  }
  .mr_sm--190 {
    margin-right: 190px !important;
  }
  .mb_sm--190 {
    margin-bottom: 190px !important;
  }
  .ptb_sm--195 {
    padding: 195px 0 !important;
  }
  .plr_sm--195 {
    padding: 0 195px !important;
  }
  .pt_sm--195 {
    padding-top: 195px !important;
  }
  .pb_sm--195 {
    padding-bottom: 195px !important;
  }
  .pl_sm--195 {
    padding-left: 195px !important;
  }
  .pr_sm--195 {
    padding-right: 195px !important;
  }
  .mt_sm--195 {
    margin-top: 195px !important;
  }
  .ml_sm--195 {
    margin-left: 195px !important;
  }
  .mr_sm--195 {
    margin-right: 195px !important;
  }
  .mb_sm--195 {
    margin-bottom: 195px !important;
  }
  .ptb_sm--200 {
    padding: 200px 0 !important;
  }
  .plr_sm--200 {
    padding: 0 200px !important;
  }
  .pt_sm--200 {
    padding-top: 200px !important;
  }
  .pb_sm--200 {
    padding-bottom: 200px !important;
  }
  .pl_sm--200 {
    padding-left: 200px !important;
  }
  .pr_sm--200 {
    padding-right: 200px !important;
  }
  .mt_sm--200 {
    margin-top: 200px !important;
  }
  .ml_sm--200 {
    margin-left: 200px !important;
  }
  .mr_sm--200 {
    margin-right: 200px !important;
  }
  .mb_sm--200 {
    margin-bottom: 200px !important;
  }
  .pl_sm--0 {
    padding-left: 0;
  }
  .pr_sm--0 {
    padding-right: 0;
  }
  .pt_sm--0 {
    padding-top: 0;
  }
  .pb_sm--0 {
    padding-bottom: 0;
  }
  .mr_sm--0 {
    margin-right: 0;
  }
  .ml_sm--0 {
    margin-left: 0;
  }
  .mt_sm--0 {
    margin-top: 0;
  }
  .mb_sm--0 {
    margin-bottom: 0;
  }
}
@media only screen and (max-width: 575px) {
  .ptb_mobile--5 {
    padding: 5px 0 !important;
  }
  .plr_mobile--5 {
    padding: 0 5px !important;
  }
  .pt_mobile--5 {
    padding-top: 5px !important;
  }
  .pb_mobile--5 {
    padding-bottom: 5px !important;
  }
  .pl_mobile--5 {
    padding-left: 5px !important;
  }
  .pr_mobile--5 {
    padding-right: 5px !important;
  }
  .mt_mobile--5 {
    margin-top: 5px !important;
  }
  .mb_mobile--5 {
    margin-bottom: 5px !important;
  }
  .ptb_mobile--10 {
    padding: 10px 0 !important;
  }
  .plr_mobile--10 {
    padding: 0 10px !important;
  }
  .pt_mobile--10 {
    padding-top: 10px !important;
  }
  .pb_mobile--10 {
    padding-bottom: 10px !important;
  }
  .pl_mobile--10 {
    padding-left: 10px !important;
  }
  .pr_mobile--10 {
    padding-right: 10px !important;
  }
  .mt_mobile--10 {
    margin-top: 10px !important;
  }
  .mb_mobile--10 {
    margin-bottom: 10px !important;
  }
  .ptb_mobile--15 {
    padding: 15px 0 !important;
  }
  .plr_mobile--15 {
    padding: 0 15px !important;
  }
  .pt_mobile--15 {
    padding-top: 15px !important;
  }
  .pb_mobile--15 {
    padding-bottom: 15px !important;
  }
  .pl_mobile--15 {
    padding-left: 15px !important;
  }
  .pr_mobile--15 {
    padding-right: 15px !important;
  }
  .mt_mobile--15 {
    margin-top: 15px !important;
  }
  .mb_mobile--15 {
    margin-bottom: 15px !important;
  }
  .ptb_mobile--20 {
    padding: 20px 0 !important;
  }
  .plr_mobile--20 {
    padding: 0 20px !important;
  }
  .pt_mobile--20 {
    padding-top: 20px !important;
  }
  .pb_mobile--20 {
    padding-bottom: 20px !important;
  }
  .pl_mobile--20 {
    padding-left: 20px !important;
  }
  .pr_mobile--20 {
    padding-right: 20px !important;
  }
  .mt_mobile--20 {
    margin-top: 20px !important;
  }
  .mb_mobile--20 {
    margin-bottom: 20px !important;
  }
  .ptb_mobile--25 {
    padding: 25px 0 !important;
  }
  .plr_mobile--25 {
    padding: 0 25px !important;
  }
  .pt_mobile--25 {
    padding-top: 25px !important;
  }
  .pb_mobile--25 {
    padding-bottom: 25px !important;
  }
  .pl_mobile--25 {
    padding-left: 25px !important;
  }
  .pr_mobile--25 {
    padding-right: 25px !important;
  }
  .mt_mobile--25 {
    margin-top: 25px !important;
  }
  .mb_mobile--25 {
    margin-bottom: 25px !important;
  }
  .ptb_mobile--30 {
    padding: 30px 0 !important;
  }
  .plr_mobile--30 {
    padding: 0 30px !important;
  }
  .pt_mobile--30 {
    padding-top: 30px !important;
  }
  .pb_mobile--30 {
    padding-bottom: 30px !important;
  }
  .pl_mobile--30 {
    padding-left: 30px !important;
  }
  .pr_mobile--30 {
    padding-right: 30px !important;
  }
  .mt_mobile--30 {
    margin-top: 30px !important;
  }
  .mb_mobile--30 {
    margin-bottom: 30px !important;
  }
  .ptb_mobile--35 {
    padding: 35px 0 !important;
  }
  .plr_mobile--35 {
    padding: 0 35px !important;
  }
  .pt_mobile--35 {
    padding-top: 35px !important;
  }
  .pb_mobile--35 {
    padding-bottom: 35px !important;
  }
  .pl_mobile--35 {
    padding-left: 35px !important;
  }
  .pr_mobile--35 {
    padding-right: 35px !important;
  }
  .mt_mobile--35 {
    margin-top: 35px !important;
  }
  .mb_mobile--35 {
    margin-bottom: 35px !important;
  }
  .ptb_mobile--40 {
    padding: 40px 0 !important;
  }
  .plr_mobile--40 {
    padding: 0 40px !important;
  }
  .pt_mobile--40 {
    padding-top: 40px !important;
  }
  .pb_mobile--40 {
    padding-bottom: 40px !important;
  }
  .pl_mobile--40 {
    padding-left: 40px !important;
  }
  .pr_mobile--40 {
    padding-right: 40px !important;
  }
  .mt_mobile--40 {
    margin-top: 40px !important;
  }
  .mb_mobile--40 {
    margin-bottom: 40px !important;
  }
  .ptb_mobile--45 {
    padding: 45px 0 !important;
  }
  .plr_mobile--45 {
    padding: 0 45px !important;
  }
  .pt_mobile--45 {
    padding-top: 45px !important;
  }
  .pb_mobile--45 {
    padding-bottom: 45px !important;
  }
  .pl_mobile--45 {
    padding-left: 45px !important;
  }
  .pr_mobile--45 {
    padding-right: 45px !important;
  }
  .mt_mobile--45 {
    margin-top: 45px !important;
  }
  .mb_mobile--45 {
    margin-bottom: 45px !important;
  }
  .ptb_mobile--50 {
    padding: 50px 0 !important;
  }
  .plr_mobile--50 {
    padding: 0 50px !important;
  }
  .pt_mobile--50 {
    padding-top: 50px !important;
  }
  .pb_mobile--50 {
    padding-bottom: 50px !important;
  }
  .pl_mobile--50 {
    padding-left: 50px !important;
  }
  .pr_mobile--50 {
    padding-right: 50px !important;
  }
  .mt_mobile--50 {
    margin-top: 50px !important;
  }
  .mb_mobile--50 {
    margin-bottom: 50px !important;
  }
  .ptb_mobile--55 {
    padding: 55px 0 !important;
  }
  .plr_mobile--55 {
    padding: 0 55px !important;
  }
  .pt_mobile--55 {
    padding-top: 55px !important;
  }
  .pb_mobile--55 {
    padding-bottom: 55px !important;
  }
  .pl_mobile--55 {
    padding-left: 55px !important;
  }
  .pr_mobile--55 {
    padding-right: 55px !important;
  }
  .mt_mobile--55 {
    margin-top: 55px !important;
  }
  .mb_mobile--55 {
    margin-bottom: 55px !important;
  }
  .ptb_mobile--60 {
    padding: 60px 0 !important;
  }
  .plr_mobile--60 {
    padding: 0 60px !important;
  }
  .pt_mobile--60 {
    padding-top: 60px !important;
  }
  .pb_mobile--60 {
    padding-bottom: 60px !important;
  }
  .pl_mobile--60 {
    padding-left: 60px !important;
  }
  .pr_mobile--60 {
    padding-right: 60px !important;
  }
  .mt_mobile--60 {
    margin-top: 60px !important;
  }
  .mb_mobile--60 {
    margin-bottom: 60px !important;
  }
  .ptb_mobile--65 {
    padding: 65px 0 !important;
  }
  .plr_mobile--65 {
    padding: 0 65px !important;
  }
  .pt_mobile--65 {
    padding-top: 65px !important;
  }
  .pb_mobile--65 {
    padding-bottom: 65px !important;
  }
  .pl_mobile--65 {
    padding-left: 65px !important;
  }
  .pr_mobile--65 {
    padding-right: 65px !important;
  }
  .mt_mobile--65 {
    margin-top: 65px !important;
  }
  .mb_mobile--65 {
    margin-bottom: 65px !important;
  }
  .ptb_mobile--70 {
    padding: 70px 0 !important;
  }
  .plr_mobile--70 {
    padding: 0 70px !important;
  }
  .pt_mobile--70 {
    padding-top: 70px !important;
  }
  .pb_mobile--70 {
    padding-bottom: 70px !important;
  }
  .pl_mobile--70 {
    padding-left: 70px !important;
  }
  .pr_mobile--70 {
    padding-right: 70px !important;
  }
  .mt_mobile--70 {
    margin-top: 70px !important;
  }
  .mb_mobile--70 {
    margin-bottom: 70px !important;
  }
  .ptb_mobile--75 {
    padding: 75px 0 !important;
  }
  .plr_mobile--75 {
    padding: 0 75px !important;
  }
  .pt_mobile--75 {
    padding-top: 75px !important;
  }
  .pb_mobile--75 {
    padding-bottom: 75px !important;
  }
  .pl_mobile--75 {
    padding-left: 75px !important;
  }
  .pr_mobile--75 {
    padding-right: 75px !important;
  }
  .mt_mobile--75 {
    margin-top: 75px !important;
  }
  .mb_mobile--75 {
    margin-bottom: 75px !important;
  }
  .ptb_mobile--80 {
    padding: 80px 0 !important;
  }
  .plr_mobile--80 {
    padding: 0 80px !important;
  }
  .pt_mobile--80 {
    padding-top: 80px !important;
  }
  .pb_mobile--80 {
    padding-bottom: 80px !important;
  }
  .pl_mobile--80 {
    padding-left: 80px !important;
  }
  .pr_mobile--80 {
    padding-right: 80px !important;
  }
  .mt_mobile--80 {
    margin-top: 80px !important;
  }
  .mb_mobile--80 {
    margin-bottom: 80px !important;
  }
  .ptb_mobile--85 {
    padding: 85px 0 !important;
  }
  .plr_mobile--85 {
    padding: 0 85px !important;
  }
  .pt_mobile--85 {
    padding-top: 85px !important;
  }
  .pb_mobile--85 {
    padding-bottom: 85px !important;
  }
  .pl_mobile--85 {
    padding-left: 85px !important;
  }
  .pr_mobile--85 {
    padding-right: 85px !important;
  }
  .mt_mobile--85 {
    margin-top: 85px !important;
  }
  .mb_mobile--85 {
    margin-bottom: 85px !important;
  }
  .ptb_mobile--90 {
    padding: 90px 0 !important;
  }
  .plr_mobile--90 {
    padding: 0 90px !important;
  }
  .pt_mobile--90 {
    padding-top: 90px !important;
  }
  .pb_mobile--90 {
    padding-bottom: 90px !important;
  }
  .pl_mobile--90 {
    padding-left: 90px !important;
  }
  .pr_mobile--90 {
    padding-right: 90px !important;
  }
  .mt_mobile--90 {
    margin-top: 90px !important;
  }
  .mb_mobile--90 {
    margin-bottom: 90px !important;
  }
  .ptb_mobile--95 {
    padding: 95px 0 !important;
  }
  .plr_mobile--95 {
    padding: 0 95px !important;
  }
  .pt_mobile--95 {
    padding-top: 95px !important;
  }
  .pb_mobile--95 {
    padding-bottom: 95px !important;
  }
  .pl_mobile--95 {
    padding-left: 95px !important;
  }
  .pr_mobile--95 {
    padding-right: 95px !important;
  }
  .mt_mobile--95 {
    margin-top: 95px !important;
  }
  .mb_mobile--95 {
    margin-bottom: 95px !important;
  }
  .ptb_mobile--100 {
    padding: 100px 0 !important;
  }
  .plr_mobile--100 {
    padding: 0 100px !important;
  }
  .pt_mobile--100 {
    padding-top: 100px !important;
  }
  .pb_mobile--100 {
    padding-bottom: 100px !important;
  }
  .pl_mobile--100 {
    padding-left: 100px !important;
  }
  .pr_mobile--100 {
    padding-right: 100px !important;
  }
  .mt_mobile--100 {
    margin-top: 100px !important;
  }
  .mb_mobile--100 {
    margin-bottom: 100px !important;
  }
}
.slick-gutter-5 {
  margin-left: -5px;
  margin-right: -5px;
}
.slick-gutter-5 .slick-slide {
  padding-left: 5px;
  padding-right: 5px;
}

.slick-gutter-10 {
  margin-left: -10px;
  margin-right: -10px;
}
.slick-gutter-10 .slick-slide {
  padding-left: 10px;
  padding-right: 10px;
}

.slick-gutter-15 {
  margin-left: -15px;
  margin-right: -15px;
}
.slick-gutter-15 .slick-slide {
  padding-left: 15px;
  padding-right: 15px;
}

.slick-gutter-20 {
  margin-left: -20px;
  margin-right: -20px;
}
.slick-gutter-20 .slick-slide {
  padding-left: 20px;
  padding-right: 20px;
}

.slick-gutter-25 {
  margin-left: -25px;
  margin-right: -25px;
}
.slick-gutter-25 .slick-slide {
  padding-left: 25px;
  padding-right: 25px;
}

.slick-gutter-30 {
  margin-left: -30px;
  margin-right: -30px;
}
.slick-gutter-30 .slick-slide {
  padding-left: 30px;
  padding-right: 30px;
}

.slick-gutter-35 {
  margin-left: -35px;
  margin-right: -35px;
}
.slick-gutter-35 .slick-slide {
  padding-left: 35px;
  padding-right: 35px;
}

.slick-gutter-40 {
  margin-left: -40px;
  margin-right: -40px;
}
.slick-gutter-40 .slick-slide {
  padding-left: 40px;
  padding-right: 40px;
}

.slick-gutter-45 {
  margin-left: -45px;
  margin-right: -45px;
}
.slick-gutter-45 .slick-slide {
  padding-left: 45px;
  padding-right: 45px;
}

.slick-gutter-50 {
  margin-left: -50px;
  margin-right: -50px;
}
.slick-gutter-50 .slick-slide {
  padding-left: 50px;
  padding-right: 50px;
}

.slick-gutter-55 {
  margin-left: -55px;
  margin-right: -55px;
}
.slick-gutter-55 .slick-slide {
  padding-left: 55px;
  padding-right: 55px;
}

.slick-gutter-60 {
  margin-left: -60px;
  margin-right: -60px;
}
.slick-gutter-60 .slick-slide {
  padding-left: 60px;
  padding-right: 60px;
}

.slick-gutter-65 {
  margin-left: -65px;
  margin-right: -65px;
}
.slick-gutter-65 .slick-slide {
  padding-left: 65px;
  padding-right: 65px;
}

.slick-gutter-70 {
  margin-left: -70px;
  margin-right: -70px;
}
.slick-gutter-70 .slick-slide {
  padding-left: 70px;
  padding-right: 70px;
}

.slick-gutter-75 {
  margin-left: -75px;
  margin-right: -75px;
}
.slick-gutter-75 .slick-slide {
  padding-left: 75px;
  padding-right: 75px;
}

.slick-gutter-80 {
  margin-left: -80px;
  margin-right: -80px;
}
.slick-gutter-80 .slick-slide {
  padding-left: 80px;
  padding-right: 80px;
}

.slick-gutter-85 {
  margin-left: -85px;
  margin-right: -85px;
}
.slick-gutter-85 .slick-slide {
  padding-left: 85px;
  padding-right: 85px;
}

.slick-gutter-90 {
  margin-left: -90px;
  margin-right: -90px;
}
.slick-gutter-90 .slick-slide {
  padding-left: 90px;
  padding-right: 90px;
}

.slick-gutter-95 {
  margin-left: -95px;
  margin-right: -95px;
}
.slick-gutter-95 .slick-slide {
  padding-left: 95px;
  padding-right: 95px;
}

.slick-gutter-100 {
  margin-left: -100px;
  margin-right: -100px;
}
.slick-gutter-100 .slick-slide {
  padding-left: 100px;
  padding-right: 100px;
}

.mt-dec-30 {
  margin-top: -30px !important;
}

.mt_dec--30 {
  margin-top: -30px !important;
}

.mt-dec-100 {
  margin-top: -100px !important;
}

@media only screen and (max-width: 479px) {
  .small-margin-pricing {
    margin-bottom: 25px !important;
  }
}

@media only screen and (max-width: 479px) {
  .contact-input {
    margin-bottom: 35px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .mb_dec--35 {
    margin-bottom: -50px;
  }
}

@media only screen and (max-width: 767px) {
  .mb_dec--35 {
    margin-bottom: -75px;
  }
}
@media only screen and (max-width: 575px) {
  .mb_dec--35 {
    margin-bottom: 0;
  }
}

@media only screen and (max-width: 575px) {
  .mt-contact-sm {
    margin-top: 30px !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .testimonial-pb {
    padding-bottom: 35px;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .contact-input {
    padding-bottom: 30px;
  }
}
@media only screen and (max-width: 767px) {
  .contact-input {
    padding-bottom: 30px;
  }
}

.pb_xl--130 {
  padding-bottom: 130px;
}
@media only screen and (max-width: 1199px) {
  .pb_xl--130 {
    padding-bottom: 110px;
  }
}

@media only screen and (max-width: 1199px) {
  .mt_experience {
    margin-top: -10px;
  }
}

.mt_dec--120 {
  margin-top: -60px;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .plr_md--0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}

.padding-contorler-am-slide {
  padding-left: 246px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .padding-contorler-am-slide {
    padding-left: 100px;
  }
}
@media only screen and (max-width: 1199px) {
  .padding-contorler-am-slide {
    padding-left: 100px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .padding-contorler-am-slide {
    padding-left: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .padding-contorler-am-slide {
    padding-left: 15px;
  }
}

.padding-contorler-am-slide-11 {
  padding-left: 246px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .padding-contorler-am-slide-11 {
    padding-left: 100px;
  }
}
@media only screen and (max-width: 1199px) {
  .padding-contorler-am-slide-11 {
    padding-left: 0;
  }
}

.padding-contorler-am-slide-right {
  padding-right: 200px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .padding-contorler-am-slide-right {
    padding-right: 30px;
  }
}
@media only screen and (max-width: 1199px) {
  .padding-contorler-am-slide-right {
    padding-right: 30px;
    padding-top: 50px;
  }
}
@media only screen and (max-width: 767px) {
  .padding-contorler-am-slide-right {
    padding-right: 30px;
    padding-top: 50px;
  }
}

.g-24 {
  --bs-gutter-x: 24px;
  --bs-gutter-y: 24px;
}

.g-40 {
  --bs-gutter-x: 40px;
  --bs-gutter-y: 40px;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
nav,
section,
summary {
  display: block;
}

audio,
canvas,
video {
  display: inline-block;
}

audio:not([controls]) {
  display: none;
  height: 0;
}

[hidden] {
  display: none;
}

a {
  color: var(--color-heading);
  text-decoration: none;
  outline: none;
}

a:hover,
a:focus,
a:active {
  text-decoration: none;
  outline: none;
  color: var(--color-primary);
}

a:focus {
  outline: none;
}

address {
  margin: 0 0 24px;
}

abbr[title] {
  border-bottom: 1px dotted;
}

b,
strong {
  font-weight: bold;
}

mark {
  background: var(--color-primary);
  color: #ffffff;
}

code,
kbd,
pre,
samp {
  font-size: var(--font-size-b3);
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  color: var(--color-primary);
}

kbd,
ins {
  color: #ffffff;
}

pre {
  font-family: "Raleway", sans-serif;
  font-size: var(--font-size-b3);
  margin: 10px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: var(--color-body);
  background: var(--color-lighter);
}

small {
  font-size: smaller;
}

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

dl {
  margin-top: 0;
  margin-bottom: 10px;
}

dd {
  margin: 0 15px 15px;
}

dt {
  font-weight: bold;
  color: var(--color-heading);
}

menu,
ol,
ul {
  margin: 16px 0;
  padding: 0 0 0 40px;
}

nav ul,
nav ol {
  list-style: none;
  list-style-image: none;
}

li > ul,
li > ol {
  margin: 0;
}

ol ul {
  margin-bottom: 0;
}

img {
  -ms-interpolation-mode: bicubic;
  border: 0;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
}

svg:not(:root) {
  overflow: hidden;
}

figure {
  margin: 0;
}

form {
  margin: 0;
}

fieldset {
  border: 1px solid var(--color-border);
  margin: 0 2px;
  min-width: inherit;
  padding: 0.35em 0.625em 0.75em;
}

legend {
  border: 0;
  padding: 0;
  white-space: normal;
}

button,
input,
select,
textarea {
  font-size: 100%;
  margin: 0;
  max-width: 100%;
  vertical-align: baseline;
}

button,
input {
  line-height: normal;
}

button,
html input[type=button],
input[type=reset],
input[type=submit] {
  -webkit-appearance: button;
  -moz-appearance: button;
  appearance: button;
  cursor: pointer;
}

button[disabled],
input[disabled] {
  cursor: default;
}

input[type=checkbox],
input[type=radio] {
  padding: 0;
}

input[type=search] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
  appearance: textfield;
  padding-right: 2px;
  width: 270px;
}

input[type=search]::-webkit-search-decoration {
  -webkit-appearance: none;
  appearance: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}

textarea {
  overflow: auto;
  vertical-align: top;
}

caption,
th,
td {
  font-weight: normal;
}

th {
  font-weight: 500;
  text-transform: uppercase;
}

td,
.wp-block-calendar tfoot td {
  border: 1px solid var(--color-border);
  padding: 7px 10px;
}

del {
  color: #333;
}

ins {
  background: rgba(255, 47, 47, 0.4);
  text-decoration: none;
}

hr {
  background-size: 4px 4px;
  border: 0;
  height: 1px;
  margin: 0 0 24px;
}

table a,
table a:link,
table a:visited {
  text-decoration: underline;
}

dt {
  font-weight: bold;
  margin-bottom: 10px;
}

dd {
  margin: 0 15px 15px;
}

caption {
  caption-side: top;
}

kbd {
  background: var(--heading-color);
}

dfn,
cite,
em {
  font-style: italic;
}

/* BlockQuote  */
blockquote,
q {
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

blockquote {
  font-size: var(--font-size-b1);
  font-style: italic;
  font-weight: var(--p-light);
  margin: 24px 40px;
}

blockquote blockquote {
  margin-right: 0;
}

blockquote cite,
blockquote small {
  font-size: var(--font-size-b3);
  font-weight: normal;
}

blockquote strong,
blockquote b {
  font-weight: 700;
}

/* ========= Forms Styles ========= */
input,
button,
select,
textarea {
  background: transparent;
  border: 1px solid var(--color-border);
  transition: all 0.4s ease-out 0s;
  color: var(--color-body);
  width: 100%;
}
input:focus, input:active,
button:focus,
button:active,
select:focus,
select:active,
textarea:focus,
textarea:active {
  outline: none;
  border-color: var(--color-primary);
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}

input {
  height: 40px;
  padding: 0 15px;
}

input[type=text],
input[type=password],
input[type=email],
input[type=number],
input[type=tel],
textarea {
  font-size: var(--font-size-b2);
  font-weight: 400;
  height: auto;
  line-height: 28px;
  background: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
  padding: 0 15px;
  outline: none;
  border: var(--border-width) solid var(--color-border);
  border-radius: var(--radius);
  /* -- Placeholder -- */
}
input[type=text]::placeholder,
input[type=password]::placeholder,
input[type=email]::placeholder,
input[type=number]::placeholder,
input[type=tel]::placeholder,
textarea::placeholder {
  color: var(--body-color);
  /* Firefox */
  opacity: 1;
}
input[type=text]:-ms-input-placeholder,
input[type=password]:-ms-input-placeholder,
input[type=email]:-ms-input-placeholder,
input[type=number]:-ms-input-placeholder,
input[type=tel]:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: var(--body-color);
}
input[type=text]::-ms-input-placeholder,
input[type=password]::-ms-input-placeholder,
input[type=email]::-ms-input-placeholder,
input[type=number]::-ms-input-placeholder,
input[type=tel]::-ms-input-placeholder,
textarea::-ms-input-placeholder {
  /* Microsoft Edge */
  color: var(--body-color);
}
input[type=text].p-holder__active, .input-active input[type=text], input[type=text].input-active,
input[type=password].p-holder__active,
.input-active input[type=password],
input[type=password].input-active,
input[type=email].p-holder__active,
.input-active input[type=email],
input[type=email].input-active,
input[type=number].p-holder__active,
.input-active input[type=number],
input[type=number].input-active,
input[type=tel].p-holder__active,
.input-active input[type=tel],
input[type=tel].input-active,
textarea.p-holder__active,
textarea.input-active {
  border-color: var(--color-primary);
  /* -- Placeholder -- */
}
input[type=text].p-holder__active::placeholder, .input-active input[type=text]::placeholder, input[type=text].input-active::placeholder,
input[type=password].p-holder__active::placeholder,
.input-active input[type=password]::placeholder,
input[type=password].input-active::placeholder,
input[type=email].p-holder__active::placeholder,
.input-active input[type=email]::placeholder,
input[type=email].input-active::placeholder,
input[type=number].p-holder__active::placeholder,
.input-active input[type=number]::placeholder,
input[type=number].input-active::placeholder,
input[type=tel].p-holder__active::placeholder,
.input-active input[type=tel]::placeholder,
input[type=tel].input-active::placeholder,
textarea.p-holder__active::placeholder,
textarea.input-active::placeholder {
  color: var(--color-primary);
  /* Firefox */
  opacity: 1;
}
input[type=text].p-holder__active:-ms-input-placeholder, .input-active input[type=text]:-ms-input-placeholder, input[type=text].input-active:-ms-input-placeholder,
input[type=password].p-holder__active:-ms-input-placeholder,
.input-active input[type=password]:-ms-input-placeholder,
input[type=password].input-active:-ms-input-placeholder,
input[type=email].p-holder__active:-ms-input-placeholder,
.input-active input[type=email]:-ms-input-placeholder,
input[type=email].input-active:-ms-input-placeholder,
input[type=number].p-holder__active:-ms-input-placeholder,
.input-active input[type=number]:-ms-input-placeholder,
input[type=number].input-active:-ms-input-placeholder,
input[type=tel].p-holder__active:-ms-input-placeholder,
.input-active input[type=tel]:-ms-input-placeholder,
input[type=tel].input-active:-ms-input-placeholder,
textarea.p-holder__active:-ms-input-placeholder,
textarea.input-active:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: var(--color-primary);
}
input[type=text].p-holder__active::-ms-input-placeholder, .input-active input[type=text]::-ms-input-placeholder, input[type=text].input-active::-ms-input-placeholder,
input[type=password].p-holder__active::-ms-input-placeholder,
.input-active input[type=password]::-ms-input-placeholder,
input[type=password].input-active::-ms-input-placeholder,
input[type=email].p-holder__active::-ms-input-placeholder,
.input-active input[type=email]::-ms-input-placeholder,
input[type=email].input-active::-ms-input-placeholder,
input[type=number].p-holder__active::-ms-input-placeholder,
.input-active input[type=number]::-ms-input-placeholder,
input[type=number].input-active::-ms-input-placeholder,
input[type=tel].p-holder__active::-ms-input-placeholder,
.input-active input[type=tel]::-ms-input-placeholder,
input[type=tel].input-active::-ms-input-placeholder,
textarea.p-holder__active::-ms-input-placeholder,
textarea.input-active::-ms-input-placeholder {
  /* Microsoft Edge */
  color: var(--color-primary);
}
input[type=text].p-holder__error, .input-error input[type=text], input[type=text].input-error,
input[type=password].p-holder__error,
.input-error input[type=password],
input[type=password].input-error,
input[type=email].p-holder__error,
.input-error input[type=email],
input[type=email].input-error,
input[type=number].p-holder__error,
.input-error input[type=number],
input[type=number].input-error,
input[type=tel].p-holder__error,
.input-error input[type=tel],
input[type=tel].input-error,
textarea.p-holder__error,
textarea.input-error {
  border-color: #f4282d;
  /* -- Placeholder -- */
}
input[type=text].p-holder__error::placeholder, .input-error input[type=text]::placeholder, input[type=text].input-error::placeholder,
input[type=password].p-holder__error::placeholder,
.input-error input[type=password]::placeholder,
input[type=password].input-error::placeholder,
input[type=email].p-holder__error::placeholder,
.input-error input[type=email]::placeholder,
input[type=email].input-error::placeholder,
input[type=number].p-holder__error::placeholder,
.input-error input[type=number]::placeholder,
input[type=number].input-error::placeholder,
input[type=tel].p-holder__error::placeholder,
.input-error input[type=tel]::placeholder,
input[type=tel].input-error::placeholder,
textarea.p-holder__error::placeholder,
textarea.input-error::placeholder {
  color: #f4282d;
  /* Firefox */
  opacity: 1;
}
input[type=text].p-holder__error:-ms-input-placeholder, .input-error input[type=text]:-ms-input-placeholder, input[type=text].input-error:-ms-input-placeholder,
input[type=password].p-holder__error:-ms-input-placeholder,
.input-error input[type=password]:-ms-input-placeholder,
input[type=password].input-error:-ms-input-placeholder,
input[type=email].p-holder__error:-ms-input-placeholder,
.input-error input[type=email]:-ms-input-placeholder,
input[type=email].input-error:-ms-input-placeholder,
input[type=number].p-holder__error:-ms-input-placeholder,
.input-error input[type=number]:-ms-input-placeholder,
input[type=number].input-error:-ms-input-placeholder,
input[type=tel].p-holder__error:-ms-input-placeholder,
.input-error input[type=tel]:-ms-input-placeholder,
input[type=tel].input-error:-ms-input-placeholder,
textarea.p-holder__error:-ms-input-placeholder,
textarea.input-error:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  color: #f4282d;
}
input[type=text].p-holder__error::-ms-input-placeholder, .input-error input[type=text]::-ms-input-placeholder, input[type=text].input-error::-ms-input-placeholder,
input[type=password].p-holder__error::-ms-input-placeholder,
.input-error input[type=password]::-ms-input-placeholder,
input[type=password].input-error::-ms-input-placeholder,
input[type=email].p-holder__error::-ms-input-placeholder,
.input-error input[type=email]::-ms-input-placeholder,
input[type=email].input-error::-ms-input-placeholder,
input[type=number].p-holder__error::-ms-input-placeholder,
.input-error input[type=number]::-ms-input-placeholder,
input[type=number].input-error::-ms-input-placeholder,
input[type=tel].p-holder__error::-ms-input-placeholder,
.input-error input[type=tel]::-ms-input-placeholder,
input[type=tel].input-error::-ms-input-placeholder,
textarea.p-holder__error::-ms-input-placeholder,
textarea.input-error::-ms-input-placeholder {
  /* Microsoft Edge */
  color: #f4282d;
}
input[type=text].p-holder__error:focus, .input-error input[type=text]:focus, input[type=text].input-error:focus,
input[type=password].p-holder__error:focus,
.input-error input[type=password]:focus,
input[type=password].input-error:focus,
input[type=email].p-holder__error:focus,
.input-error input[type=email]:focus,
input[type=email].input-error:focus,
input[type=number].p-holder__error:focus,
.input-error input[type=number]:focus,
input[type=number].input-error:focus,
input[type=tel].p-holder__error:focus,
.input-error input[type=tel]:focus,
input[type=tel].input-error:focus,
textarea.p-holder__error:focus,
textarea.input-error:focus {
  border-color: #f4282d;
}
input[type=text]:focus,
input[type=password]:focus,
input[type=email]:focus,
input[type=number]:focus,
input[type=tel]:focus,
textarea:focus {
  border-color: var(--color-primary);
}

input[type=checkbox],
input[type=radio] {
  opacity: 1;
  position: relative;
  height: auto !important;
  max-width: 18px;
  width: max-content;
}
input[type=checkbox] ~ label,
input[type=radio] ~ label {
  position: relative;
  font-size: 14px;
  line-height: 17px;
  color: #2C3C28;
  font-weight: 500;
  padding-left: 25px;
  cursor: pointer;
}
input[type=checkbox] ~ label::before,
input[type=radio] ~ label::before {
  content: " ";
  position: absolute;
  top: 1 px;
  left: 0;
  width: 15px;
  height: 15px;
  background-color: #5d5d7e;
  border-radius: 2px;
  transition: all 0.3s;
  border-radius: 2px;
}
input[type=checkbox] ~ label::after,
input[type=radio] ~ label::after {
  content: " ";
  position: absolute;
  top: 16%;
  left: 2px;
  width: 10px;
  height: 6px;
  background-color: transparent;
  border-bottom: 2px solid #b8b8b8;
  border-left: 2px solid #b8b8b8;
  border-radius: 2px;
  transform: rotate(-45deg);
  opacity: 0;
  transition: all 0.3s;
}
input[type=checkbox]:checked ~ label::after,
input[type=radio]:checked ~ label::after {
  opacity: 1;
}

input:checked ~ .rn-check-box-label::before {
  background: var(--color-primary) !important;
}

input[type=radio] ~ label::before {
  border-radius: 50%;
}
input[type=radio] ~ label::after {
  width: 8px;
  height: 8px;
  left: 3px;
  background: #fff;
  border-radius: 50%;
}

.form-group {
  margin-bottom: 20px;
}
.form-group label {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
}
.form-group input {
  border: 0 none;
  border-radius: 4px;
  height: 50px;
  font-size: var(--font-size-b2);
  transition: var(--transition);
  padding: 0 20px;
  background-color: var(--color-lightest);
  border: 1px solid transparent;
  transition: var(--transition);
}
.form-group input:focus {
  border-color: var(--color-primary);
  box-shadow: none;
}
.form-group textarea {
  min-height: 160px;
  border: 0 none;
  border-radius: 4px;
  resize: none;
  padding: 15px;
  font-size: var(--font-size-b2);
  transition: var(--transition);
  background-color: var(--color-lightest);
  border: 1px solid transparent;
}
.form-group textarea:focus {
  border-color: var(--color-primary);
}

input[type=submit] {
  width: auto;
  padding: 0 30px;
  border-radius: 500px;
  display: inline-block;
  font-weight: 500;
  transition: 0.3s;
  height: 60px;
  background: var(--color-primary);
  color: var(--color-white);
  font-weight: var(--p-medium);
  font-size: var(--font-size-b2);
  line-height: var(--line-height-b3);
  height: 50px;
  border: 2px solid var(--color-primary);
  transition: var(--transition);
}
input[type=submit]:hover {
  background: transparent;
  color: var(--color-primary);
  transform: translateY(-5px);
}

/*==============================
 *  Utilities
=================================*/
.clearfix:before,
.clearfix:after {
  content: " ";
  display: table;
}

.clearfix:after {
  clear: both;
}

.fix {
  overflow: hidden;
}

.slick-initialized .slick-slide {
  margin-bottom: -10px;
}

.slick-gutter-15 {
  margin: -30px -15px;
}
.slick-gutter-15 .slick-slide {
  padding: 30px 15px;
}

iframe {
  width: 100%;
}

/*===============================
    Background Color 
=================================*/
.bg-color-primary {
  background: var(--color-primary);
}

.bg-color-secondary {
  background: var(--color-secondary);
}

.bg-color-tertiary {
  background: var(--color-tertiary);
}

.bg-color-gray {
  background: var(--color-gray);
}

.bg-color-white {
  background: #FFFFFF;
}

.bg-color-black {
  background: #1A1A1A;
}

.bg-color-extra03 {
  background: var(--color-extra03);
}

/*===========================
Background Image 
=============================*/
.bg_image {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.bg_image--1 {
  background-image: url(../images/bg/bg-image-1.jpg);
}

.bg_image--2 {
  background-image: url(../images/bg/bg-image-2.jpg);
}

.bg_image--3 {
  background-image: url(../images/bg/bg-image-3.jpg);
}

.bg_image--4 {
  background-image: url(../images/bg/bg-image-4.jpg);
}

.bg_image--5 {
  background-image: url(../images/bg/bg-image-5.jpg);
}

.bg_image--6 {
  background-image: url(../images/bg/bg-image-6.jpg);
}

.bg_image--7 {
  background-image: url(../images/bg/bg-image-7.jpg);
}

.bg_image--8 {
  background-image: url(../images/bg/bg-image-8.jpg);
}

.bg_image--9 {
  background-image: url(../images/bg/bg-image-9.jpg);
}

.bg_image--10 {
  background-image: url(../images/bg/bg-image-10.jpg);
}

.bg_image--11 {
  background-image: url(../images/bg/bg-image-11.jpg);
}

.bg_image--12 {
  background-image: url(../images/bg/bg-image-12.jpg);
}

.bg_image--13 {
  background-image: url(../images/bg/bg-image-13.jpg);
}

.bg_image--14 {
  background-image: url(../images/bg/bg-image-14.jpg);
}

.bg_image--15 {
  background-image: url(../images/bg/bg-image-15.jpg);
}

.bg_image--16 {
  background-image: url(../images/bg/bg-image-16.jpg);
}

.bg_image--17 {
  background-image: url(../images/bg/bg-image-17.jpg);
}

.bg_image--18 {
  background-image: url(../images/bg/bg-image-18.jpg);
}

.bg_image--19 {
  background-image: url(../images/bg/bg-image-19.jpg);
}

.bg_image--20 {
  background-image: url(../images/bg/bg-image-20.jpg);
}

.bg_image--21 {
  background-image: url(../images/bg/bg-image-21.jpg);
}

.bg_image--22 {
  background-image: url(../images/bg/bg-image-22.jpg);
}

.bg_image--23 {
  background-image: url(../images/bg/bg-image-23.jpg);
}

.bg_image--24 {
  background-image: url(../images/bg/bg-image-24.jpg);
}

.bg_image--25 {
  background-image: url(../images/bg/bg-image-25.jpg);
}

.bg_image--26 {
  background-image: url(../images/bg/bg-image-26.jpg);
}

.bg_image--27 {
  background-image: url(../images/bg/bg-image-27.jpg);
}

.bg_image--28 {
  background-image: url(../images/bg/bg-image-28.jpg);
}

.bg_image--29 {
  background-image: url(../images/bg/bg-image-29.jpg);
}

.bg_image--30 {
  background-image: url(../images/bg/bg-image-30.jpg);
}

.bg_image--31 {
  background-image: url(../images/bg/bg-image-31.jpg);
}

.bg_image--32 {
  background-image: url(../images/bg/bg-image-32.jpg);
}

.bg_image--33 {
  background-image: url(../images/bg/bg-image-33.jpg);
}

.bg_image--34 {
  background-image: url(../images/bg/bg-image-34.jpg);
}

.bg_image--35 {
  background-image: url(../images/bg/bg-image-35.jpg);
}

.bg_image--36 {
  background-image: url(../images/bg/bg-image-36.jpg);
}

.bg_image--37 {
  background-image: url(../images/bg/bg-image-37.jpg);
}

.bg_image--38 {
  background-image: url(../images/bg/bg-image-38.jpg);
}

.bg_image--39 {
  background-image: url(../images/bg/bg-image-39.jpg);
}

.bg_image--40 {
  background-image: url(../images/bg/bg-image-40.jpg);
}

/* Height and width */
.fullscreen {
  min-height: 980px;
  width: 100%;
}

/*===================
Custom Row
======================*/
.row--0 {
  margin-left: 0px;
  margin-right: 0px;
}
.row--0 > [class*=col] {
  padding-left: 0px;
  padding-right: 0px;
}

.row--5 {
  margin-left: -5px;
  margin-right: -5px;
}
.row--5 > [class*=col] {
  padding-left: 5px;
  padding-right: 5px;
}

.row--10 {
  margin-left: -10px;
  margin-right: -10px;
}
.row--10 > [class*=col] {
  padding-left: 10px;
  padding-right: 10px;
}

.row--20 {
  margin-left: -20px;
  margin-right: -20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--20 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--20 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--20 > [class*=col], .row--20 > [class*=col-] {
  padding-left: 20px;
  padding-right: 20px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--20 > [class*=col], .row--20 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--25 {
  margin-left: -25px;
  margin-right: -25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--25 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--25 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--25 > [class*=col], .row--25 > [class*=col-] {
  padding-left: 25px;
  padding-right: 25px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--25 > [class*=col], .row--25 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--30 {
  margin-left: -30px;
  margin-right: -30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--30 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--30 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--30 > [class*=col], .row--30 > [class*=col-] {
  padding-left: 30px;
  padding-right: 30px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--30 > [class*=col], .row--30 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--45 {
  margin-left: -45px;
  margin-right: -45px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--45 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--45 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--45 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--45 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--45 > [class*=col], .row--45 > [class*=col-] {
  padding-left: 45px;
  padding-right: 45px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--45 > [class*=col], .row--45 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--40 {
  margin-left: -40px;
  margin-right: -40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--40 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--40 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--40 > [class*=col], .row--40 > [class*=col-] {
  padding-left: 40px;
  padding-right: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--40 > [class*=col], .row--40 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

.row--60 {
  margin-left: -60px;
  margin-right: -60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--60 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--60 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--60 {
    margin-left: -15px;
    margin-right: -15px;
  }
}
@media only screen and (max-width: 767px) {
  .row--60 {
    margin-left: -15px !important;
    margin-right: -15px !important;
  }
}
.row--60 > [class*=col], .row--60 > [class*=col-] {
  padding-left: 60px;
  padding-right: 60px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}
@media only screen and (max-width: 767px) {
  .row--60 > [class*=col], .row--60 > [class*=col-] {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
}

/*===========================
    Input Placeholder
=============================*/
input:-moz-placeholder,
textarea:-moz-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}

/*=============================
	Overlay styles 
==============================*/
[data-overlay],
[data-black-overlay],
[data-white-overlay] {
  position: relative;
  z-index: 2;
}

[data-overlay] > div,
[data-overlay] > *,
[data-black-overlay] > div,
[data-black-overlay] > *,
[data-white-overlay] > div,
[data-white-overlay] > * {
  position: relative;
  z-index: 2;
}

[data-overlay]:before,
[data-black-overlay]:before,
[data-white-overlay]:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: -1;
}

[data-overlay]:before {
  background: var(--color-primary);
}

[data-black-overlay]:before {
  background-color: #000000;
}

[data-white-overlay]:before {
  background-color: #ffffff;
}

[data-overlay="1"]:before,
[data-black-overlay="1"]:before,
[data-white-overlay="1"]:before {
  opacity: 0.1;
}

[data-overlay="2"]:before,
[data-black-overlay="2"]:before,
[data-white-overlay="2"]:before {
  opacity: 0.2;
}

[data-overlay="3"]:before,
[data-black-overlay="3"]:before,
[data-white-overlay="3"]:before {
  opacity: 0.3;
}

[data-overlay="4"]:before,
[data-black-overlay="4"]:before,
[data-white-overlay="4"]:before {
  opacity: 0.4;
}

[data-overlay="5"]:before,
[data-black-overlay="5"]:before,
[data-white-overlay="5"]:before {
  opacity: 0.5;
}

[data-overlay="6"]:before,
[data-black-overlay="6"]:before,
[data-white-overlay="6"]:before {
  opacity: 0.6;
}

[data-overlay="7"]:before,
[data-black-overlay="7"]:before,
[data-white-overlay="7"]:before {
  opacity: 0.7;
}

[data-overlay="8"]:before,
[data-black-overlay="8"]:before,
[data-white-overlay="8"]:before {
  opacity: 0.8;
}

[data-overlay="9"]:before,
[data-black-overlay="9"]:before,
[data-white-overlay="9"]:before {
  opacity: 0.9;
}

[data-overlay="10"]:before,
[data-black-overlay="10"]:before,
[data-white-overlay="10"]:before {
  opacity: 1;
}

/*!
Animate.css - http://daneden.me/animate
Version - 3.4.0
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 Daniel Eden
*/
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.animated.bounceIn,
.animated.bounceOut {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
}

.animated.flipOutX,
.animated.flipOutY {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
}

@-webkit-keyframes bounce {
  from, 20%, 53%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
@keyframes bounce {
  from, 20%, 53%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    -webkit-animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    -webkit-transform: translate3d(0, -15px, 0);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -4px, 0);
    transform: translate3d(0, -4px, 0);
  }
}
.bounce {
  -webkit-animation-name: bounce;
  animation-name: bounce;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
}

/*jump animation */
@keyframes jump-1 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-2 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  50% {
    -webkit-transform: translate3d(0, 30px, 0);
    transform: translate3d(0, 30px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-3 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 50px, 0) scale(0.7);
    transform: translate3d(0, 50px, 0) scale(0.7);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-4 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 20px, 0) scale(0.8);
    transform: translate3d(0, 20px, 0) scale(0.8);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-5 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    transform: translate3d(0, 10px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@-webkit-keyframes flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
@keyframes flash {
  from, 50%, to {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0;
  }
}
.flash {
  -webkit-animation-name: flash;
  animation-name: flash;
}

@-webkit-keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes pulse {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  50% {
    -webkit-transform: scale3d(1.05, 1.05, 1.05);
    transform: scale3d(1.05, 1.05, 1.05);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.pulse {
  -webkit-animation-name: pulse;
  animation-name: pulse;
}

@-webkit-keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes rubberBand {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  30% {
    -webkit-transform: scale3d(1.25, 0.75, 1);
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    -webkit-transform: scale3d(0.75, 1.25, 1);
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    -webkit-transform: scale3d(1.15, 0.85, 1);
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    -webkit-transform: scale3d(0.95, 1.05, 1);
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    -webkit-transform: scale3d(1.05, 0.95, 1);
    transform: scale3d(1.05, 0.95, 1);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.rubberBand {
  -webkit-animation-name: rubberBand;
  animation-name: rubberBand;
}

@-webkit-keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
.shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}

@-webkit-keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
@keyframes swing {
  20% {
    -webkit-transform: rotate3d(0, 0, 1, 15deg);
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    -webkit-transform: rotate3d(0, 0, 1, -10deg);
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, -5deg);
    transform: rotate3d(0, 0, 1, -5deg);
  }
  to {
    -webkit-transform: rotate3d(0, 0, 1, 0deg);
    transform: rotate3d(0, 0, 1, 0deg);
  }
}
.swing {
  -webkit-transform-origin: top center;
  transform-origin: top center;
  -webkit-animation-name: swing;
  animation-name: swing;
}

@-webkit-keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes tada {
  from {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
  10%, 20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }
  30%, 50%, 70%, 90% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }
  40%, 60%, 80% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }
  to {
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.tada {
  -webkit-animation-name: tada;
  animation-name: tada;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes wobble {
  from {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes wobble {
  from {
    -webkit-transform: none;
    transform: none;
  }
  15% {
    -webkit-transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    -webkit-transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    -webkit-transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    -webkit-transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    -webkit-transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.wobble {
  -webkit-animation-name: wobble;
  animation-name: wobble;
}

@-webkit-keyframes jello {
  from, 11.1%, to {
    -webkit-transform: none;
    transform: none;
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
@keyframes jello {
  from, 11.1%, to {
    -webkit-transform: none;
    transform: none;
  }
  22.2% {
    -webkit-transform: skewX(-12.5deg) skewY(-12.5deg);
    transform: skewX(-12.5deg) skewY(-12.5deg);
  }
  33.3% {
    -webkit-transform: skewX(6.25deg) skewY(6.25deg);
    transform: skewX(6.25deg) skewY(6.25deg);
  }
  44.4% {
    -webkit-transform: skewX(-3.125deg) skewY(-3.125deg);
    transform: skewX(-3.125deg) skewY(-3.125deg);
  }
  55.5% {
    -webkit-transform: skewX(1.5625deg) skewY(1.5625deg);
    transform: skewX(1.5625deg) skewY(1.5625deg);
  }
  66.6% {
    -webkit-transform: skewX(-0.78125deg) skewY(-0.78125deg);
    transform: skewX(-0.78125deg) skewY(-0.78125deg);
  }
  77.7% {
    -webkit-transform: skewX(0.390625deg) skewY(0.390625deg);
    transform: skewX(0.390625deg) skewY(0.390625deg);
  }
  88.8% {
    -webkit-transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
  }
}
.jello {
  -webkit-animation-name: jello;
  animation-name: jello;
  -webkit-transform-origin: center;
  transform-origin: center;
}

@-webkit-keyframes bounceIn {
  from, 20%, 40%, 60%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
@keyframes bounceIn {
  from, 20%, 40%, 60%, 80%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }
  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
.bounceIn {
  -webkit-animation-name: bounceIn;
  animation-name: bounceIn;
}

@-webkit-keyframes bounceInDown {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes bounceInDown {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -3000px, 0);
    transform: translate3d(0, -3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, 25px, 0);
    transform: translate3d(0, 25px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, 5px, 0);
    transform: translate3d(0, 5px, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.bounceInDown {
  -webkit-animation-name: bounceInDown;
  animation-name: bounceInDown;
}

@-webkit-keyframes bounceInLeft {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes bounceInLeft {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-3000px, 0, 0);
    transform: translate3d(-3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(25px, 0, 0);
    transform: translate3d(25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(5px, 0, 0);
    transform: translate3d(5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.bounceInLeft {
  -webkit-animation-name: bounceInLeft;
  animation-name: bounceInLeft;
}

@-webkit-keyframes bounceInRight {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes bounceInRight {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(3000px, 0, 0);
    transform: translate3d(3000px, 0, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }
  75% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
  90% {
    -webkit-transform: translate3d(-5px, 0, 0);
    transform: translate3d(-5px, 0, 0);
  }
  to {
    -webkit-transform: none;
    transform: none;
  }
}
.bounceInRight {
  -webkit-animation-name: bounceInRight;
  animation-name: bounceInRight;
}

@-webkit-keyframes bounceInUp {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes bounceInUp {
  from, 60%, 75%, 90%, to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 3000px, 0);
    transform: translate3d(0, 3000px, 0);
  }
  60% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  75% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  90% {
    -webkit-transform: translate3d(0, -5px, 0);
    transform: translate3d(0, -5px, 0);
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.bounceInUp {
  -webkit-animation-name: bounceInUp;
  animation-name: bounceInUp;
}

@-webkit-keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  50%, 55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}
@keyframes bounceOut {
  20% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }
  50%, 55% {
    opacity: 1;
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
}
.bounceOut {
  -webkit-animation-name: bounceOut;
  animation-name: bounceOut;
}

@-webkit-keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
@keyframes bounceOutDown {
  20% {
    -webkit-transform: translate3d(0, 10px, 0);
    transform: translate3d(0, 10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, -20px, 0);
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
.bounceOutDown {
  -webkit-animation-name: bounceOutDown;
  animation-name: bounceOutDown;
}

@-webkit-keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
@keyframes bounceOutLeft {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(20px, 0, 0);
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
.bounceOutLeft {
  -webkit-animation-name: bounceOutLeft;
  animation-name: bounceOutLeft;
}

@-webkit-keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
@keyframes bounceOutRight {
  20% {
    opacity: 1;
    -webkit-transform: translate3d(-20px, 0, 0);
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
.bounceOutRight {
  -webkit-animation-name: bounceOutRight;
  animation-name: bounceOutRight;
}

@-webkit-keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes bounceOutUp {
  20% {
    -webkit-transform: translate3d(0, -10px, 0);
    transform: translate3d(0, -10px, 0);
  }
  40%, 45% {
    opacity: 1;
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
.bounceOutUp {
  -webkit-animation-name: bounceOutUp;
  animation-name: bounceOutUp;
}

@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInDownBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInDownBig {
  -webkit-animation-name: fadeInDownBig;
  animation-name: fadeInDownBig;
}

@-webkit-keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInLeft {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}

@-webkit-keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInLeftBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInLeftBig {
  -webkit-animation-name: fadeInLeftBig;
  animation-name: fadeInLeftBig;
}

@-webkit-keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInRight {
  from {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}

@-webkit-keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInRightBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInRightBig {
  -webkit-animation-name: fadeInRightBig;
  animation-name: fadeInRightBig;
}

@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInUp2 {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 40%, 0);
    transform: translate3d(0, 40%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}

@-webkit-keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInUpBig {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInUpBig {
  -webkit-animation-name: fadeInUpBig;
  animation-name: fadeInUpBig;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

@-webkit-keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
.fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown;
}

@-webkit-keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
@keyframes fadeOutDownBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 2000px, 0);
    transform: translate3d(0, 2000px, 0);
  }
}
.fadeOutDownBig {
  -webkit-animation-name: fadeOutDownBig;
  animation-name: fadeOutDownBig;
}

@-webkit-keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
.fadeOutLeft {
  -webkit-animation-name: fadeOutLeft;
  animation-name: fadeOutLeft;
}

@-webkit-keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
@keyframes fadeOutLeftBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(-2000px, 0, 0);
    transform: translate3d(-2000px, 0, 0);
  }
}
.fadeOutLeftBig {
  -webkit-animation-name: fadeOutLeftBig;
  animation-name: fadeOutLeftBig;
}

@-webkit-keyframes fadeOutRight {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes fadeOutRight {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
.fadeOutRight {
  -webkit-animation-name: fadeOutRight;
  animation-name: fadeOutRight;
}

@-webkit-keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
@keyframes fadeOutRightBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(2000px, 0, 0);
    transform: translate3d(2000px, 0, 0);
  }
}
.fadeOutRightBig {
  -webkit-animation-name: fadeOutRightBig;
  animation-name: fadeOutRightBig;
}

@-webkit-keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
.fadeOutUp {
  -webkit-animation-name: fadeOutUp;
  animation-name: fadeOutUp;
}

@-webkit-keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
@keyframes fadeOutUpBig {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -2000px, 0);
    transform: translate3d(0, -2000px, 0);
  }
}
.fadeOutUpBig {
  -webkit-animation-name: fadeOutUpBig;
  animation-name: fadeOutUpBig;
}

@-webkit-keyframes flip {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}
@keyframes flip {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -360deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  40% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -190deg);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  50% {
    -webkit-transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    transform: perspective(400px) translate3d(0, 0, 150px) rotate3d(0, 1, 0, -170deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  80% {
    -webkit-transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    transform: perspective(400px) scale3d(0.95, 0.95, 0.95);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
}
.animated.flip {
  -webkit-backface-visibility: visible;
  backface-visibility: visible;
  -webkit-animation-name: flip;
  animation-name: flip;
}

@-webkit-keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInX {
  from {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
.flipInX {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInX;
  animation-name: flipInX;
}

@-webkit-keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
@keyframes flipInY {
  from {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -20deg);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  60% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 10deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -5deg);
  }
  to {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
}
.flipInY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
}

@-webkit-keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
@keyframes flipOutX {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    transform: perspective(400px) rotate3d(1, 0, 0, -20deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    transform: perspective(400px) rotate3d(1, 0, 0, 90deg);
    opacity: 0;
  }
}
.flipOutX {
  -webkit-animation-name: flipOutX;
  animation-name: flipOutX;
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
}

@-webkit-keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
@keyframes flipOutY {
  from {
    -webkit-transform: perspective(400px);
    transform: perspective(400px);
  }
  30% {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    transform: perspective(400px) rotate3d(0, 1, 0, -15deg);
    opacity: 1;
  }
  to {
    -webkit-transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    transform: perspective(400px) rotate3d(0, 1, 0, 90deg);
    opacity: 0;
  }
}
.flipOutY {
  -webkit-backface-visibility: visible !important;
  backface-visibility: visible !important;
  -webkit-animation-name: flipOutY;
  animation-name: flipOutY;
}

@-webkit-keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes lightSpeedIn {
  from {
    -webkit-transform: translate3d(100%, 0, 0) skewX(-30deg);
    transform: translate3d(100%, 0, 0) skewX(-30deg);
    opacity: 0;
  }
  60% {
    -webkit-transform: skewX(20deg);
    transform: skewX(20deg);
    opacity: 1;
  }
  80% {
    -webkit-transform: skewX(-5deg);
    transform: skewX(-5deg);
    opacity: 1;
  }
  to {
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.lightSpeedIn {
  -webkit-animation-name: lightSpeedIn;
  animation-name: lightSpeedIn;
  -webkit-animation-timing-function: ease-out;
  animation-timing-function: ease-out;
}

@-webkit-keyframes lightSpeedOut {
  from {
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}
@keyframes lightSpeedOut {
  from {
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(100%, 0, 0) skewX(30deg);
    transform: translate3d(100%, 0, 0) skewX(30deg);
    opacity: 0;
  }
}
.lightSpeedOut {
  -webkit-animation-name: lightSpeedOut;
  animation-name: lightSpeedOut;
  -webkit-animation-timing-function: ease-in;
  animation-timing-function: ease-in;
}

@-webkit-keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateIn {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, -200deg);
    transform: rotate3d(0, 0, 1, -200deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateIn {
  -webkit-animation-name: rotateIn;
  animation-name: rotateIn;
}

@-webkit-keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInDownLeft {
  -webkit-animation-name: rotateInDownLeft;
  animation-name: rotateInDownLeft;
}

@-webkit-keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInDownRight {
  -webkit-animation-name: rotateInDownRight;
  animation-name: rotateInDownRight;
}

@-webkit-keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInUpLeft {
  -webkit-animation-name: rotateInUpLeft;
  animation-name: rotateInUpLeft;
}

@-webkit-keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
@keyframes rotateInUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -90deg);
    transform: rotate3d(0, 0, 1, -90deg);
    opacity: 0;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: none;
    transform: none;
    opacity: 1;
  }
}
.rotateInUpRight {
  -webkit-animation-name: rotateInUpRight;
  animation-name: rotateInUpRight;
}

@-webkit-keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}
@keyframes rotateOut {
  from {
    -webkit-transform-origin: center;
    transform-origin: center;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-transform: rotate3d(0, 0, 1, 200deg);
    transform: rotate3d(0, 0, 1, 200deg);
    opacity: 0;
  }
}
.rotateOut {
  -webkit-animation-name: rotateOut;
  animation-name: rotateOut;
}

@-webkit-keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
@keyframes rotateOutDownLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, 45deg);
    transform: rotate3d(0, 0, 1, 45deg);
    opacity: 0;
  }
}
.rotateOutDownLeft {
  -webkit-animation-name: rotateOutDownLeft;
  animation-name: rotateOutDownLeft;
}

@-webkit-keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
@keyframes rotateOutDownRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
.rotateOutDownRight {
  -webkit-animation-name: rotateOutDownRight;
  animation-name: rotateOutDownRight;
}

@-webkit-keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
@keyframes rotateOutUpLeft {
  from {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: left bottom;
    transform-origin: left bottom;
    -webkit-transform: rotate3d(0, 0, 1, -45deg);
    transform: rotate3d(0, 0, 1, -45deg);
    opacity: 0;
  }
}
.rotateOutUpLeft {
  -webkit-animation-name: rotateOutUpLeft;
  animation-name: rotateOutUpLeft;
}

@-webkit-keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}
@keyframes rotateOutUpRight {
  from {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    opacity: 1;
  }
  to {
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
    -webkit-transform: rotate3d(0, 0, 1, 90deg);
    transform: rotate3d(0, 0, 1, 90deg);
    opacity: 0;
  }
}
.rotateOutUpRight {
  -webkit-animation-name: rotateOutUpRight;
  animation-name: rotateOutUpRight;
}

@-webkit-keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  20%, 60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  40%, 80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}
@keyframes hinge {
  0% {
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  20%, 60% {
    -webkit-transform: rotate3d(0, 0, 1, 80deg);
    transform: rotate3d(0, 0, 1, 80deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
  }
  40%, 80% {
    -webkit-transform: rotate3d(0, 0, 1, 60deg);
    transform: rotate3d(0, 0, 1, 60deg);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    opacity: 1;
  }
  to {
    -webkit-transform: translate3d(0, 700px, 0);
    transform: translate3d(0, 700px, 0);
    opacity: 0;
  }
}
.hinge {
  -webkit-animation-name: hinge;
  animation-name: hinge;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes rollIn {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
    transform: translate3d(-100%, 0, 0) rotate3d(0, 0, 1, -120deg);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.rollIn {
  -webkit-animation-name: rollIn;
  animation-name: rollIn;
}

/* originally authored by Nick Pettit - https://github.com/nickpettit/glide */
@-webkit-keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
@keyframes rollOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
    transform: translate3d(100%, 0, 0) rotate3d(0, 0, 1, 120deg);
  }
}
.rollOut {
  -webkit-animation-name: rollOut;
  animation-name: rollOut;
}

@-webkit-keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
@keyframes zoomIn {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
.zoomIn {
  -webkit-animation-name: zoomIn;
  animation-name: zoomIn;
}

@-webkit-keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInDown {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInDown {
  -webkit-animation-name: zoomInDown;
  animation-name: zoomInDown;
}

@-webkit-keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInLeft {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(-1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInLeft {
  -webkit-animation-name: zoomInLeft;
  animation-name: zoomInLeft;
}

@-webkit-keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInRight {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(1000px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-10px, 0, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInRight {
  -webkit-animation-name: zoomInRight;
  animation-name: zoomInRight;
}

@-webkit-keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomInUp {
  from {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 1000px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  60% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomInUp {
  -webkit-animation-name: zoomInUp;
  animation-name: zoomInUp;
}

@-webkit-keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
@keyframes zoomOut {
  from {
    opacity: 1;
  }
  50% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }
  to {
    opacity: 0;
  }
}
.zoomOut {
  -webkit-animation-name: zoomOut;
  animation-name: zoomOut;
}

@-webkit-keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomOutDown {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, -60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, 2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomOutDown {
  -webkit-animation-name: zoomOutDown;
  animation-name: zoomOutDown;
}

@-webkit-keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}
@keyframes zoomOutLeft {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(-2000px, 0, 0);
    transform: scale(0.1) translate3d(-2000px, 0, 0);
    -webkit-transform-origin: left center;
    transform-origin: left center;
  }
}
.zoomOutLeft {
  -webkit-animation-name: zoomOutLeft;
  animation-name: zoomOutLeft;
}

@-webkit-keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}
@keyframes zoomOutRight {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(-42px, 0, 0);
  }
  to {
    opacity: 0;
    -webkit-transform: scale(0.1) translate3d(2000px, 0, 0);
    transform: scale(0.1) translate3d(2000px, 0, 0);
    -webkit-transform-origin: right center;
    transform-origin: right center;
  }
}
.zoomOutRight {
  -webkit-animation-name: zoomOutRight;
  animation-name: zoomOutRight;
}

@-webkit-keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
@keyframes zoomOutUp {
  40% {
    opacity: 1;
    -webkit-transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
    -webkit-animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  }
  to {
    opacity: 0;
    -webkit-transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -2000px, 0);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    -webkit-animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
  }
}
.zoomOutUp {
  -webkit-animation-name: zoomOutUp;
  animation-name: zoomOutUp;
}

@-webkit-keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInDown {
  from {
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slideInDown {
  -webkit-animation-name: slideInDown;
  animation-name: slideInDown;
}

@-webkit-keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInLeft {
  from {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInLeft2 {
  from {
    -webkit-transform: translate3d(-10%, 0, 0);
    transform: translate3d(-10%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slideInLeft {
  -webkit-animation-name: slideInLeft;
  animation-name: slideInLeft;
}

@-webkit-keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInRight {
  from {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.slideInRight {
  -webkit-animation-name: slideInRight;
  animation-name: slideInRight;
}

@-webkit-keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInUp {
  from {
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
    visibility: visible;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes slideInUp2 {
  from {
    -webkit-transform: translate3d(0, 20%, 0);
    transform: translate3d(0, 20%, 0);
    visibility: hidden;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    visibility: visible;
  }
}
@keyframes slideInUp3 {
  from {
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
    visibility: hidden;
  }
  to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    visibility: visible;
  }
}
[data-aos=slideInUp2] {
  opacity: 0;
  transition-property: transform, opacity;
}
[data-aos=slideInUp2].aos-animate {
  opacity: 1;
}
@media screen and (min-width: 768px) {
  [data-aos=slideInUp2] {
    transform: translateY(30px);
  }
  [data-aos=slideInUp2].aos-animate {
    transform: translateY(0);
  }
}

.slideInUp {
  -webkit-animation-name: slideInUp;
  animation-name: slideInUp;
}

@-webkit-keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes slideOutDown {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
.slideOutDown {
  -webkit-animation-name: slideOutDown;
  animation-name: slideOutDown;
}

@-webkit-keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes slideOutLeft {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
}
.slideOutLeft {
  -webkit-animation-name: slideOutLeft;
  animation-name: slideOutLeft;
}

@-webkit-keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
@keyframes slideOutRight {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
}
.slideOutRight {
  -webkit-animation-name: slideOutRight;
  animation-name: slideOutRight;
}

@-webkit-keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes slideOutUp {
  from {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  to {
    visibility: hidden;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
.slideOutUp {
  -webkit-animation-name: slideOutUp;
  animation-name: slideOutUp;
}

@keyframes jump-1 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  40% {
    -webkit-transform: translate3d(0, 20px, 0);
    transform: translate3d(0, 20px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes jump-2 {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  50% {
    -webkit-transform: translate3d(0, 30px, 0);
    transform: translate3d(0, 30px, 0);
  }
  100% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes rotateIt {
  to {
    transform: rotate(-360deg);
  }
}
@keyframes rotateIt2 {
  to {
    transform: rotate(360deg);
  }
}
@keyframes shape-service-1 {
  0% {
    right: -40%;
    top: 30%;
  }
  100% {
    right: -23%;
    top: 0;
  }
}
@keyframes animate-floting {
  0% {
    transform: translateX(50%);
  }
  50% {
    transform: translateX(-40%);
  }
  100% {
    transform: translateX(40%);
  }
}
@keyframes animate-floting-2 {
  0% {
    transform: translateX(-50%);
  }
  50% {
    transform: translateX(40%);
  }
  100% {
    transform: translateX(-40%);
  }
}
@keyframes animate-floting-3 {
  0% {
    transform: translateX(-20%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-20%);
  }
}
.floting-line {
  animation: animate-floting 15s linear infinite;
}
.floting-line:hover {
  animation-play-state: paused;
}

.floting-line-2 {
  animation: animate-floting-2 15s linear infinite;
}
.floting-line-2:hover {
  animation-play-state: paused;
}

@keyframes waves {
  0% {
    -webkit-transform: scale(0.2, 0.2);
    transform: scale(0.2, 0.2);
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  }
  50% {
    opacity: 0.9;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)";
  }
  100% {
    -webkit-transform: scale(0.9, 0.9);
    transform: scale(0.9, 0.9);
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  }
}
@keyframes vsmorph {
  0% {
    border-radius: var(--morp-value);
  }
  50% {
    border-radius: var(--morp-md-value);
  }
  100% {
    border-radius: 40% 60%;
  }
}
@keyframes morpspin {
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@keyframes customOne {
  0% {
    -webkit-transform: translateY(-50%) scale(0);
    transform: translateY(-50%) scale(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateY(-50%) scale(1.3);
    transform: translateY(-50%) scale(1.3);
    opacity: 0;
  }
}
@keyframes liveAuction {
  0% {
    background: var(--color-white);
  }
  100% {
    background: var(--color-danger);
  }
}
.cd-intro {
  margin: 4em auto;
}

@media only screen and (min-width: 768px) {
  .cd-intro {
    margin: 5em auto;
  }
}
@media only screen and (min-width: 1170px) {
  .cd-intro {
    margin: 6em auto;
  }
}
.cd-headline {
  font-size: 3rem;
  line-height: 1.2;
}

@media only screen and (min-width: 768px) {
  .cd-headline {
    font-size: 4.4rem;
    font-weight: 300;
  }
}
@media only screen and (min-width: 1170px) {
  .cd-headline {
    font-size: 48px;
  }
}
@media only screen and (max-width: 768px) {
  .cd-headline {
    font-size: 40px;
  }
}
@media only screen and (max-width: 479px) {
  .cd-headline {
    font-size: 26px;
  }
}
.cd-words-wrapper {
  display: inline-block;
  position: relative;
  text-align: left;
}

.cd-words-wrapper b {
  display: inline-block;
  position: absolute;
  white-space: nowrap;
  left: 0;
  top: 0;
}

.cd-words-wrapper b.is-visible {
  position: relative;
}

.no-js .cd-words-wrapper b {
  opacity: 0;
}

.no-js .cd-words-wrapper b.is-visible {
  opacity: 1;
}

/* -------------------------------- 

xclip 

-------------------------------- */
.cd-headline.clip span {
  display: inline-block;
  padding: 0;
}

.cd-headline.clip .cd-words-wrapper {
  overflow: hidden;
  vertical-align: middle;
  position: relative;
  margin-top: -20px;
}
.cd-headline.clip .cd-words-wrapper b {
  font-weight: 700;
}

.cd-headline.clip .cd-words-wrapper::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-primary-3);
  transform: translateY(-50%);
}

.cd-headline.clip b {
  opacity: 0;
}

.cd-headline.clip b.is-visible {
  opacity: 1;
}

/**
 * Settings
 */
/**
  * Easings
  */
/**
  * Core
  */
[data-sal] {
  transition-duration: 0.2s;
  transition-delay: 0s;
  transition-duration: var(--sal-duration, 0.2s);
  transition-delay: var(--sal-delay, 0s);
  transition-timing-function: var(--sal-easing, ease);
}

[data-sal][data-sal-duration="200"] {
  transition-duration: 0.2s;
}

[data-sal][data-sal-duration="250"] {
  transition-duration: 0.25s;
}

[data-sal][data-sal-duration="300"] {
  transition-duration: 0.3s;
}

[data-sal][data-sal-duration="350"] {
  transition-duration: 0.35s;
}

[data-sal][data-sal-duration="400"] {
  transition-duration: 0.4s;
}

[data-sal][data-sal-duration="450"] {
  transition-duration: 0.45s;
}

[data-sal][data-sal-duration="500"] {
  transition-duration: 0.5s;
}

[data-sal][data-sal-duration="550"] {
  transition-duration: 0.55s;
}

[data-sal][data-sal-duration="600"] {
  transition-duration: 0.6s;
}

[data-sal][data-sal-duration="650"] {
  transition-duration: 0.65s;
}

[data-sal][data-sal-duration="700"] {
  transition-duration: 0.7s;
}

[data-sal][data-sal-duration="750"] {
  transition-duration: 0.75s;
}

[data-sal][data-sal-duration="800"] {
  transition-duration: 0.8s;
}

[data-sal][data-sal-duration="850"] {
  transition-duration: 0.85s;
}

[data-sal][data-sal-duration="900"] {
  transition-duration: 0.9s;
}

[data-sal][data-sal-duration="950"] {
  transition-duration: 0.95s;
}

[data-sal][data-sal-duration="1000"] {
  transition-duration: 1s;
}

[data-sal][data-sal-duration="1050"] {
  transition-duration: 1.05s;
}

[data-sal][data-sal-duration="1100"] {
  transition-duration: 1.1s;
}

[data-sal][data-sal-duration="1150"] {
  transition-duration: 1.15s;
}

[data-sal][data-sal-duration="1200"] {
  transition-duration: 1.2s;
}

[data-sal][data-sal-duration="1250"] {
  transition-duration: 1.25s;
}

[data-sal][data-sal-duration="1300"] {
  transition-duration: 1.3s;
}

[data-sal][data-sal-duration="1350"] {
  transition-duration: 1.35s;
}

[data-sal][data-sal-duration="1400"] {
  transition-duration: 1.4s;
}

[data-sal][data-sal-duration="1450"] {
  transition-duration: 1.45s;
}

[data-sal][data-sal-duration="1500"] {
  transition-duration: 1.5s;
}

[data-sal][data-sal-duration="1550"] {
  transition-duration: 1.55s;
}

[data-sal][data-sal-duration="1600"] {
  transition-duration: 1.6s;
}

[data-sal][data-sal-duration="1650"] {
  transition-duration: 1.65s;
}

[data-sal][data-sal-duration="1700"] {
  transition-duration: 1.7s;
}

[data-sal][data-sal-duration="1750"] {
  transition-duration: 1.75s;
}

[data-sal][data-sal-duration="1800"] {
  transition-duration: 1.8s;
}

[data-sal][data-sal-duration="1850"] {
  transition-duration: 1.85s;
}

[data-sal][data-sal-duration="1900"] {
  transition-duration: 1.9s;
}

[data-sal][data-sal-duration="1950"] {
  transition-duration: 1.95s;
}

[data-sal][data-sal-duration="2000"] {
  transition-duration: 2s;
}

[data-sal][data-sal-delay="50"] {
  transition-delay: 0.05s;
}

[data-sal][data-sal-delay="100"] {
  transition-delay: 0.1s;
}

[data-sal][data-sal-delay="150"] {
  transition-delay: 0.15s;
}

[data-sal][data-sal-delay="200"] {
  transition-delay: 0.2s;
}

[data-sal][data-sal-delay="250"] {
  transition-delay: 0.25s;
}

[data-sal][data-sal-delay="300"] {
  transition-delay: 0.3s;
}

[data-sal][data-sal-delay="350"] {
  transition-delay: 0.35s;
}

[data-sal][data-sal-delay="400"] {
  transition-delay: 0.4s;
}

[data-sal][data-sal-delay="450"] {
  transition-delay: 0.45s;
}

[data-sal][data-sal-delay="500"] {
  transition-delay: 0.5s;
}

[data-sal][data-sal-delay="550"] {
  transition-delay: 0.55s;
}

[data-sal][data-sal-delay="600"] {
  transition-delay: 0.6s;
}

[data-sal][data-sal-delay="650"] {
  transition-delay: 0.65s;
}

[data-sal][data-sal-delay="700"] {
  transition-delay: 0.7s;
}

[data-sal][data-sal-delay="750"] {
  transition-delay: 0.75s;
}

[data-sal][data-sal-delay="800"] {
  transition-delay: 0.8s;
}

[data-sal][data-sal-delay="850"] {
  transition-delay: 0.85s;
}

[data-sal][data-sal-delay="900"] {
  transition-delay: 0.9s;
}

[data-sal][data-sal-delay="950"] {
  transition-delay: 0.95s;
}

[data-sal][data-sal-delay="1000"] {
  transition-delay: 1s;
}

[data-sal][data-sal-easing=linear] {
  transition-timing-function: linear;
}

[data-sal][data-sal-easing=ease] {
  transition-timing-function: ease;
}

[data-sal][data-sal-easing=ease-in] {
  transition-timing-function: ease-in;
}

[data-sal][data-sal-easing=ease-out] {
  transition-timing-function: ease-out;
}

[data-sal][data-sal-easing=ease-in-out] {
  transition-timing-function: ease-in-out;
}

[data-sal][data-sal-easing=ease-in-cubic] {
  transition-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

[data-sal][data-sal-easing=ease-out-cubic] {
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}

[data-sal][data-sal-easing=ease-in-out-cubic] {
  transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
}

[data-sal][data-sal-easing=ease-in-circ] {
  transition-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.335);
}

[data-sal][data-sal-easing=ease-out-circ] {
  transition-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}

[data-sal][data-sal-easing=ease-in-out-circ] {
  transition-timing-function: cubic-bezier(0.785, 0.135, 0.15, 0.86);
}

[data-sal][data-sal-easing=ease-in-expo] {
  transition-timing-function: cubic-bezier(0.95, 0.05, 0.795, 0.035);
}

[data-sal][data-sal-easing=ease-out-expo] {
  transition-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
}

[data-sal][data-sal-easing=ease-in-out-expo] {
  transition-timing-function: cubic-bezier(1, 0, 0, 1);
}

[data-sal][data-sal-easing=ease-in-quad] {
  transition-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

[data-sal][data-sal-easing=ease-out-quad] {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

[data-sal][data-sal-easing=ease-in-out-quad] {
  transition-timing-function: cubic-bezier(0.455, 0.03, 0.515, 0.955);
}

[data-sal][data-sal-easing=ease-in-quart] {
  transition-timing-function: cubic-bezier(0.895, 0.03, 0.685, 0.22);
}

[data-sal][data-sal-easing=ease-out-quart] {
  transition-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
}

[data-sal][data-sal-easing=ease-in-out-quart] {
  transition-timing-function: cubic-bezier(0.77, 0, 0.175, 1);
}

[data-sal][data-sal-easing=ease-in-quint] {
  transition-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
}

[data-sal][data-sal-easing=ease-out-quint] {
  transition-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}

[data-sal][data-sal-easing=ease-in-out-quint] {
  transition-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
}

[data-sal][data-sal-easing=ease-in-sine] {
  transition-timing-function: cubic-bezier(0.47, 0, 0.745, 0.715);
}

[data-sal][data-sal-easing=ease-out-sine] {
  transition-timing-function: cubic-bezier(0.39, 0.575, 0.565, 1);
}

[data-sal][data-sal-easing=ease-in-out-sine] {
  transition-timing-function: cubic-bezier(0.445, 0.05, 0.55, 0.95);
}

[data-sal][data-sal-easing=ease-in-back] {
  transition-timing-function: cubic-bezier(0.6, -0.28, 0.735, 0.045);
}

[data-sal][data-sal-easing=ease-out-back] {
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

[data-sal][data-sal-easing=ease-in-out-back] {
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/**
  * Animations
  */
[data-sal|=fade] {
  opacity: 0;
  transition-property: opacity;
}

[data-sal|=fade].sal-animate,
body.sal-disabled [data-sal|=fade] {
  opacity: 1;
}

[data-sal|=slide] {
  opacity: 0;
  transition-property: opacity, transform;
}

[data-sal=slide-up] {
  transform: translateY(20%);
}

[data-sal=slide-down] {
  transform: translateY(-20%);
}

[data-sal=slide-left] {
  transform: translateX(20%);
}

[data-sal=slide-right] {
  transform: translateX(-20%);
}

[data-sal|=slide].sal-animate,
body.sal-disabled [data-sal|=slide] {
  opacity: 1;
  transform: none;
}

[data-sal|=zoom] {
  opacity: 0;
  transition-property: opacity, transform;
}

[data-sal=zoom-in] {
  transform: scale(0.5);
}

[data-sal=zoom-out] {
  transform: scale(1.1);
}

[data-sal|=zoom].sal-animate,
body.sal-disabled [data-sal|=zoom] {
  opacity: 1;
  transform: none;
}

[data-sal|=flip] {
  backface-visibility: hidden;
  transition-property: transform;
}

[data-sal=flip-left] {
  transform: perspective(2000px) rotateY(-91deg);
}

[data-sal=flip-right] {
  transform: perspective(2000px) rotateY(91deg);
}

[data-sal=flip-up] {
  transform: perspective(2000px) rotateX(-91deg);
}

[data-sal=flip-down] {
  transform: perspective(2000px) rotateX(91deg);
}

[data-sal|=flip].sal-animate,
body.sal-disabled [data-sal|=flip] {
  transform: none;
}

/* elements */
body {
  background: #F3F4F6;
}

.sidebar_left {
  height: 100vh;
  padding: 16px 0;
  border-right: 1px solid #E2E2E2;
  width: 273px;
  background: #fff;
  z-index: 999;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sidebar_left {
    left: -273px;
  }
}
@media only screen and (max-width: 767px) {
  .sidebar_left {
    left: -273px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .sidebar_left.collapsed {
    left: 0 !important;
  }
}
@media only screen and (max-width: 767px) {
  .sidebar_left.collapsed {
    left: 0 !important;
  }
}

.sidebar_left {
  position: fixed;
}
.sidebar_left .logo {
  padding: 10px 26px;
}

.rcf_dashboard {
  display: flex;
  align-items: flex-start;
}
.rcf_dashboard .right-area-body-content {
  width: calc(100% - 273px);
  margin-left: auto;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rcf_dashboard .right-area-body-content {
    width: 100%;
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  .rcf_dashboard .right-area-body-content {
    width: 100%;
    margin-left: 0;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .rcf_dashboard .right-area-body-content.collapsed {
    width: 100% !important;
    margin-left: auto !important;
    margin-right: -273px;
  }
}
@media only screen and (max-width: 767px) {
  .rcf_dashboard .right-area-body-content.collapsed {
    width: 100% !important;
    margin-left: auto !important;
    margin-right: -273px;
  }
}

.rts-side-nav-area-left {
  list-style: none;
  padding-left: 0;
  margin-top: 35px;
}
.rts-side-nav-area-left li.single-menu-item {
  margin: 0;
}
.rts-side-nav-area-left li.single-menu-item a {
  display: flex;
  align-items: center;
  gap: 15px;
  width: 100%;
  position: relative;
  height: 46px;
  padding: 0 27px;
  transition: 0.3s;
}
.rts-side-nav-area-left li.single-menu-item a.with-plus::after {
  position: absolute;
  content: "\f078";
  font-family: var(--font-three);
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  transition: 0.3s;
  font-size: 14px;
}
.rts-side-nav-area-left li.single-menu-item a p {
  transition: 0.3s;
  font-weight: 500;
  color: #2D3B29;
}
.rts-side-nav-area-left li.single-menu-item a img {
  transition: 0.3s;
}
.rts-side-nav-area-left li.single-menu-item a:hover {
  background: var(--color-primary);
  color: #fff;
}
.rts-side-nav-area-left li.single-menu-item a:hover p {
  color: #fff;
  margin-bottom: 0;
}
.rts-side-nav-area-left li.single-menu-item a:hover img {
  filter: brightness(0) saturate(100%) invert(99%) sepia(1%) saturate(7498%) hue-rotate(306deg) brightness(109%) contrast(100%);
}
.rts-side-nav-area-left li .submenu {
  list-style: none;
}
.rts-side-nav-area-left li .submenu li {
  margin: 0;
}
.rts-side-nav-area-left li .submenu .mobile-menu-link {
  height: 42px;
  font-weight: 500;
  color: #2D3B29;
  padding-left: 45px;
  position: relative;
}
.rts-side-nav-area-left li .submenu .mobile-menu-link::after {
  position: absolute;
  content: "";
  height: 5px;
  width: 5px;
  background: var(--color-primary);
  left: 25px;
  border-radius: 50%;
  transition: 0.3s;
}
.rts-side-nav-area-left li .submenu .mobile-menu-link:hover::after {
  background: #fff;
}

.right-area-body-content {
  height: 100vh;
}

.action-interactive-area__header {
  display: flex;
  align-items: center;
  gap: 10px;
}
@media only screen and (max-width: 575px) {
  .action-interactive-area__header {
    gap: 5px;
  }
}
.action-interactive-area__header .single_action__haeader {
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #E5E4FF;
  transition: 0.3s;
  cursor: pointer;
}
.action-interactive-area__header .single_action__haeader.active::after {
  display: none !important;
}
.action-interactive-area__header .single_action__haeader.active::before {
  display: none !important;
}
.action-interactive-area__header .single_action__haeader svg path {
  transition: 0.3s;
}
.action-interactive-area__header .single_action__haeader:hover {
  background: var(--color-primary);
}
.action-interactive-area__header .single_action__haeader:hover svg path {
  fill: #fff;
}
.action-interactive-area__header .single_action__haeader .avatar {
  border: 2px solid var(--color-primary);
  border-radius: 50%;
}

.single_action__haeader.search-action {
  position: relative;
}
.single_action__haeader.search-action .search-opoup {
  display: none;
  position: absolute;
  top: 150%;
  inset-inline-end: 0;
  min-width: 375px;
  border-radius: 10px;
  background-color: #fff;
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  z-index: 9;
}
@media only screen and (max-width: 575px) {
  .single_action__haeader.search-action .search-opoup {
    inset-inline-end: auto;
  }
}
@media only screen and (max-width: 479px) {
  .single_action__haeader.search-action .search-opoup {
    min-width: 278px;
    right: -157px;
    left: auto;
    inset-inline-end: auto;
  }
}
.single_action__haeader.search-action .search-opoup input {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  color: #707070;
  padding: 20px 60px 20px 32px;
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.single_action__haeader.search-action .search-opoup i {
  position: absolute;
  inset-inline-end: 32px;
  font-size: 20px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.single_action__haeader.notification {
  position: relative;
}
.single_action__haeader.notification svg {
  position: relative;
}
.single_action__haeader.notification .notification_main_wrapper {
  display: none;
  position: absolute;
  top: 128%;
  inset-inline-end: 0;
  min-width: 375px;
  border-radius: 10px;
  background-color: #fff;
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  z-index: 9;
}
@media only screen and (max-width: 575px) {
  .single_action__haeader.notification .notification_main_wrapper {
    min-width: 320px;
    inset-inline-end: auto;
  }
}
@media only screen and (max-width: 479px) {
  .single_action__haeader.notification .notification_main_wrapper {
    right: -109px;
  }
}
.single_action__haeader.notification .notification_main_wrapper::before {
  content: "";
  position: absolute;
  top: -10px;
  inset-inline-end: 10px;
  border-bottom: 10px solid #fff;
  -webkit-border-start: 10px solid transparent;
  border-inline-start: 10px solid transparent;
  -webkit-border-end: 10px solid transparent;
  border-inline-end: 10px solid transparent;
}
.single_action__haeader.notification .notification_main_wrapper .title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  font-size: 16px;
  line-height: 22px;
  font-weight: 500;
  margin-bottom: 0;
  padding: 15px 20px;
  margin: 20px 20px 5px;
  border-radius: 8px;
  background-color: #f3f2f7;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
}
.single_action__haeader.notification .notification_main_wrapper .title .count {
  background-color: var(--color-primary);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 10px;
  border-radius: 100%;
  color: #ffffff;
  background-color: var(--color-primary);
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single_action__haeader.notification .notification_main_wrapper .title .count {
    right: 50px;
  }
}
.single_action__haeader.notification .notification_main_wrapper .notification__content {
  padding: 30px 0;
  margin: 0 20px;
  -webkit-padding-end: 6px;
  padding-inline-end: 6px;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: none;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin: 0;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items {
  list-style: none;
  margin: 0;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  color: var(--body-color);
  font-size: 14px;
  line-height: 21px;
  font-weight: 600;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--dark-color) !important;
  -webkit-box-align: start !important;
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link .avatar {
  width: 40px;
  height: 40px;
  margin-top: 3px;
  border-radius: 11%;
  background-color: #eceaf3;
  border: none;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link .avatar img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link .main-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 5px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link .main-content .name-user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  font-size: 15px;
  line-height: 21px;
  font-weight: 500;
  margin-bottom: 0;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link .main-content .name-user .time-ago {
  color: var(--color-primary) !important;
  font-size: 12px;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link .main-content .disc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  font-size: 15px;
  font-weight: 400;
  font-size: 14px;
}
.single_action__haeader.notification .notification_main_wrapper .notification__content .notification__items .single__items .single-link .main-content .disc .count {
  width: 10px;
  height: 10px;
  background-color: var(--color-primary) !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 100%;
  font-size: 10px;
  color: #fff;
  background: var(--color-danger);
  margin-top: 5px;
}

.single_action__haeader.user_avatar__information {
  position: relative;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper {
  margin-top: 10px;
  display: none;
  position: absolute;
  top: 100%;
  inset-inline-end: 0;
  min-width: 375px;
  border-radius: 10px;
  background-color: #fff;
  -webkit-box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  z-index: 9;
}
@media only screen and (max-width: 479px) {
  .single_action__haeader.user_avatar__information .user_information_main_wrapper {
    min-width: 290px;
  }
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper.language-area {
  min-width: 170px;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper.language-area .select-language-area {
  list-style: none;
  padding: 0;
  margin: 0;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper.language-area .select-language-area li {
  margin: 0;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper.language-area .select-language-area li a {
  display: block;
  padding: 7px 15px;
  border-bottom: 1px solid rgba(140, 137, 156, 0.0823529412);
  transition: 0.3s;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper.language-area .select-language-area li a:hover {
  color: #fff;
  background: var(--color-primary);
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper.language-area .select-language-area li:last-child a {
  border: none;
  border-radius: 0 0 10px 10px;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper::before {
  content: "";
  position: absolute;
  top: -10px;
  inset-inline-end: 10px;
  border-bottom: 10px solid #fff;
  -webkit-border-start: 10px solid transparent;
  border-inline-start: 10px solid transparent;
  -webkit-border-end: 10px solid transparent;
  border-inline-end: 10px solid transparent;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 15px;
  margin: 20px 20px 0;
  border-radius: 12px;
  background: #f3f2f7;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_header .main-avatar {
  border-radius: 5px;
  display: block;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_header .main-avatar img {
  width: 100%;
  border-radius: 5px;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_header .user_naim-information .title {
  font-size: 18px;
  line-height: 22px;
  font-weight: 500;
  margin-bottom: 5px;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_header .user_naim-information .desig {
  font-size: 14px;
  line-height: 21px;
  font-weight: 400;
  color: #a3a3a3;
  margin-bottom: 0;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_body_content {
  padding: 30px 0;
  margin: 0 20px;
  -webkit-padding-end: 6px;
  padding-inline-end: 6px;
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: none;
  max-height: unset;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_body_content .items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 0;
  margin: 0;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_body_content .items .single_items {
  list-style: none;
  margin: 0;
  padding: 0;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .user_body_content .items .single_items .hader_popup_link {
  color: #272222 !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 15px;
  color: #25232c;
  font-size: 16px;
  line-height: 21px;
  font-weight: 600;
  cursor: pointer;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .popup-footer-btn {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  padding: 15px 25px;
  margin: 0 20px 20px;
  border-radius: 10px;
  background: #f3f2f7;
}
.single_action__haeader.user_avatar__information .user_information_main_wrapper .popup-footer-btn a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  color: #464255;
  font-size: 14px;
  line-height: 21px;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: #464255 !important;
}

header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 13px 29px;
  background: #fff;
}
header .headerleft .collups-show-icon {
  cursor: pointer;
}
header .headerleft .collups-show-icon i {
  font-size: 20px;
  color: #2D3B29;
  font-weight: 500;
  display: none;
}
header .headerleft .collups-show-icon.collapsed img {
  display: none;
}
header .headerleft .collups-show-icon.collapsed i {
  display: block;
}

.sidebar_left.collapsed {
  left: -273px;
}

.rcf_dashboard .right-area-body-content.collapsed {
  width: 100%;
  margin-left: 0;
}

.submenu .mm-active a {
  background: var(--color-primary);
  color: #fff !important;
}
.submenu .mm-active a::after {
  background: #fff !important;
}

.submenu .mm-active a {
  background: var(--color-primary);
  color: #fff !important;
}
.submenu .mm-active a::after {
  background: #fff !important;
}

.parent-nav li a.active {
  background: var(--color-primary);
  color: #fff !important;
}
.parent-nav li a.active::after {
  background: #fff !important;
}

.profile-setting-area-main-wrapper {
  padding: 33px;
}
@media only screen and (max-width: 575px) {
  .profile-setting-area-main-wrapper {
    padding: 8px;
  }
}
.profile-setting-area-main-wrapper .title {
  font-size: 20px;
}
.profile-setting-area-main-wrapper .inner-profile-setting {
  padding: 30px;
  border: 1px solid #E2E2E2;
  background: #fff;
  border-radius: 8px;
  display: flex;
  align-items: flex-start;
  gap: 23px;
}
@media only screen and (max-width: 767px) {
  .profile-setting-area-main-wrapper .inner-profile-setting {
    flex-direction: column;
    align-items: flex-start;
  }
}
@media only screen and (max-width: 575px) {
  .profile-setting-area-main-wrapper .inner-profile-setting {
    padding: 15px;
  }
}
.profile-setting-area-main-wrapper .inner-profile-setting .left-setting-area {
  padding: 40px 12px 12px 12px;
  background: #F3F4F6;
  width: 35%;
  text-align: center;
  border-radius: 10px;
}
@media only screen and (max-width: 767px) {
  .profile-setting-area-main-wrapper .inner-profile-setting .left-setting-area {
    width: 100%;
  }
}
.profile-setting-area-main-wrapper .inner-profile-setting .left-setting-area .personal-info .thumbnail-img {
  margin-bottom: 26px;
}
.profile-setting-area-main-wrapper .inner-profile-setting .left-setting-area .personal-info .infor .title {
  margin-bottom: 10px;
  font-size: 20px;
}

.tab-button-area-setting {
  margin-top: 40px;
}
.tab-button-area-setting ul {
  padding: 0;
  list-style: none;
  flex-direction: column;
  margin: 0;
}
.tab-button-area-setting ul li {
  margin: 0;
  display: block;
}
.tab-button-area-setting ul li button {
  display: block;
  height: 60px;
  background: #629D23 !important;
  color: #FFFFFF;
  text-align: left;
  border: none !important;
  padding: 0 24px;
  font-weight: 500;
  color: #fff;
  display: block;
  min-width: max-content;
}
.tab-button-area-setting ul li button.active {
  color: #fff !important;
  background: #6DA432 !important;
}
.tab-button-area-setting ul li button:hover {
  color: #fff !important;
  background: #6DA432 !important;
}
.tab-button-area-setting ul li button img {
  margin-right: 18px;
}

.tab-content-area-user-setting {
  border: 1px solid #E2E2E2;
  width: calc(65% - 30px);
  border-radius: 10px;
  padding: 27px;
}
@media only screen and (max-width: 767px) {
  .tab-content-area-user-setting {
    width: 100%;
  }
}
@media only screen and (max-width: 575px) {
  .tab-content-area-user-setting {
    padding: 10px;
  }
}

.inner-content-setting-form .title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}
.inner-content-setting-form p {
  margin: 0;
  font-size: 14px;
}
.inner-content-setting-form form {
  margin-top: 40px;
}
.inner-content-setting-form form .half-input-wrapper {
  display: flex;
  align-items: center;
  gap: 25px;
  margin-bottom: 45px;
}
@media only screen and (max-width: 575px) {
  .inner-content-setting-form form .half-input-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
}
.inner-content-setting-form form .half-input-wrapper .single {
  flex-basis: 50%;
}
@media only screen and (max-width: 575px) {
  .inner-content-setting-form form .half-input-wrapper .single {
    flex-basis: 100%;
    width: 100%;
  }
}
.inner-content-setting-form form .half-input-wrapper .single label {
  color: #2D3B29;
  font-weight: 500;
  margin-bottom: 10px;
}
.inner-content-setting-form form .half-input-wrapper .single input {
  border: 1px solid #E8E9EB;
  height: 48px;
  border-radius: 4px;
}
.inner-content-setting-form form .half-input-wrapper .single input:focus {
  border: 1px solid var(--color-primary);
}
.inner-content-setting-form form .about-me-area-setting-area label {
  color: #2D3B29;
  font-weight: 500;
  margin-bottom: 10px;
}
.inner-content-setting-form form .about-me-area-setting-area textarea {
  height: 150px;
  border: 1px solid #E8E9EB;
  text-align: left;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}
.inner-content-setting-form form .about-me-area-setting-area textarea:focus {
  border: 1px solid var(--color-primary);
}

form.change-pass-form .single,
.social-media-edit-wrapper .single {
  margin-bottom: 25px;
}
form.change-pass-form .single:last-child,
.social-media-edit-wrapper .single:last-child {
  margin-bottom: 0;
}
form.change-pass-form label,
.social-media-edit-wrapper label {
  color: #2D3B29;
  font-weight: 500;
  margin-bottom: 10px;
}
form.change-pass-form input,
.social-media-edit-wrapper input {
  border: 1px solid #E8E9EB;
  height: 48px;
  border-radius: 4px;
}

.about-me-area-setting-area {
  margin-top: 30px;
}
.about-me-area-setting-area .title {
  font-size: 15px;
  margin-bottom: 15px;
}
.about-me-area-setting-area .inner-border-wrapper {
  display: flex;
  padding: 19px;
  border: 1px solid #E8E9EB;
  margin-bottom: 20px;
  padding-bottom: 30px;
  border-radius: 5px;
}
.about-me-area-setting-area .button-area {
  display: flex;
  align-items: center;
  gap: 15px;
}

.profile-image img {
  border-radius: 5px;
  border: 5px solid var(--color-border);
  height: auto;
  width: max-content;
  object-fit: cover;
  max-width: 250px;
  height: 300px;
}

.brows-file-wrapper {
  position: relative;
  cursor: pointer;
}
.brows-file-wrapper input {
  position: absolute;
  height: 100%;
  width: 100%;
  opacity: 0;
  cursor: pointer;
  background: var(--background-color-4);
  height: 50px;
  border-radius: 5px;
  color: var(--color-white);
  font-size: 14px;
  padding: 10px 20px;
  border: 2px solid var(--color-border);
  transition: 0.3s;
}
.brows-file-wrapper label {
  width: max-content;
  padding: 0 16px;
  height: 45px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: var(--color-primary);
  color: var(--color-white);
  font-weight: 500;
  font-size: 16px;
  transition: 0.3s;
  position: relative;
  z-index: 10;
}

.notification__items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin: 0;
  list-style: none;
}
.notification__items .single__items {
  list-style: none;
  margin: 0;
}
.notification__items .single__items .single-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 10px;
  color: var(--body-color);
  font-size: 14px;
  line-height: 21px;
  font-weight: 600;
  cursor: pointer;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  gap: 15px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--dark-color) !important;
  -webkit-box-align: start !important;
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}
.notification__items .single__items .single-link .avatar {
  width: 40px;
  height: 40px;
  margin-top: 3px;
  border-radius: 11%;
  background-color: #eceaf3;
  border: none;
}
.notification__items .single__items .single-link .main-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 5px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.notification__items .single__items .single-link .main-content .name-user {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  font-size: 15px;
  line-height: 21px;
  font-weight: 500;
  margin-bottom: 0;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
}
.notification__items .single__items .single-link .main-content .name-user .time-ago {
  color: var(--color-primary) !important;
  font-size: 12px;
}
.notification__items .single__items .single-link .main-content .disc {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  font-size: 15px;
  font-weight: 400;
  font-size: 14px;
}
.notification__items .single__items .single-link .main-content .disc .count {
  width: 10px;
  height: 10px;
  background-color: var(--color-primary) !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 100%;
  font-size: 10px;
  color: #fff;
  background: var(--color-danger);
  margin-top: 5px;
  animation: zeroone 1s ease-in-out infinite;
}

@keyframes zeroone {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
    scale: 0.5;
  }
}
.apex-chart-top-area-banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.input-area-search-head-vendor {
  position: relative;
}
.input-area-search-head-vendor input {
  height: 50px;
  border-radius: 5px;
  background: #fff;
  border: 1px solid transparent;
}
.input-area-search-head-vendor input:focus {
  border: 1px solid var(--color-primary);
}
.input-area-search-head-vendor .rts-btn.btn-primary {
  position: absolute;
  right: 0;
  top: 5px;
  right: 5px;
  height: 40px;
}

.body-root-inner {
  padding: 40px 30px;
}
@media only screen and (max-width: 575px) {
  .body-root-inner {
    padding: 10px;
  }
}

.single-vendor-area {
  padding: 40px;
  border: 1px solid #E2E2E2;
  border-radius: 6px;
  height: 100%;
  background: #fff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-vendor-area {
    padding: 20px;
  }
}
@media only screen and (max-width: 767px) {
  .single-vendor-area {
    padding: 20px;
  }
}
@media only screen and (max-width: 575px) {
  .single-vendor-area {
    padding: 25px;
  }
}
.single-vendor-area .logo-vendor {
  max-width: max-content;
  height: auto;
  margin-bottom: 20px;
}
.single-vendor-area .logo-vendor img {
  max-width: max-content;
  height: auto;
}
@media only screen and (max-width: 575px) {
  .single-vendor-area .logo-vendor img {
    max-width: 110px;
  }
}
.single-vendor-area .title {
  font-size: 24px;
  margin-bottom: 15px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-vendor-area .title {
    font-size: 20px;
  }
}
.single-vendor-area .title span {
  padding: 4px 12px;
  background: var(--color-primary);
  font-size: 14px;
  color: #fff;
  border-radius: 2px;
  margin-left: 10px;
  font-weight: 400;
}
.single-vendor-area .title span.closed {
  background: #DC2626;
}
.single-vendor-area .stars-area {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-bottom: 30px;
}
.single-vendor-area .stars-area i {
  color: #FF9A00;
}
.single-vendor-area .stars-area span {
  margin-left: 8px;
  color: #74787C;
  font-weight: 500;
}
.single-vendor-area .location {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 15px;
}
.single-vendor-area .location:last-child {
  margin-bottom: 0;
}
.single-vendor-area .location i {
  font-size: 20px;
}
.single-vendor-area .location p {
  max-width: 70%;
}
@media only screen and (max-width: 575px) {
  .single-vendor-area .location p {
    max-width: 100%;
  }
}
.single-vendor-area a.rts-btn {
  margin-top: 30px;
}

.vendor-list-main-wrapper {
  padding: 30px;
  border-radius: 10px;
  background: #fff;
  border: 1px solid #E2E2E2;
  margin-top: 20px;
}
@media only screen and (max-width: 1199px) {
  .vendor-list-main-wrapper {
    overflow: scroll;
  }
  .vendor-list-main-wrapper .table-responsive {
    width: 1000px;
  }
}
@media only screen and (max-width: 479px) {
  .vendor-list-main-wrapper {
    padding: 6px;
  }
}

.vendor-list-main-wrapper thead {
  border: none;
}
.vendor-list-main-wrapper thead tr {
  border: none;
}
.vendor-list-main-wrapper thead tr th {
  border: none;
  margin-bottom: 20px;
  padding-bottom: 38px;
}
.vendor-list-main-wrapper tbody tr td {
  padding: 20px 0;
}
.vendor-list-main-wrapper tbody tr td img {
  max-width: 54px;
  cursor: pointer;
}
.vendor-list-main-wrapper tbody tr td .itemside {
  display: flex;
  align-items: center;
  gap: 25px;
  text-decoration: none;
}
.vendor-list-main-wrapper tbody tr td .itemside .info .title {
  margin-bottom: 7px;
  text-decoration: none;
}
.vendor-list-main-wrapper tbody tr td .itemside .info .stars-wrapper {
  display: flex;
}
.vendor-list-main-wrapper tbody tr td .itemside .info .stars-wrapper .stars {
  display: flex;
  align-items: center;
  color: #FF9A00;
}
.vendor-list-main-wrapper tbody tr td .itemside .info .stars-wrapper .stars i {
  color: #FF9A00;
}
.vendor-list-main-wrapper tbody tr td .itemside .info .stars-wrapper span {
  margin-left: 10px;
}
.vendor-list-main-wrapper tbody tr td p {
  margin: 0;
}
.vendor-list-main-wrapper tbody tr td .rts-btn {
  text-decoration: none;
}
.vendor-list-main-wrapper tbody tr td .open {
  padding: 5px 10px;
  display: block;
  background: #E0EBD3;
  text-align: center;
  color: var(--color-primary);
  font-weight: 600;
  border-radius: 2px;
}
.vendor-list-main-wrapper tbody tr td .close {
  padding: 5px 10px;
  display: block;
  background: #FAD8CF;
  text-align: center;
  color: #F05C54;
  font-weight: 600;
  border-radius: 2px;
}
.vendor-list-main-wrapper tbody tr:hover {
  --bs-table-accent-bg: transparent;
}

.vendor-list-p .vendor-list-main-wrapper tbody tr td .rts-btn {
  text-decoration: none;
  margin-left: auto;
}

.vendor-banner-left {
  background: #2C3C28;
  border-radius: 6px;
  height: 100%;
  text-align: center;
  padding: 40px 25px;
}
.vendor-banner-left .stars-area {
  display: flex;
  align-items: center;
  color: #fff;
}
.vendor-banner-left .stars-area i {
  color: #FF9A00;
}
.vendor-banner-left .stars-area span {
  margin-left: 10px;
  color: #fff;
}
.vendor-banner-left .location {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-top: 25px;
}
.vendor-banner-left .location i {
  color: #fff;
}
.vendor-banner-left .location p {
  color: #fff;
  text-align: left;
}

.banner-vendor-details {
  background-image: url(../images/vendor/01.webp);
  height: 400px;
  border-radius: 6px;
  position: relative;
  padding: 40px;
}
.banner-vendor-details .content-area .title {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 36px;
}
@media only screen and (max-width: 575px) {
  .banner-vendor-details .content-area .title {
    font-size: 26px;
  }
}
.banner-vendor-details .content-area .title span {
  color: var(--color-primary);
  font-weight: 400;
}

.shop-now-goshop-btn {
  display: flex;
  align-items: center;
  gap: 10px;
}
.shop-now-goshop-btn .plus-icon {
  height: 30px;
  width: 30px;
  background: var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.shop-now-goshop-btn .plus-icon i {
  color: #fff;
}
.shop-now-goshop-btn span {
  font-weight: 700;
  color: #232722;
}

.shop-now-goshop-btn {
  max-width: max-content;
}
.shop-now-goshop-btn .plus-icon {
  opacity: 0;
  display: flex;
  transition: opacity 0.4s 0.25s, transform 0.6s 0.25s;
  transition-timing-function: cubic-bezier(0.1, 0.75, 0.25, 1);
}
.shop-now-goshop-btn .plus-icon + .plus-icon {
  margin-inline-end: 0;
  margin-inline-start: 8px;
  display: flex;
  margin-inline-start: 0;
  margin-inline-end: 0;
  opacity: 1;
  transform: translateX(0px);
  transition-delay: 0s;
  order: -2;
}
.shop-now-goshop-btn .text {
  display: flex;
  transition: transform 0.6s 0.125s cubic-bezier(0.1, 0.75, 0.25, 1);
  margin-inline-start: -1px;
}
.shop-now-goshop-btn:hover .text {
  transition-delay: 0s;
  transform: translateX(-33px);
}
.shop-now-goshop-btn:hover .plus-icon + .plus-icon {
  opacity: 0;
  transform: translateX(-30px);
  transition-delay: 0s;
}
.shop-now-goshop-btn:hover .plus-icon {
  opacity: 1;
  transition-delay: 0s;
  transform: translateX(-30px);
}

.product-area-add-wrapper {
  padding: 30px 0;
  position: relative;
  border-radius: 6px;
  margin-top: 30px;
  background-image: url(../images/vendor/02.webp);
}
.product-area-add-wrapper .title {
  margin-left: -50px;
  color: #fff;
  text-align: center;
  font-size: 48px;
}
.product-area-add-wrapper .one {
  position: absolute;
  right: 50px;
  bottom: 0;
  height: 100%;
}
@media only screen and (max-width: 1199px) {
  .product-area-add-wrapper .one {
    max-width: 29%;
    height: auto;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-area-add-wrapper .one {
    display: none;
  }
}
@media only screen and (max-width: 767px) {
  .product-area-add-wrapper .one {
    display: none;
  }
}
.product-area-add-wrapper .two {
  position: absolute;
  left: 10%;
  bottom: 0;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .product-area-add-wrapper .two {
    display: none;
  }
}
@media only screen and (max-width: 767px) {
  .product-area-add-wrapper .two {
    display: none;
  }
}

.order-details-table-1-table {
  padding: 30px;
  padding-bottom: 30px;
  border: 1px solid #E2E2E2;
  margin-top: 30px;
  background: #fff;
  border-radius: 10px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .order-details-table-1-table {
    overflow: auto;
  }
}
@media only screen and (max-width: 767px) {
  .order-details-table-1-table {
    overflow: auto;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .order-details-table-1-table table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    width: 1100px;
  }
}
@media only screen and (max-width: 767px) {
  .order-details-table-1-table table {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    width: 1100px;
  }
}
.order-details-table-1-table .order-details-table thead {
  border: none;
}
.order-details-table-1-table .order-details-table thead tr {
  border: none;
  border-bottom: 1px solid #E8E9EB;
}
.order-details-table-1-table .order-details-table thead tr th {
  border: none;
  margin-bottom: 15px;
  padding-bottom: 15px;
}
.order-details-table-1-table .order-details-table tr {
  border-bottom: 1px solid #E8E9EB;
}
.order-details-table-1-table .order-details-table tr.b-n {
  border: none;
}
.order-details-table-1-table .order-details-table tr td {
  padding: 10px;
}
.order-details-table-1-table .order-details-table tr td .item {
  display: flex;
  align-items: center;
  gap: 15px;
}
.order-details-table-1-table .order-details-table tr td .item .discription .title {
  margin-bottom: 2px;
}

@media only screen and (max-width: 1199px) {
  .card-body.table-product-select {
    overflow: auto;
  }
  .card-body.table-product-select table {
    width: 900px;
  }
}

@media screen and (max-width: 1250px) {
  .top-product-wrapper-scroll {
    overflow: auto;
  }
  .top-product-wrapper-scroll .top-product-area-start {
    width: 900px;
  }
}
@media screen and (max-width: 750px) {
  .top-product-wrapper-scroll {
    overflow: auto;
  }
  .top-product-wrapper-scroll .top-product-area-start {
    width: 800px;
  }
}

@media screen and (max-width: 1250px) {
  .rop-product-right {
    overflow: auto;
  }
  .rop-product-right .top-product-area-start {
    width: 500px;
  }
}
@media screen and (max-width: 1200px) {
  .rop-product-right {
    overflow: auto;
  }
  .rop-product-right .top-product-area-start {
    width: 100%;
  }
}

@media screen and (max-width: 1250px) {
  .best-shop-seller-top-scroll {
    overflow: auto;
  }
  .best-shop-seller-top-scroll .top-product-area-start {
    width: 800px;
  }
}
@media screen and (max-width: 1200px) {
  .best-shop-seller-top-scroll {
    overflow: auto;
    width: 100%;
  }
  .best-shop-seller-top-scroll .top-product-area-start {
    width: 100%;
  }
}
@media screen and (max-width: 991px) {
  .best-shop-seller-top-scroll {
    overflow: auto;
  }
  .best-shop-seller-top-scroll .top-product-area-start {
    width: 100%;
  }
}
@media screen and (max-width: 691px) {
  .best-shop-seller-top-scroll {
    overflow: auto;
  }
  .best-shop-seller-top-scroll .top-product-area-start {
    width: 550px !important;
  }
}

.vendor-list-main-wrapper tbody tr td:nth-child(2) {
  width: 30%;
}

#example_wrapper .dataTables_length {
  padding: 20px;
}
#example_wrapper .dataTables_length label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 500;
}
#example_wrapper #example_filter {
  padding: 20px;
}
#example_wrapper #example_filter label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
  font-size: 18px;
}
#example_wrapper #example_filter input {
  height: 35px;
  border: 1px solid #f1f1f1;
}
#example_wrapper #example_info {
  padding: 20px;
}
#example_wrapper #example_paginate {
  padding: 20px;
}
#example_wrapper .paginate_button.current {
  background: var(--color-primary) !important;
  color: #fff !important;
  border: 1px solid var(--color-primary) !important;
}
#example_wrapper .paginate_button:hover {
  background: var(--color-primary) !important;
  color: #fff !important;
  border: 1px solid var(--color-primary) !important;
}
#example_wrapper .paginate_button {
  background: #fff !important;
  color: var(--color-primary);
  border: 1px solid #fff !important;
  border: 1px solid #f1f1f1 !important;
}

table.dataTable.no-footer {
  border-bottom: 1px solid #f1f1f1;
}

.transiction-filter thead tr th {
  padding: 0 !important;
}

.table-transixtion .dataTables_length {
  padding: 20px 0 !important;
}
.table-transixtion #example_wrapper #example_info {
  padding: 20px 0 !important;
}
.table-transixtion #example_wrapper #example_paginate {
  padding: 20px 0 !important;
}

table.dataTable thead .sorting {
  position: relative;
  background-image: none;
  max-width: max-content;
}
table.dataTable thead .sorting::after {
  content: "";
  right: auto;
  position: absolute;
  background-image: url(../images/form/01.png);
  height: 19px;
  width: 19px;
  background-repeat: no-repeat;
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(132deg) brightness(103%) contrast(103%);
}

table.dataTable thead .sorting_asc {
  position: relative;
  background-image: none;
  max-width: max-content;
}
table.dataTable thead .sorting_asc::after {
  content: "";
  right: auto;
  position: absolute;
  background-image: url(../images/form/01.png);
  height: 19px;
  width: 19px;
  background-repeat: no-repeat;
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(132deg) brightness(103%) contrast(103%);
}

table.dataTable thead .sorting_asc {
  background-image: none !important;
}

table.dataTable thead .sorting_desc {
  background-image: none !important;
  position: relative;
  background-image: none;
  max-width: max-content;
}
table.dataTable thead .sorting_desc::after {
  content: "";
  right: 0;
  position: absolute;
  background-image: url(../images/form/02.png);
  height: 19px;
  width: 19px;
  background-repeat: no-repeat;
  filter: brightness(0) saturate(100%) invert(100%) sepia(100%) saturate(0%) hue-rotate(132deg) brightness(103%) contrast(103%);
}

.order-page .vendor-list-main-wrapper tbody tr td:nth-child(2) {
  width: 17%;
}

.between-stock-table.statrusts {
  position: relative;
}
.between-stock-table.statrusts .action-edit-deleate {
  position: absolute;
  top: 52%;
  right: 24px;
  border: 1px solid #f1f1f1;
  z-index: 10;
  display: none;
  border-radius: 5px;
}
.between-stock-table.statrusts .action-edit-deleate span {
  display: block;
  max-width: 100%;
  padding: 17px 25px;
  background: #fff;
  border-bottom: 1px solid #f1f1f1;
  font-weight: 500;
  color: #74787C;
  cursor: pointer;
  transition: 0.3s;
}
.between-stock-table.statrusts .action-edit-deleate span:last-child {
  border: none;
}
.between-stock-table.statrusts .action-edit-deleate span:hover {
  background: var(--color-primary);
  color: #fff;
}

.between-stock-table.action {
  position: relative;
}
.between-stock-table.action .action-edit-deleate {
  position: absolute;
  top: 52%;
  right: 24px;
  border: 1px solid #f1f1f1;
  z-index: 10;
  display: none;
  border-radius: 5px;
}
.between-stock-table.action .action-edit-deleate span {
  display: block;
  max-width: 100%;
  padding: 17px;
  background: #fff;
  border-bottom: 1px solid #f1f1f1;
  font-weight: 500;
  color: #74787C;
  cursor: pointer;
  transition: 0.3s;
}
.between-stock-table.action .action-edit-deleate span:last-child {
  border: none;
}
.between-stock-table.action .action-edit-deleate span:hover {
  background: var(--color-primary);
  color: #fff;
}

.p-d-page .vendor-list-main-wrapper tbody tr td:nth-child(2) {
  width: 15%;
}

.rts-btn {
  max-width: max-content;
  padding: 14px 25px;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
}
.rts-btn.btn-primary {
  background: #629D23;
  border-radius: 6px;
  display: block;
  max-width: max-content;
}
.rts-btn.radious-sm {
  border-radius: 6px;
}
.rts-btn.with-icon {
  display: flex;
  align-items: center;
  gap: 10px;
}
.rts-btn.with-icon .arrow-icon {
  display: inline-block;
  transition: opacity 0.4s 0.25s, transform 0.6s 0.25s;
  transition-timing-function: cubic-bezier(0.1, 0.75, 0.25, 1);
}
.rts-btn.with-icon .arrow-icon + .arrow-icon {
  margin-inline-end: 0;
  margin-inline-start: 8px;
  display: inline-block;
  margin-inline-start: 0;
  margin-inline-end: 0;
  opacity: 0;
  transform: translateX(-10px);
  transition-delay: 0s;
  order: -2;
}
.rts-btn.with-icon .btn-text {
  display: inline-block;
  transition: transform 0.6s 0.125s cubic-bezier(0.1, 0.75, 0.25, 1);
  margin-inline-start: -23px;
}
.rts-btn.with-icon:hover .btn-text {
  transition-delay: 0.1s;
  transform: translateX(23px);
}
.rts-btn.with-icon:hover .arrow-icon + .arrow-icon {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.225s;
}
.rts-btn.with-icon:hover .arrow-icon {
  opacity: 0;
  transition-delay: 0s;
  transform: translateX(10px);
}

.nice-select {
  -webkit-tap-highlight-color: transparent;
  background-color: #fff;
  border-radius: 5px;
  border: solid 1px #e8e8e8;
  box-sizing: border-box;
  clear: both;
  cursor: pointer;
  display: block;
  float: left;
  font-family: inherit;
  font-size: 14px;
  font-weight: normal;
  height: 42px;
  line-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 30px;
  position: relative;
  text-align: left !important;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: auto;
}

.nice-select:hover {
  border-color: #dbdbdb;
}

.nice-select:active, .nice-select.open, .nice-select:focus {
  border-color: #999;
}

.nice-select:after {
  border-bottom: 2px solid #999;
  border-right: 2px solid #999;
  content: "";
  display: block;
  height: 5px;
  margin-top: -4px;
  pointer-events: none;
  position: absolute;
  right: 12px;
  top: 50%;
  -webkit-transform-origin: 66% 66%;
  -ms-transform-origin: 66% 66%;
  transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  width: 5px;
}

.nice-select.open:after {
  -webkit-transform: rotate(-135deg);
  -ms-transform: rotate(-135deg);
  transform: rotate(-135deg);
}

.nice-select.open .list {
  opacity: 1;
  pointer-events: auto;
  -webkit-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
}

.nice-select.disabled {
  border-color: #ededed;
  color: #999;
  pointer-events: none;
}

.nice-select.disabled:after {
  border-color: #cccccc;
}

.nice-select.wide {
  width: 100%;
}

.nice-select.wide .list {
  left: 0 !important;
  right: 0 !important;
}

.nice-select.right {
  float: right;
}

.nice-select.right .list {
  left: auto;
  right: 0;
}

.nice-select.small {
  font-size: 12px;
  height: 36px;
  line-height: 34px;
}

.nice-select.small:after {
  height: 4px;
  width: 4px;
}

.nice-select.small .option {
  line-height: 34px;
  min-height: 34px;
}

.nice-select .list {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
  box-sizing: border-box;
  margin-top: 4px;
  opacity: 0;
  overflow: hidden;
  padding: 0;
  pointer-events: none;
  position: absolute;
  top: 100%;
  right: 0;
  -webkit-transform-origin: 50% 0;
  -ms-transform-origin: 50% 0;
  transform-origin: 50% 0;
  -webkit-transform: scale(0.75) translateY(-21px);
  -ms-transform: scale(0.75) translateY(-21px);
  transform: scale(0.75) translateY(-21px);
  -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
  z-index: 69;
}

.nice-select .list:hover .option:not(:hover) {
  background-color: transparent !important;
}

.nice-select .option {
  cursor: pointer;
  font-weight: 400;
  line-height: 40px;
  list-style: none;
  min-height: 40px;
  outline: none;
  padding-left: 18px;
  padding-right: 29px;
  text-align: left;
  -webkit-transition: all 0.2s;
  transition: all 0.2s;
}

.nice-select .option:hover, .nice-select .option.focus, .nice-select .option.selected.focus {
  background-color: #f6f6f6;
}

.nice-select .option.selected {
  font-weight: bold;
}

.nice-select .option.disabled {
  background-color: transparent;
  color: #999;
  cursor: default;
}

.no-csspointerevents .nice-select .list {
  display: none;
}

.no-csspointerevents .nice-select.open .list {
  display: block;
}

.input-between-search-transiction {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.input-between-search-transiction input {
  height: 50px;
  border-radius: 4px;
  border: 1px solid #E2E2E2;
  width: 362px;
  padding: 15px;
  display: flex;
  align-items: center;
}

.table-transixtion th {
  border: none;
  margin-bottom: 20px;
  padding-bottom: 20px !important;
}
.table-transixtion tbody .payment {
  gap: 10px;
}
.table-transixtion tbody .id {
  color: #74787C;
  font-weight: 500;
}
.table-transixtion tbody td p {
  font-weight: 500;
}
.table-transixtion tbody td .btn-primary {
  padding: 8px 12px;
  border-radius: 4px;
  transition: 0.3s;
  border: 1px solid transparent;
}
.table-transixtion tbody td .btn-primary:hover {
  background: transparent;
  border: 1px solid var(--color-primary);
  color: #629D23;
}

.button-wrapper-reviews {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 0;
  justify-content: flex-end;
}
.button-wrapper-reviews .rts-btn {
  margin-left: 0 !important;
}

.ratings-area-reviews-tablwe {
  display: flex;
  align-items: center;
  gap: 5px;
}
.ratings-area-reviews-tablwe i {
  color: #FF9A00;
}

.title-right-actioin-btn-wrapper-product-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title-right-actioin-btn-wrapper-product-list .button-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
}
.title-right-actioin-btn-wrapper-product-list .button-wrapper button {
  height: 37px;
  display: flex;
  align-items: center;
}
.title-right-actioin-btn-wrapper-product-list .button-wrapper .nice-select {
  height: 37px;
  display: flex;
  align-items: center;
}

.product-top-filter-area-l {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 35px;
}
@media only screen and (max-width: 575px) {
  .product-top-filter-area-l {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
.product-top-filter-area-l .left-area-button-fiulter {
  display: flex;
  align-items: center;
  gap: 9px;
}
.product-top-filter-area-l .left-area-button-fiulter .signle-product-single-button span {
  display: block;
  padding: 5px 10px;
  border: 1px solid #C6C9CC;
  color: #595F69;
  font-weight: 500;
  border-radius: 5px;
  cursor: pointer;
  transition: 0.3s;
}
.product-top-filter-area-l .left-area-button-fiulter .signle-product-single-button span:hover {
  background: var(--color-primary);
  border: 1px solid var(--color-primary);
  color: #fff;
}

.right-filter-upload-area svg {
  cursor: pointer;
}

.single-shopping-card-one.tranding-product {
  display: flex;
  align-items: flex-start;
  gap: 18px;
  background: #FFFFFF;
  margin-bottom: 15px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-shopping-card-one.tranding-product {
    flex-direction: column;
    align-items: flex-start;
  }
}
@media only screen and (max-width: 1199px) {
  .single-shopping-card-one.tranding-product {
    flex-direction: column;
    align-items: flex-start;
  }
}
@media only screen and (max-width: 575px) {
  .single-shopping-card-one.tranding-product {
    flex-direction: column;
  }
}
.single-shopping-card-one.tranding-product:last-child {
  margin-bottom: 0;
}
.single-shopping-card-one.tranding-product .thumbnail-preview {
  border: 1px solid #EAEAEA;
  height: 130px;
  min-width: 130px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-shopping-card-one.tranding-product .thumbnail-preview {
    height: auto;
    width: 100%;
  }
}
@media only screen and (max-width: 1199px) {
  .single-shopping-card-one.tranding-product .thumbnail-preview {
    height: auto;
    width: 100%;
  }
}
@media only screen and (max-width: 575px) {
  .single-shopping-card-one.tranding-product .thumbnail-preview {
    width: 100%;
    height: auto;
  }
}
.single-shopping-card-one.tranding-product .thumbnail-preview img {
  height: 130px;
  width: 130px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-shopping-card-one.tranding-product .thumbnail-preview img {
    height: auto;
    width: 100%;
  }
}
@media only screen and (max-width: 1199px) {
  .single-shopping-card-one.tranding-product .thumbnail-preview img {
    height: auto;
    width: 100%;
  }
}
@media only screen and (max-width: 575px) {
  .single-shopping-card-one.tranding-product .thumbnail-preview img {
    width: 100%;
    height: auto;
  }
}
.single-shopping-card-one.tranding-product .thumbnail-preview .badge {
  left: 7px;
}
.single-shopping-card-one.tranding-product .thumbnail-preview .badge i {
  font-size: 48px;
}
.single-shopping-card-one.tranding-product .body-content {
  padding-top: 0;
}
.single-shopping-card-one.tranding-product .time-tag {
  margin-bottom: 9px;
}
.single-shopping-card-one.tranding-product .price-area {
  margin-top: 8px;
}
.single-shopping-card-one.tranding-product .body-content a .title {
  margin-bottom: 5px;
}
.single-shopping-card-one.tranding-product .cart-counter-action {
  margin-top: 10px;
}

.single-shopping-card-one {
  padding: 15px;
  background: #F5F6F7;
  border-radius: 6px;
}
.single-shopping-card-one .image-and-action-area-wrapper {
  position: relative;
}
.single-shopping-card-one .image-and-action-area-wrapper .action-share-option {
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%) rotateX(-90deg);
  display: flex;
  align-items: center;
  gap: 8px;
  height: 48px;
  border-radius: 10px 10px 0 0;
  background: var(--color-primary);
  padding: 10px 29px;
  transform-origin: bottom;
  transition: 0.4s cubic-bezier(0.375, 1.185, 0.92, 0.975);
}
.single-shopping-card-one .image-and-action-area-wrapper .action-share-option .single-action {
  height: 28px;
  width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1.2px dashed rgba(255, 255, 255, 0.42);
  border-radius: 50%;
  transition: all 0.3s;
}
.single-shopping-card-one .image-and-action-area-wrapper .action-share-option .single-action i {
  color: #fff;
  transition: all 0.3s;
}
.single-shopping-card-one .image-and-action-area-wrapper .action-share-option .single-action:hover {
  background: #fff;
}
.single-shopping-card-one .image-and-action-area-wrapper .action-share-option .single-action:hover i {
  color: var(--color-primary);
  animation: 0.5s mymove;
}
.single-shopping-card-one .thumbnail-preview {
  border-radius: 6px;
  overflow: hidden;
  display: block;
  position: relative;
}
.single-shopping-card-one .thumbnail-preview .badge {
  position: absolute;
  left: 30px;
  top: -10px;
  z-index: 5;
}
.single-shopping-card-one .thumbnail-preview .badge i {
  color: #EABC5E;
  font-size: 50px;
}
.single-shopping-card-one .thumbnail-preview .badge span {
  position: absolute;
  top: 17px;
  left: 17px;
  font-size: 11px;
  line-height: 1.1;
  color: #2C3C28;
  text-align: left;
  font-weight: 700;
}
.single-shopping-card-one .thumbnail-preview img {
  width: 100%;
  transition: 0.3s;
  transform: scale(1.01);
}
.single-shopping-card-one .thumbnail-preview .action-share-option {
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%) rotateX(-90deg);
  display: flex;
  align-items: center;
  gap: 8px;
  height: 48px;
  border-radius: 10px 10px 0 0;
  background: var(--color-primary);
  padding: 10px 29px;
  transform-origin: bottom;
  transition: 0.4s cubic-bezier(0.375, 1.185, 0.92, 0.975);
}
.single-shopping-card-one .thumbnail-preview .action-share-option .single-action {
  height: 28px;
  width: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1.2px dashed rgba(255, 255, 255, 0.42);
  border-radius: 50%;
  transition: all 0.3s;
}
.single-shopping-card-one .thumbnail-preview .action-share-option .single-action i {
  color: #fff;
  transition: all 0.3s;
}
.single-shopping-card-one .thumbnail-preview .action-share-option .single-action:hover {
  background: #fff;
}
.single-shopping-card-one .thumbnail-preview .action-share-option .single-action:hover i {
  color: var(--color-primary);
  animation: 0.5s mymove;
}
.single-shopping-card-one .body-content {
  padding-top: 15px;
}
.single-shopping-card-one .body-content .time-tag {
  padding: 3px 8px;
  background: #FFFFFF;
  border: 1px solid rgba(43, 66, 38, 0.12);
  box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  max-width: max-content;
  font-size: 10px;
  color: #2C3C28;
  font-weight: 600;
  margin-bottom: 15px;
}
.single-shopping-card-one .body-content .time-tag i {
  color: #2C3C28;
  font-weight: 500;
}
.single-shopping-card-one .body-content a .title {
  transition: 0.3s;
  font-size: 16px;
  margin-bottom: 10px;
}
.single-shopping-card-one .body-content a:hover .title {
  color: var(--color-primary);
}
.single-shopping-card-one .body-content .availability {
  font-size: 14px;
  font-weight: 400;
}
.single-shopping-card-one .body-content .price-area {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}
.single-shopping-card-one .body-content .price-area .current {
  font-weight: 700;
  color: var(--color-danger);
  font-size: 20px;
  margin-bottom: 0;
}
.single-shopping-card-one .body-content .price-area .previous {
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 500;
  color: #74787C;
  position: relative;
}
.single-shopping-card-one .body-content .price-area .previous::after {
  position: absolute;
  overflow: auto;
  left: -5%;
  top: 50%;
  content: "";
  height: 1px;
  width: 110%;
  background: #74787C;
}
.single-shopping-card-one .cart-counter-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 7px;
  margin-top: 10px;
  flex-wrap: wrap;
}
.single-shopping-card-one .cart-counter-action .quantity-edit {
  width: 92px;
  display: flex;
  align-items: center;
  border: 1px solid rgba(43, 66, 38, 0.12);
  border-radius: 4px;
  padding: 2px 10px;
  justify-content: space-between;
  background: #fff;
  box-shadow: 0px 4px 17px rgba(0, 0, 0, 0.04);
}
.single-shopping-card-one .cart-counter-action .quantity-edit .button-wrapper-action {
  border: 1px solid rgba(43, 66, 38, 0.12);
  border-radius: 2px;
  background: #fff;
  display: flex;
}
.single-shopping-card-one .cart-counter-action .quantity-edit input {
  padding: 0;
  max-width: 10px;
  font-weight: 600;
}
.single-shopping-card-one .cart-counter-action .quantity-edit button {
  padding: 0;
  max-width: max-content;
  font-size: 0;
}
.single-shopping-card-one .cart-counter-action .quantity-edit button i {
  font-size: 10px;
  padding: 4px 6px;
  transition: 0.3s;
}
.single-shopping-card-one .cart-counter-action .quantity-edit button:first-child i {
  border-right: 1px solid rgba(43, 66, 38, 0.12);
}
.single-shopping-card-one .cart-counter-action .quantity-edit button:hover i {
  background: var(--color-primary);
  color: #fff;
}
.single-shopping-card-one .cart-counter-action .rts-btn {
  font-size: 14px;
  padding: 8px 16px !important;
  background: transparent;
  color: #629D23;
  border: 1px solid #629D23;
}
@media only screen and (min-width: 1600px) and (max-width: 1919px) {
  .single-shopping-card-one .cart-counter-action .rts-btn {
    padding: 9px 10px !important;
    font-size: 11px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-shopping-card-one .cart-counter-action .rts-btn {
    padding: 10px 7px !important;
    font-size: 10px;
  }
}
.single-shopping-card-one .cart-counter-action .rts-btn i {
  transition: transform 0.6s 0.125s cubic-bezier(0.1, 0.75, 0.25, 1);
}
.single-shopping-card-one .cart-counter-action .rts-btn:hover {
  background: var(--color-primary);
  color: #fff;
}
.single-shopping-card-one:hover .thumbnail-preview img {
  transform: scale(1.1);
}
.single-shopping-card-one:hover .action-share-option {
  bottom: 0;
  transform: translateX(-50%) rotateX(0deg);
}
.single-shopping-card-one.deals-of-day {
  background: #fff;
  border: 1px solid #E2E2E2;
  overflow: hidden;
  position: relative;
}
.single-shopping-card-one.deals-of-day .onsale-offer span {
  display: block;
  position: absolute;
  padding: 5px 15px;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  background: var(--color-primary);
  border-radius: 6px 0 6px 0;
  z-index: 10;
  left: 0;
  top: 0;
  letter-spacing: 1px;
  text-transform: uppercase;
}
.single-shopping-card-one.deals-of-day:hover .thumbnail-preview img {
  transform: scale(1.2);
}
.single-shopping-card-one.deals-of-day .start-area-rating {
  margin-bottom: 10px;
}
.single-shopping-card-one.deals-of-day .start-area-rating i {
  color: #FF9A00;
}
.single-shopping-card-one.deals-of-day .thumbnail-preview {
  border: none !important;
}
.single-shopping-card-one.deals-of-day .thumbnail-preview img {
  width: 100%;
  transform: scale(1.01);
}
.single-shopping-card-one.deals-of-day .cart-counter-action .rts-btn {
  background: #629d23;
  color: #629D23;
  border: 1px solid #629D23;
  width: 100%;
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.single-shopping-card-one.deals-of-day .cart-counter-action .rts-btn .btn-text {
  color: #fff;
}
.single-shopping-card-one.deals-of-day .cart-counter-action .rts-btn .arrow-icon i {
  color: #fff;
}

.product-filter-area-vendors-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  .product-filter-area-vendors-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
.product-filter-area-vendors-details form {
  position: relative;
}
.product-filter-area-vendors-details form input {
  height: 50px;
  border: 1px solid #D9D9D9;
  border-radius: 5px;
  width: 470px;
}
@media only screen and (max-width: 767px) {
  .product-filter-area-vendors-details form input {
    width: 320px;
  }
}
@media only screen and (max-width: 575px) {
  .product-filter-area-vendors-details form input {
    width: 290px;
  }
}
.product-filter-area-vendors-details form a {
  position: absolute;
  right: 5px;
  height: 40px;
  top: 50%;
  transform: translateY(-50%);
}

.vendor-list-main-wrapper.product-wrapper {
  padding: 0;
}
.vendor-list-main-wrapper.product-wrapper .card-body {
  padding: 0;
}
.vendor-list-main-wrapper.product-wrapper .card-body thead {
  border-radius: 5px 5px 0 0;
  overflow: hidden;
}
.vendor-list-main-wrapper.product-wrapper .card-body thead tr {
  background: var(--color-primary);
  border-radius: 5px 5px 0 0;
}
.vendor-list-main-wrapper.product-wrapper .card-body thead tr th {
  padding: 14px 20px;
  color: #fff;
}
.vendor-list-main-wrapper.product-wrapper .card-body tbody tr td {
  padding: 20px;
  line-height: 0;
}

.item-check-area-table-left {
  display: flex;
  align-items: center;
  gap: 28px;
}
.item-check-area-table-left .form-check input {
  height: 28px !important;
  width: 28px;
}
.item-check-area-table-left .item-image-and-name {
  display: flex;
  align-items: center;
  gap: 19px;
}
.item-check-area-table-left .item-image-and-name .thumbnail {
  min-width: max-content;
}
.item-check-area-table-left .item-image-and-name .thumbnail img {
  max-width: max-content;
  min-width: max-content;
}
.item-check-area-table-left .item-image-and-name p {
  color: #2D3B29;
  margin-bottom: 0;
}
.item-check-area-table-left p {
  margin-bottom: 0;
}

.between-stock-table {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.apexcharts-tooltip.apexcharts-theme-light {
  border: 1px solid #e3e3e3;
  background: rgba(255, 255, 255, 0.96);
}

.apexcharts-tooltip {
  border-radius: 5px;
  box-shadow: 2px 2px 6px -4px #999;
  cursor: default;
  font-size: 14px;
  left: 62px;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  top: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  white-space: nowrap;
  z-index: 12;
  transition: 0.15s ease all;
}

.apexcharts-tooltip {
  border-color: transparent;
  border: none !important;
  overflow: visible !important;
  border-radius: 10px !important;
}

.apexcharts-tooltip .custom-tooltip {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 5px;
  padding: 15px 18px;
  border-radius: 10px;
  background-color: #fff;
  border: none !important;
  overflow: visible;
  -webkit-box-shadow: 0 18px 16px rgba(0, 0, 0, 0.06);
  box-shadow: 0 18px 16px rgba(0, 0, 0, 0.06);
}

.apexcharts-tooltip .custom-tooltip__title {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  margin-bottom: 0;
  color: var(--body-color);
}

.apexcharts-tooltip .custom-tooltip__subtitle {
  font-size: 10px;
  line-height: 15px;
  font-weight: 400;
  margin-bottom: 0;
  color: var(--success-color);
}

.apex-xhart-area-one {
  padding: 25px;
  background: #fff;
  border-radius: 10px;
}
@media only screen and (max-width: 575px) {
  .apex-xhart-area-one {
    padding: 10px;
  }
}

.top-product-area-start {
  padding: 30px;
  border-radius: 10px;
  background: #fff;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .top-product-area-start {
    padding: 20px;
  }
}
@media only screen and (max-width: 1199px) {
  .top-product-area-start {
    padding: 20px;
  }
}
.top-product-area-start .between-area-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .top-product-area-start .between-area-top {
    flex-wrap: wrap;
    gap: 15px;
  }
}
@media only screen and (max-width: 1199px) {
  .top-product-area-start .between-area-top {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}
.top-product-area-start .between-area-top .title {
  font-size: 20px;
  margin-bottom: 10px;
}

.product-top-area-single {
  display: flex;
  align-items: center;
  margin: 20px 0;
  gap: 10px;
}
.product-top-area-single .image-area {
  display: flex;
  align-items: center;
  gap: 14px;
  flex-basis: 40%;
}
.product-top-area-single .image-area .thumbnail {
  max-width: 60px;
}
.product-top-area-single .image-area .thumbnail img {
  max-width: 60px;
  border: 1px solid #F2F3F5;
  border-radius: 5px;
}
.product-top-area-single .image-area .information p {
  margin-bottom: 10px;
  font-weight: 600;
  color: #2C3C28;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .product-top-area-single .image-area .information p {
    font-size: 14px;
  }
}
@media only screen and (max-width: 1199px) {
  .product-top-area-single .image-area .information p {
    font-size: 14px;
  }
}
.product-top-area-single .image-area .information span {
  min-width: max-content;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .product-top-area-single .image-area .information span {
    font-size: 13px;
  }
}
@media only screen and (max-width: 1199px) {
  .product-top-area-single .image-area .information span {
    font-size: 14px;
  }
}
.product-top-area-single > div p {
  margin-bottom: 10px;
  font-weight: 600;
  color: #2C3C28;
  min-width: max-content;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .product-top-area-single > div p {
    font-size: 14px;
  }
}
@media only screen and (max-width: 1199px) {
  .product-top-area-single > div p {
    font-size: 14px;
  }
}
.product-top-area-single .coupon-code,
.product-top-area-single .logo,
.product-top-area-single .indec {
  flex-basis: 20%;
  display: flex;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .product-top-area-single .coupon-code,
  .product-top-area-single .logo,
  .product-top-area-single .indec {
    margin-left: 7px;
  }
}
.product-top-area-single.bottom .image-area {
  flex-basis: 50%;
}
.product-top-area-single.bottom .coupon-code,
.product-top-area-single.bottom .logo,
.product-top-area-single.bottom .indec {
  flex-basis: 25%;
  display: flex;
}
.product-top-area-single .logo {
  display: flex;
  justify-content: center;
}
.product-top-area-single .indec {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  justify-content: flex-end;
  margin-right: 40px;
}

.sale-statictics-button ul {
  list-style: none;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
}
.sale-statictics-button li button {
  border-radius: 6px !important;
  padding: 5px 15px;
  border: 1px solid #C6C9CC !important;
  color: #595F69;
  font-weight: 500;
}
.sale-statictics-button li button.active {
  background: var(--color-primary) !important;
  color: #fff !important;
}

.apex-chart-top-area-banner .title-top {
  font-size: 20px;
}

.add-product-page {
  padding: 30px !important;
}

.cart-bar {
  position: fixed;
  top: 0;
  right: 0;
  background: #fff;
  width: 360px;
  height: 100%;
  padding: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  visibility: hidden;
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
  -webkit-transition: all 500ms ease;
  transition: all 500ms ease;
  z-index: 1000;
}

.cart-bar.show {
  visibility: visible;
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

.cart-bar .cart-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #E7E7E7;
}

.cart-bar .cart-header .cart-heading {
  font-size: 17px;
  font-weight: 600;
  color: #000000;
  font-family: var(--font-secondary);
}

.close-cart {
  cursor: pointer;
  margin-top: -11px;
}
.close-cart i {
  transition: 0.3s;
}
.close-cart:hover i {
  transform: scale(1.3);
  color: var(--color-primary);
}

.cart-bar .product-item {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #E7E7E7;
}

.product-item {
  margin-bottom: 65px;
  position: relative;
  overflow: hidden;
}

.cart-bar .product-item:last-child {
  margin-bottom: 0;
}

.cart-bar .product-detail {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.cart-bar .cart-edit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.cart-edit {
  margin-right: 20px;
}

.cart-bar .product-detail .product-thumb {
  margin-right: 15px;
  max-width: 75px;
}

.cart-bar .cart-edit .quantity-edit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border: 1px solid #E7E7E7;
  padding: 3px 10px;
  border-radius: 5px;
  margin-bottom: 3px;
}

.cart-edit .quantity-edit {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 3px 10px;
  border-radius: 5px;
}

.cart-bar .cart-edit .item-wrapper {
  display: inline-block;
}

.cart-bar .cart-edit .quantity-edit button {
  background: none;
  font-size: 0;
}

.cart-bar .cart-edit .quantity-edit button i {
  font-size: 14px;
  color: #C0C0C0;
}

.cart-edit .quantity-edit button i {
  font-size: 16px;
  color: #d3d3d3;
}

edit .quantity-edit button {
  background: none;
  font-size: 0;
}

.cart-edit .quantity-edit button {
  background: none;
  font-size: 0;
}

.cart-bar .cart-edit .quantity-edit button i {
  font-size: 14px;
  color: #000000;
}
.cart-bar .cart-edit .quantity-edit button i:hover {
  color: var(--color-primary);
}

.cart-edit .quantity-edit button i {
  font-size: 16px;
  color: #d3d3d3;
}

.cart-bar .cart-edit .quantity-edit input {
  text-align: center;
  max-width: 26px;
  padding: 0;
}

.cart-edit .quantity-edit input {
  text-align: center;
  max-width: 55px;
  font-size: 16px;
  font-weight: 700;
  color: #040404;
}

.cart-bar .cart-edit .product-edit {
  margin-right: 15px;
}

.cart-bar .product-detail .product-name {
  font-size: 14px;
  font-weight: 400;
  color: var(--color-primary);
}

.cart-bar .product-detail span {
  display: inline-block;
  line-height: 19px !important;
}

.cart-bar .product-detail .product-variation span {
  color: #868686;
  font-family: roboto;
  font-weight: 400;
  font-size: 13px;
  line-height: 15px;
}

.cart-bar .product-detail .product-qnty, .cart-bar .product-detail .product-price {
  color: #404040;
  font-weight: 500;
  font-size: 13px;
  font-family: roboto;
}

.cart-bar .cart-bottom-area {
  margin-top: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

.cart-bar .cart-bottom-area .spend-shipping {
  margin-bottom: 30px;
  background: #F5F5F5;
  padding: 10px 15px;
  font-size: 12px;
  font-weight: 500;
  color: var(--color-primary);
  text-align: center;
}

.cart-bar .cart-bottom-area .spend-shipping i {
  font-size: 15px;
  margin-right: 7px;
}

.cart-bar .cart-bottom-area .spend-shipping .amount {
  font-weight: 700;
  color: #040404;
}

.cart-bar .cart-bottom-area .total-price {
  font-size: 18px;
  color: #040404;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  margin-bottom: 20px;
}
.cart-bar .cart-bottom-area .total-price span {
  color: var(--color-primary);
}

.cart-bar .cart-bottom-area .checkout-btn {
  border: 1px solid var(--color-primary);
  margin-bottom: 10px;
  color: var(--color-primary);
}

.cart-bar .cart-bottom-area .cart-btn {
  width: 100%;
  padding: 10px 20px;
  border-radius: 7px;
  font-size: 14px;
  text-align: center;
  -webkit-transition: all 300ms;
  transition: all 300ms;
}

.cart-bar .cart-bottom-area .view-btn {
  border: 1px solid transparent;
  background: var(--color-primary);
  color: #fff;
}

.cart-bar .cart-bottom-area .cart-btn {
  width: 100%;
  padding: 10px 20px;
  border-radius: 7px;
  font-size: 14px;
  text-align: center;
  -webkit-transition: all 300ms;
  transition: all 300ms;
}

.cart-bar .cart-bottom-area .checkout-btn:hover {
  background: var(--color-primary);
  color: #fff;
}

.side-bar {
  position: fixed;
  overflow: hidden;
  top: 0;
  right: -100%;
  width: 465px;
  padding: 40px 30px;
  padding-top: 50px;
  height: 100%;
  display: block;
  background-color: white;
  backdrop-filter: blur(7px);
  z-index: 1900;
  transition: all 600ms ease;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow: visible;
}
@media only screen and (max-width: 575px) {
  .side-bar {
    width: 320px;
  }
}
.side-bar .inner-main-wrapper-desk .thumbnail {
  display: flex;
  justify-content: center;
}
.side-bar .inner-main-wrapper-desk .thumbnail img {
  width: 85%;
  margin: auto;
}
.side-bar .inner-main-wrapper-desk .inner-content {
  text-align: center;
  margin-top: 30px;
}
.side-bar .inner-main-wrapper-desk .inner-content p {
  max-width: 95%;
  text-align: center;
  margin: auto;
}
.side-bar .inner-main-wrapper-desk .inner-content .title {
  font-weight: 600;
}
.side-bar .inner-main-wrapper-desk .inner-content .footer {
  padding-top: 50px;
  margin-top: 80px;
  border-top: 1px solid #c2c2c2;
}
.side-bar .inner-main-wrapper-desk .inner-content .footer .title {
  font-weight: 500;
}
.side-bar .inner-main-wrapper-desk .inner-content .footer a.rts-btn {
  margin: auto;
}

.side-bar.show {
  right: 0;
  overflow-y: auto;
}

.side-bar button.close-icon-menu {
  max-width: max-content;
  margin-right: auto;
  margin-left: auto;
  margin-top: -50px;
  position: absolute;
  height: 45px;
  width: 45px;
  background: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  right: 0;
}
.side-bar button.close-icon-menu i {
  color: #fff;
  height: 50px;
  width: 50px;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0;
  margin-top: 0;
  font-size: 22px;
}

#anywhere-home.bgshow {
  background: #0e1013;
  opacity: 70%;
  visibility: visible;
  pointer-events: visible;
  z-index: 999;
  top: 0;
}

#anywhere-home {
  cursor: url(../images/banner/shape/close.png), auto;
  background: #0e1013;
  position: fixed;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease-in-out;
  pointer-events: none;
  z-index: 50;
}

.right-collups-add-product .right-collups-area-top .title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}
.right-collups-add-product .right-collups-area-top p {
  font-weight: 500;
  font-size: 14px;
}
.right-collups-add-product .input-main-wrapper .single-input {
  margin-top: 38px;
}
.right-collups-add-product .input-main-wrapper .single-input label {
  margin-bottom: 11px;
  font-size: 16px;
  color: #74787C;
}
.right-collups-add-product .input-main-wrapper .single-input input,
.right-collups-add-product .input-main-wrapper .single-input textarea {
  height: 62px;
  border: 1px solid #E8E9EB;
  border-radius: 4px;
}
.right-collups-add-product .input-main-wrapper .single-input textarea {
  height: 106px;
}

.file-upload-add-product .profile-left {
  position: relative;
  width: 100%;
  height: 100%;
}
.file-upload-add-product .profile-left .profile-image {
  position: relative;
}
.file-upload-add-product .profile-left .profile-image span {
  position: absolute;
  left: 50%;
  top: 68%;
  pointer-events: none;
  z-index: -1;
  transform: translate(-50%, -50%);
}
.file-upload-add-product .profile-left .profile-image img {
  height: auto;
  height: 150px;
  object-fit: scale-down;
  border: 1px dashed #74787C;
  min-width: 100%;
}
.file-upload-add-product .profile-left .button-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 100;
  pointer-events: none;
}

.button-area-botton-wrapper-p-list {
  display: flex;
  align-items: center;
  gap: 15px;
}
.button-area-botton-wrapper-p-list button {
  margin: 0;
  position: relative;
  border: 1px solid var(--color-primary);
}
.button-area-botton-wrapper-p-list button.bg-transparent {
  border: 1px solid var(--color-primary);
  color: var(--color-primary);
}
.button-area-botton-wrapper-p-list button.bg-transparent:hover {
  background: var(--color-primary) !important;
  color: #fff !important;
}

.between-stock-table.statrus p {
  margin: 0;
  padding: 7px 15px;
  border-radius: 33px;
  background: #EFF5E9;
  color: #629D23;
  font-weight: 500;
}

.footer-copyright {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25px;
  background: #fff;
  margin-bottom: -41px;
  margin-top: 20px;
  border-radius: 7px 7px 0 0;
  border: 1px solid #E2E2E2;
}
@media only screen and (max-width: 767px) {
  .footer-copyright {
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
}
.footer-copyright .left p {
  margin: 0;
  padding: 0;
  color: #2D3B29;
  font-weight: 500;
}
.footer-copyright ul {
  list-style: none;
  display: flex;
  align-items: center;
  gap: 45px;
  margin: 0;
  padding: 0;
}
.footer-copyright ul li {
  margin: 0;
}
.footer-copyright ul li a {
  color: #629D23;
}

.flex-direction-column {
  flex-direction: column;
}

.search-input-area {
  transition: all 500ms ease;
  visibility: hidden;
  transform: translateY(-100%);
  opacity: 0;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 57px 0;
  background: white;
  box-shadow: 1px 1px 50px rgba(0, 0, 0, 0.46);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search-input-area.show {
  visibility: visible;
  transform: inherit;
  opacity: 1;
}

.search-input-area .search-input-inner {
  display: flex;
  align-items: center;
  position: relative;
}

.search-input-area .search-input-inner .input-div {
  width: 80%;
  display: flex;
  align-items: center;
  margin: auto;
}

.search-input-area .search-input-inner .input-div input {
  background: #F7F7F7;
  border-radius: 5px;
  height: 55px;
  border: 1px solid transparent;
}
.search-input-area .search-input-inner .input-div input:focus {
  border: 1px solid var(--color-primary);
}

.search-input-area .search-input-inner .input-div button {
  max-width: max-content;
  padding: 18px 21px;
  background: var(--color-primary);
  display: flex;
  color: #fff;
  align-items: center;
  justify-content: center;
  display: block;
  margin-left: -9px;
  border-radius: 0 5px 5px 0;
}

.search-input-area .search-close-icon {
  cursor: pointer;
  position: absolute;
  right: 38px;
  top: 22px;
}

.search-input-area .search-close-icon i {
  position: relative;
  z-index: 1;
  color: var(--color-primary);
  transition: 0.3s;
  font-size: 18px;
}
.search-input-area .search-close-icon i:hover {
  color: #F7F7F7;
}
.search-input-area .search-close-icon i:hover::after {
  background: var(--color-primary);
}

.search-input-area .search-close-icon i::after {
  position: absolute;
  height: 45px;
  width: 45px;
  content: "";
  border-radius: 5px;
  background: rgba(85, 60, 223, 0.0784313725);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  transition: 0.3s;
}

#anywhere-home {
  cursor: url(../images/banner/shape/close.png), auto;
  background: #0e1013;
  position: fixed;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease-in-out;
  pointer-events: none;
  z-index: 50;
}

#anywhere-home.bgshow {
  background: #0e1013;
  opacity: 70%;
  visibility: visible;
  pointer-events: visible;
  z-index: 999;
  top: 0;
}

.progress-wrap {
  position: fixed;
  right: 30px;
  bottom: 30px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 10000;
  opacity: 1;
  visibility: hidden;
  transform: translateY(15px);
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
}

.progress-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  box-shadow: rgba(98, 157, 35, 0.062745098) 0px 0px 6px 7px;
}

.progress-wrap::after {
  position: absolute;
  font-family: var(--font-three);
  content: "\f077";
  text-align: center;
  line-height: 46px;
  font-size: 16px;
  color: var(--color-primary);
  left: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 1;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  border: 0px solid var(--color-primary);
  box-shadow: none;
  border-radius: 50% !important;
  border-radius: 5px;
  font-weight: 300;
}

.progress-wrap:hover::after {
  opacity: 1;
  content: "\f077";
  border: 0px solid var(--color-primary);
  font-weight: 300;
}

.progress-wrap::before {
  position: absolute;
  font-family: var(--font-three);
  content: "\f077";
  text-align: center;
  line-height: 46px;
  font-size: 16px;
  opacity: 0;
  background: var(--color-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  left: 0;
  top: 0;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  z-index: 2;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  font-weight: 300;
}

.progress-wrap:hover::before {
  opacity: 0;
}

.progress-wrap svg path {
  fill: none;
}

.progress-wrap svg {
  color: var(--color-primary);
  border-radius: 50%;
  background: #fff;
}

.progress-wrap svg.progress-circle path {
  stroke: var(--color-primary);
  stroke-width: 0px;
  box-sizing: border-box;
  -webkit-transition: all 200ms linear;
  transition: all 200ms linear;
  padding: 2px;
}

.home-blue .progress-wrap svg.progress-circle path {
  stroke: var(--color-primary-2);
}
.home-blue .progress-wrap::after {
  border-color: var(--color-primary-2);
  box-shadow: 0px 3px 20px 6px rgba(7, 66, 233, 0.3215686275);
  color: var(--color-primary-2);
}

.registration-wrapper-1 {
  max-width: 800px;
  border-radius: 20px;
  background: #FFFFFF;
  margin: auto;
  text-align: center;
  padding: 100px 150px;
}
@media only screen and (max-width: 767px) {
  .registration-wrapper-1 {
    padding: 80px 50px;
  }
}
@media only screen and (max-width: 575px) {
  .registration-wrapper-1 {
    padding: 40px 15px;
  }
}
.registration-wrapper-1 .logo-area img {
  max-width: 57px;
  margin: auto;
  margin-bottom: 30px;
}

.registration-form {
  text-align: left;
}
.registration-form .input-wrapper {
  width: 100%;
  text-align: left;
  margin-bottom: 30px;
}
.registration-form .input-wrapper:last-child {
  margin-bottom: 0;
}
.registration-form .input-wrapper label {
  margin-bottom: 12px;
  font-weight: 500;
  color: #2C3C28;
}
.registration-form .input-wrapper input {
  border-radius: 5px;
  border: 1px solid #EBEBEB;
  height: 50px;
}
.registration-form .input-wrapper input:focus {
  border: 1px solid var(--color-primary);
}
.registration-form button.rts-btn {
  max-width: 100%;
  width: 100%;
}
.registration-form .another-way-to-registration .registradion-top-text {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  margin-bottom: 30px;
  position: relative;
}
.registration-form .another-way-to-registration .registradion-top-text span {
  font-weight: 500;
  color: #2C3C28;
}
.registration-form .another-way-to-registration .registradion-top-text::after {
  position: absolute;
  content: "";
  left: 0;
  width: 35%;
  height: 1px;
  background: #E7E7E7;
}
@media only screen and (max-width: 767px) {
  .registration-form .another-way-to-registration .registradion-top-text::after {
    display: none;
  }
}
.registration-form .another-way-to-registration .registradion-top-text::before {
  position: absolute;
  content: "";
  right: 0%;
  width: 35%;
  height: 1px;
  background: #E7E7E7;
}
@media only screen and (max-width: 767px) {
  .registration-form .another-way-to-registration .registradion-top-text::before {
    display: none;
  }
}
.registration-form .another-way-to-registration .login-with-brand {
  display: flex;
  align-items: center;
  gap: 10px;
}
.registration-form .another-way-to-registration .login-with-brand > a {
  flex-basis: 49%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  border-radius: 6px;
  border: 1px solid #EBEBEB;
}
.registration-form .another-way-to-registration p {
  margin: 0;
  display: flex;
  justify-content: center;
  margin-top: 30px;
  font-size: 16px;
}
.registration-form .another-way-to-registration p a {
  color: #2C3C28;
  font-weight: 600;
  margin-left: 10px;
}

.single-brand-area-start {
  text-align: center;
  border: 1px solid #f1f1f1;
  padding: 40px 25px;
  border-radius: 6px;
}
.single-brand-area-start .logo {
  margin-bottom: 26px;
}
.single-brand-area-start .item {
  font-size: 18px;
  font-weight: 500;
  color: var(--color-primary);
}

.rating-table ul {
  list-style: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  gap: 3px;
  color: #eeae00;
}
.rating-table ul li {
  margin: 0;
  padding: 0;
}
.rating-table ul li i {
  color: #eeae00;
  font-size: 14px;
}

.rating-page tbody tr td:nth-child(2) {
  width: 15%;
}

.customers-details-wrapper-one-dashboard {
  padding: 30px;
}
.customers-details-wrapper-one-dashboard .title {
  font-size: 20px;
  margin-bottom: 15px;
}
.customers-details-wrapper-one-dashboard .main-customers-details-top {
  display: flex;
  align-items: center;
  gap: 146px;
  border-bottom: 1px solid #E2E2E2;
  padding-bottom: 35px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .customers-details-wrapper-one-dashboard .main-customers-details-top {
    gap: 25px;
    flex-direction: column;
    align-items: flex-start;
  }
}
@media only screen and (max-width: 1199px) {
  .customers-details-wrapper-one-dashboard .main-customers-details-top {
    gap: 25px;
    flex-direction: column;
    align-items: flex-start;
  }
}
.customers-details-wrapper-one-dashboard .main-customers-details-top .left {
  display: flex;
  align-items: center;
  gap: 19px;
}
.customers-details-wrapper-one-dashboard .main-customers-details-top .left .name {
  margin-bottom: 5px;
}
.customers-details-wrapper-one-dashboard .main-customers-details-top .right-area {
  display: flex;
  align-items: center;
  gap: 120px;
  justify-content: space-between;
  width: 100%;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .customers-details-wrapper-one-dashboard .main-customers-details-top .right-area {
    gap: 65px;
  }
}
@media only screen and (max-width: 1199px) {
  .customers-details-wrapper-one-dashboard .main-customers-details-top .right-area {
    gap: 30px;
  }
}
.customers-details-wrapper-one-dashboard .main-customers-details-top .right-area .short-contact-info p {
  margin-bottom: 10px;
}
.customers-details-wrapper-one-dashboard .main-customers-details-top .right-area .short-contact-info a {
  font-weight: 500;
  color: #2D3B29;
}

.billing-address-area-4 {
  padding: 0 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #E2E2E2;
  margin-bottom: 30px;
}
.billing-address-area-4:last-child {
  border: none;
}
.billing-address-area-4 .main-billing-address-wrapper {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 15px;
}
.billing-address-area-4 .main-billing-address-wrapper .single-billing-address {
  flex-basis: 25%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .billing-address-area-4 .main-billing-address-wrapper .single-billing-address {
    flex-basis: 45%;
  }
}
@media only screen and (max-width: 767px) {
  .billing-address-area-4 .main-billing-address-wrapper .single-billing-address {
    flex-basis: 45%;
  }
}
@media only screen and (max-width: 575px) {
  .billing-address-area-4 .main-billing-address-wrapper .single-billing-address {
    flex-basis: 100%;
  }
}
.billing-address-area-4 .main-billing-address-wrapper .single-billing-address p {
  margin-bottom: 10px;
}
.billing-address-area-4 .main-billing-address-wrapper .single-billing-address p span {
  font-weight: 500;
  color: var(--color-heading-1);
}

.single-over-fiew-card {
  padding: 26px;
  border: 1px solid #E2E2E2;
  border-radius: 5px;
  background: #FFFFFF;
  height: 100%;
}

.single-over-fiew-card .top-main {
  color: #74787C;
  font-weight: 500;
}
.single-over-fiew-card .bottom {
  display: flex;
  align-items: center;
  margin-top: 15px;
  gap: 40px;
}
@media only screen and (min-width: 1600px) and (max-width: 1919px) {
  .single-over-fiew-card .bottom {
    gap: 15px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-over-fiew-card .bottom {
    gap: 15px;
  }
}
@media only screen and (max-width: 1199px) {
  .single-over-fiew-card .bottom {
    gap: 5px;
  }
}
.single-over-fiew-card .bottom .title {
  font-size: 38px;
  line-height: 1.2;
  margin-bottom: 0;
  word-break: keep-all;
}
@media only screen and (min-width: 1600px) and (max-width: 1919px) {
  .single-over-fiew-card .bottom .title {
    font-size: 26px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-over-fiew-card .bottom .title {
    font-size: 26px;
  }
}
@media only screen and (max-width: 1199px) {
  .single-over-fiew-card .bottom .title {
    font-size: 26px;
  }
}
.single-over-fiew-card .bottom .right-primary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-over-fiew-card .bottom .right-primary {
    gap: 5px;
  }
}
@media only screen and (max-width: 1199px) {
  .single-over-fiew-card .bottom .right-primary {
    gap: 5px;
  }
}
@media only screen and (min-width: 1200px) and (max-width: 1599px) {
  .single-over-fiew-card .bottom .right-primary img {
    max-width: 40px;
  }
}
.single-over-fiew-card .bottom .right-primary .increase i {
  color: var(--color-primary);
}
.single-over-fiew-card .bottom .right-primary .increase span {
  color: var(--color-primary);
}
@media only screen and (max-width: 1199px) {
  .single-over-fiew-card .bottom .right-primary img {
    max-width: 40px;
  }
}
/*# sourceMappingURL=../maps/style.css.map */
