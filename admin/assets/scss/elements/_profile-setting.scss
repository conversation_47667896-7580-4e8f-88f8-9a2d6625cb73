
// profile setting area start
.profile-setting-area-main-wrapper{
    padding: 33px;
    @media #{$large-mobile} {
        padding: 8px;
    }
    .title{
        font-size: 20px;
    }
    .inner-profile-setting{
        padding: 30px;
        border: 1px solid #E2E2E2;
        background: #fff;
        border-radius: 8px;
        display: flex;
        align-items: flex-start;
        gap: 23px;
        @media #{$sm-layout} {
            flex-direction: column;
            align-items: flex-start;
        }
        @media #{$large-mobile} {
            padding: 15px;
        }
        .left-setting-area{
            padding: 40px 12px 12px 12px;
            background: #F3F4F6;
            width: 35%;
            text-align: center;
            border-radius: 10px;
            @media #{$sm-layout} {
                width: 100%;
            }
            .personal-info{
                .thumbnail-img{
                    margin-bottom: 26px;
                }
                .infor{
                    .title{
                        margin-bottom: 10px;
                        font-size: 20px;
                    }
                }
            }
        }
    }
}

.tab-button-area-setting{
    margin-top: 40px;
    ul{
        padding: 0;
        list-style: none;
        flex-direction: column;
        margin: 0;
        li{
            margin: 0;
            display: block;
            button{
                display: block;
                height: 60px;
                background: #629D23 !important;
                color: #FFFFFF;
                text-align: left;
                border: none !important;
                padding: 0 24px;
                font-weight: 500;
                color: #fff;
                display: block;
                min-width: max-content;
                &.active{
                    color: #fff !important;
                    background: #6DA432 !important;
                }
                &:hover{
                    color: #fff !important;
                    background: #6DA432 !important;
                }
                img{
                    margin-right: 18px;
                }
            }
        }
    }
}

.tab-content-area-user-setting{
    border: 1px solid #E2E2E2;
    width: calc(65% - 30px);
    border-radius: 10px;
    padding: 27px;
    @media #{$sm-layout} {
        width: 100%;
    }
    @media #{$large-mobile} {
        padding: 10px;
    }
}

.inner-content-setting-form{
    .title{
        font-size: 16px;
        font-weight: 600;
        margin: 0;
    }
    p{
        margin: 0;
        font-size: 14px;
    }
    form{
        margin-top: 40px;
        .half-input-wrapper{
            display: flex;
            align-items: center;
            gap: 25px;
            margin-bottom: 45px;
            @media #{$large-mobile} {
                flex-direction: column;
                align-items: flex-start;
                gap: 20px;
            }
            .single{
                flex-basis: 50%;
                @media #{$large-mobile} {
                    flex-basis: 100%;
                    width: 100%;
                }
                label{
                    color: #2D3B29;
                    font-weight: 500;
                    margin-bottom: 10px;
                }
                input{
                    border: 1px solid #E8E9EB;
                    height: 48px;
                    border-radius: 4px;
                    &:focus{
                        border: 1px solid var(--color-primary);
                    }
                }
            }
        }
        .about-me-area-setting-area{
            label{
                color: #2D3B29;
                font-weight: 500;
                margin-bottom: 10px;
            }
            textarea{
                height: 150px;
                border: 1px solid #E8E9EB;
                text-align: left;
                padding: 15px;
                margin-bottom: 20px;
                border-radius: 4px;
                &:focus{
                    border: 1px solid var(--color-primary);
                }
            }
        }
    }
}

form.change-pass-form,
.social-media-edit-wrapper{
    .single{
        margin-bottom: 25px;
        &:last-child{
            margin-bottom: 0;
        }
    }
    label{
        color: #2D3B29;
        font-weight: 500;
        margin-bottom: 10px;
    }
    input{
        border: 1px solid #E8E9EB;
        height: 48px;
        border-radius: 4px;
    }
}

.about-me-area-setting-area{
    margin-top: 30px;
    .title{
        font-size: 15px;
        margin-bottom: 15px;
    }
    .inner-border-wrapper{
        display: flex;
        padding: 19px;
        border: 1px solid #E8E9EB;
        margin-bottom: 20px;
        padding-bottom: 30px;
        border-radius: 5px;
    }
    .button-area{
        display: flex;
        align-items: center;
        gap: 15px;
    }
}

.profile-image{
    img{
        border-radius: 5px;
        border: 5px solid var(--color-border);
        height: auto;
        width: max-content;
        object-fit: cover;
        max-width: 250px;
        height: 300px;
    }
}

.brows-file-wrapper {
    position: relative;
    cursor: pointer;
    input{
        position: absolute;
        height: 100%;
        width: 100%;
        opacity: 0;
        cursor: pointer;
        background: var(--background-color-4);
        height: 50px;
        border-radius: 5px;
        color: var(--color-white);
        font-size: 14px;
        padding: 10px 20px;
        border: 2px solid var(--color-border);
        transition: 0.3s;
    }
    label{
        width: max-content;
        padding: 0 16px;
        height: 45px;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        background: var(--color-primary);
        color: var(--color-white);
        font-weight: 500;
        font-size: 16px;
        transition: .3s;
        position: relative;
        z-index: 10;
    }
}

.notification__items{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    gap: 20px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    padding-left: 0;
    margin: 0;
    list-style: none;
    .single__items{
        list-style: none;
        margin: 0;
        .single-link{
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            gap: 10px;
            color: var(--body-color);
            font-size: 14px;
            line-height: 21px;
            font-weight: 600;
            cursor: pointer;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            gap: 15px !important;
            font-size: 16px !important;
            font-weight: 500 !important;
            color: var(--dark-color) !important;
            -webkit-box-align: start !important;
            -ms-flex-align: start !important;
            align-items: flex-start !important;
            .avatar{
                width: 40px;
                height: 40px;
                margin-top: 3px;
                border-radius: 11%;
                background-color: #eceaf3;
                border: none;
            }
            .main-content{
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-flex: 1;
                -ms-flex: 1;
                flex: 1;
                gap: 5px;
                -webkit-box-orient: vertical;
                -webkit-box-direction: normal;
                -ms-flex-direction: column;
                flex-direction: column;
                -webkit-box-pack: justify;
                -ms-flex-pack: justify;
                justify-content: space-between;
                .name-user{
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: justify;
                    -ms-flex-pack: justify;
                    justify-content: space-between;
                    font-size: 15px;
                    line-height: 21px;
                    font-weight: 500;
                    margin-bottom: 0;
                    -webkit-transition: color 0.3s ease;
                    transition: color 0.3s ease;

                    .time-ago{
                        color: var(--color-primary) !important;
                        font-size: 12px;
                    }
                }
                .disc{
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: justify;
                    -ms-flex-pack: justify;
                    justify-content: space-between;
                    font-size: 15px;
                    font-weight: 400;
                    font-size: 14px;
                    .count{
                        width: 10px;
                        height: 10px;
                        background-color: var(--color-primary) !important;
                        display: -webkit-box;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-box-align: center;
                        -ms-flex-align: center;
                        align-items: center;
                        -webkit-box-pack: center;
                        -ms-flex-pack: center;
                        justify-content: center;
                        width: 10px;
                        height: 10px;
                        border-radius: 100%;
                        font-size: 10px;
                        color: #fff;
                        background: var(--color-danger);
                        margin-top: 5px;
                        animation: zeroone 1s ease-in-out infinite;
                    }
                }
            }
        }
    }
}


@keyframes zeroone {
    from {
        opacity: 0;
    }
    // 50%{
    //     scale: .5;
    // }
    to {
        opacity: 1;
        scale: .5;
    }
  }


.apex-chart-top-area-banner{
    display: flex;
    align-items: center;
    justify-content: space-between;
}