---
name: Bug Report
about: Report a bug for the MongoDB PHP library.
title: ''
labels: ''
assignees: ''

---

<!--
You are about to open an issue for the MongoDB PHP library. If you instead
intended to report an issue with the MongoDB PHP extension, please do so via its
repository[^1].

If you've identified a security vulnerability in a driver or any other MongoDB
project, please create a vulnerability report[^2].

[^1]: https://github.com/mongodb/mongo-php-driver
[^2]: https://mongodb.com/docs/manual/tutorial/create-a-vulnerability-report
-->

### Bug Report

<!--
Briefly describe the issue. Feel free to cross-reference your report with any
other related GitHub or JIRA issues for additional context. Before reporting an
issue, check that you are using the most recent version of the driver.
-->

### Environment

<!--
What operating system and PHP version are you using? Mention if PHP was
installed through a third-party bundle (e.g. MAMP, XAMPP) and report its version
if applicable.

What version of MongoDB are you using? Include details about the topology (e.g.
replica set, sharded cluster) and how you are connecting to the database (e.g.
TLS connections, authentication). If possible, share your connection string and
additional URI options (be sure to redact any auth credentials). Please specify
whether you are self-hosting the database or using a cloud provider, such as
MongoDB Atlas.

Include version information for the extension, libmongoc, and libbson. This may
be collected by grepping phpinfo() output:

    php -i | grep -E 'mongodb|libmongoc|libbson'

In some cases, it may be helpful to provide all phpinfo() output (e.g. `php -i`)
and/or list all installed extensions (e.g. `php -m`).

Include version information for the library. This may be
collected by running the following from your project's root directory:

    composer show mongodb/mongodb
-->

### Test Script

<!--
If possible, attach a complete PHP script that can be executed on its own to
reproduce the issue. Clarify whether this script can be run from the CLI or if
it can must be run through a web SAPI.

If the error only presents itself in a web SAPI, note whether the issue can be
reproduced with PHP's built-in web server[^3]. If not, please share your web
server version and any relevant configuration details in the Environment section
above.

[^3]: https://php.net/manual/en/features.commandline.webserver.php
-->

### Expected and Actual Behavior

<!--
How did the script fail to perform the expected task? Feel free to include debug
and/or `var_dump()` output if that helps explain the error.

If the observed behavior is an unexpected exception, please include its full
message and any relevant backtrace information. If you encountered a
segmentation fault, please include a GDB backtrace[^4].

[^4]: https://bugs.php.net/bugs-generating-backtrace.php
-->

### Debug Log

<!--
If the issue relates to internal driver behavior (e.g. connection issues),
please include a debug log[^5]. This may be generated by setting the
`mongodb.debug` INI option to "stderr" or a directory (useful for web SAPIs).

[^5]: https://php.net/manual/en/mongodb.configuration.php#ini.mongodb.debug
-->
