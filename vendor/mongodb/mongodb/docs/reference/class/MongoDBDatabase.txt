=======================
MongoDB\\Database Class
=======================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpclass:: MongoDB\\Database

   Provides methods for common operations on a database, such as executing
   database commands and managing collections.

   You can construct a database directly using the driver's
   :php:`MongoDB\\Driver\\Manager <class.mongodb-driver-manager>` class or
   select a database from the library's :phpclass:`MongoDB\\Client` class. A
   database may also be cloned from an existing :phpclass:`MongoDB\\Database`
   object via the :phpmethod:`withOptions() <MongoDB\\Database::withOptions>`
   method.

   :phpclass:`MongoDB\\Database` supports the :php:`readConcern
   <mongodb-driver-readconcern>`, :php:`readPreference
   <mongodb-driver-readpreference>`, :php:`typeMap
   <manual/en/mongodb.persistence.deserialization.php#mongodb.persistence.typemaps>`,
   and :php:`writeConcern <mongodb-driver-writeconcern>` options. If you omit an
   option, the database inherits the value from the :php:`Manager
   <mongodb-driver-manager>` constructor argument or the :phpclass:`Client
   <MongoDB\\Client>` object used to select the database.

   Operations within the :phpclass:`MongoDB\\Database` class inherit the
   Database's options.

Methods
-------

.. toctree::
   :titlesonly:

   /reference/method/MongoDBDatabase__construct
   /reference/method/MongoDBDatabase__get
   /reference/method/MongoDBDatabase-aggregate
   /reference/method/MongoDBDatabase-command
   /reference/method/MongoDBDatabase-createCollection
   /reference/method/MongoDBDatabase-drop
   /reference/method/MongoDBDatabase-dropCollection
   /reference/method/MongoDBDatabase-getDatabaseName
   /reference/method/MongoDBDatabase-getManager
   /reference/method/MongoDBDatabase-getReadConcern
   /reference/method/MongoDBDatabase-getReadPreference
   /reference/method/MongoDBDatabase-getTypeMap
   /reference/method/MongoDBDatabase-getWriteConcern
   /reference/method/MongoDBDatabase-listCollectionNames
   /reference/method/MongoDBDatabase-listCollections
   /reference/method/MongoDBDatabase-modifyCollection
   /reference/method/MongoDBDatabase-renameCollection
   /reference/method/MongoDBDatabase-selectCollection
   /reference/method/MongoDBDatabase-selectGridFSBucket
   /reference/method/MongoDBDatabase-watch
   /reference/method/MongoDBDatabase-withOptions

