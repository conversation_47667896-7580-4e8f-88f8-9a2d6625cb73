=============================================
MongoDB\\GridFS\\Bucket::getFileIdForStream()
=============================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getFileIdForStream()

   Gets the file document's ID of the GridFS file associated with a stream.

   .. code-block:: php

      function getFileIdForStream($stream): mixed

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-getFileIdForStream-param.rst

Return Values
-------------

The ``_id`` field of the metadata document associated with the GridFS stream.
The return type will depend on the bucket's ``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-gridfs-corruptfileexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = $bucket->openUploadStream('filename');

   $id = $bucket->getFileIdForStream($stream);

   var_dump($id);

   fclose($stream);

The output would then resemble::

   object(MongoDB\BSON\ObjectId)#3005 (1) {
     ["oid"]=>
     string(24) "5acfb37d7e21e83cdb3e1583"
   }

See Also
--------

- :phpmethod:`MongoDB\\GridFS\\Bucket::getFileDocumentForStream()`
