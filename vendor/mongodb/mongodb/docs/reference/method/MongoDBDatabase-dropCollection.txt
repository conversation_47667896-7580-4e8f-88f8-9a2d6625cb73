===================================
MongoDB\\Database::dropCollection()
===================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Database::dropCollection()

   Drop a collection within the current database.

   .. code-block:: php

      function dropCollection($collectionName, array $options = []): array|object

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBDatabase-method-dropCollection-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBDatabase-method-dropCollection-option.rst

Return Values
-------------

An array or object with the result document of the :manual:`drop
</reference/command/drop>` command. The return type will depend on the
``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Example
-------

The following example drops the ``users`` collection in the ``test`` database:

.. code-block:: php

   <?php

   $db = (new MongoDB\Client)->test;

   $result = $db->dropCollection('users');

   var_dump($result);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#8 (1) {
     ["storage":"ArrayObject":private]=>
     array(3) {
       ["ns"]=>
       string(10) "test.users"
       ["nIndexesWas"]=>
       int(1)
       ["ok"]=>
       float(1)
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Collection::drop()`
- :manual:`drop </reference/command/drop>` command reference in the MongoDB
  manual
