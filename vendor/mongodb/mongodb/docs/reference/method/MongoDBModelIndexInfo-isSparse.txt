=====================================
MongoDB\\Model\\IndexInfo::isSparse()
=====================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\IndexInfo::isSparse()

   Return whether the index is a :manual:`sparse index </core/index-sparse>`.
   This correlates with the ``sparse`` option for
   :phpmethod:`MongoDB\\Collection::createIndex()`.

   .. code-block:: php

      function isSparse(): boolean

Return Values
-------------

A boolean indicating whether the index is a sparse index.

Examples
--------

.. code-block:: php

   <?php

   $info = new IndexInfo([
       'sparse' => true,
   ]);

   var_dump($info->isSparse());

The output would then resemble::

   bool(true)

See Also
--------

- :phpmethod:`MongoDB\\Collection::createIndex()`
- :manual:`listIndexes </reference/command/listIndexes>` command reference in
  the MongoDB manual
- :manual:`Sparse Indexes </core/index-sparse>` in the MongoDB manual
