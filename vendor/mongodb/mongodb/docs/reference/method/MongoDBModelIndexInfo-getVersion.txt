=======================================
MongoDB\\Model\\IndexInfo::getVersion()
=======================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\IndexInfo::getVersion()

   Return the index version.

   .. code-block:: php

      function getVersion(): integer

Return Values
-------------

The index version.

Examples
--------

.. code-block:: php

   <?php

   $info = new IndexInfo([
       'v' => 1,
   ]);

   var_dump($info->getVersion());

The output would then resemble::

   int(1)

See Also
--------

- :phpmethod:`MongoDB\\Collection::createIndex()`
- :manual:`listIndexes </reference/command/listIndexes>` command reference in
  the MongoDB manual
