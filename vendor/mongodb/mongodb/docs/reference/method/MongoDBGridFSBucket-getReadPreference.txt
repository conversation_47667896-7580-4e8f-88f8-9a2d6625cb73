============================================
MongoDB\\GridFS\\Bucket::getReadPreference()
============================================

.. versionadded:: 1.2

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getReadPreference()

   Returns the read preference for this GridFS bucket.

   .. code-block:: php

      function getReadPreference(): MongoDB\Driver\ReadPreference

Return Values
-------------

A :php:`MongoDB\\Driver\\ReadPreference <class.mongodb-driver-readpreference>`
object.

Example
-------

.. code-block:: php

   <?php

   $database = (new MongoDB\Client)->selectDatabase('test');
   $bucket = $database->selectGridFSBucket([
      'readPreference' => new MongoDB\Driver\ReadPreference('primaryPreferred'),
   ]);

   var_dump($bucket->getReadPreference());

The output would then resemble::

   object(MongoDB\Driver\ReadPreference)#3 (1) {
     ["mode"]=>
     string(16) "primaryPreferred"
   }

See Also
--------

- :manual:`Read Preference </reference/read-preference>` in the MongoDB manual
- :phpmethod:`MongoDB\\Client::getReadPreference()`
- :phpmethod:`MongoDB\\Collection::getReadPreference()`
- :phpmethod:`MongoDB\\Database::getReadPreference()`
