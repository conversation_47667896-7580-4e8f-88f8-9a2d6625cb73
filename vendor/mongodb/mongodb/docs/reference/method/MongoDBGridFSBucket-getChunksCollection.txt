==============================================
MongoDB\\GridFS\\Bucket::getChunksCollection()
==============================================

.. versionadded:: 1.2

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::getChunksCollection()

   Returns the chunks collection used by the bucket.

   .. code-block:: php

      function getChunksCollection(): MongoDB\Collection

Return Values
-------------

A :phpclass:`MongoDB\\Collection` object for the chunks collection.

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   var_dump((string) $bucket->getChunksCollection());

The output would then resemble::

   string(14) "test.fs.chunks"
