=====================================
MongoDB\\Model\\IndexInfo::isUnique()
=====================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\IndexInfo::isUnique()

   Return whether the index is a :manual:`unique index </core/index-unique>`.
   This correlates with the ``unique`` option for
   :phpmethod:`MongoDB\\Collection::createIndex()`.

   .. code-block:: php

      function isUnique(): boolean

Return Values
-------------

A boolean indicating whether the index is a unique index.

Examples
--------

.. code-block:: php

   <?php

   $info = new IndexInfo([
       'unique' => true,
   ]);

   var_dump($info->isUnique());

The output would then resemble::

   bool(true)

See Also
--------

- :phpmethod:`MongoDB\\Collection::createIndex()`
- :manual:`listIndexes </reference/command/listIndexes>` command reference in
  the MongoDB manual
- :manual:`Unique Indexes </core/index-unique>` in the MongoDB manual
