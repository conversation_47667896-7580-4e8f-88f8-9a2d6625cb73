=============================================
MongoDB\\GridFS\\Bucket::openDownloadStream()
=============================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::openDownloadStream()

   Selects a GridFS file by its ``_id`` and opens it as a readable stream.

   .. code-block:: php

      function openDownloadStream($id): resource

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-openDownloadStream-param.rst

Return Values
-------------

A readable stream resource.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-gridfs-filenotfoundexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $uploadStream = fopen('php://temp', 'w+b');
   fwrite($uploadStream, "foobar");
   rewind($uploadStream);

   $id = $bucket->uploadFromStream('filename', $uploadStream);

   $downloadStream = $bucket->openDownloadStream($id);

   var_dump(stream_get_contents($downloadStream));

The output would then resemble::

   string(6) "foobar"

See Also
--------

- :phpmethod:`MongoDB\\GridFS\\Bucket::downloadToStream()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::downloadToStreamByName()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::openDownloadStreamByName()`
