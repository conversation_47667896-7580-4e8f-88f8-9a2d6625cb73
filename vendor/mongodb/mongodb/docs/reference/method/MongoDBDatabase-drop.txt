=========================
MongoDB\\Database::drop()
=========================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Database::drop()

   Drop the database.

   .. code-block:: php

      function drop(array $options = []): array|object

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBDatabase-method-drop-param.rst

   The ``$options`` parameter supports the following options:

   .. include:: /includes/apiargs/MongoDBDatabase-method-drop-option.rst

Return Values
-------------

An array or object with the result document of the :manual:`dropDatabase
</reference/command/dropDatabase>` command. The return type will depend on the
``typeMap`` option.

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-unsupportedexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Example
-------

The following example drops the ``test`` database:

.. code-block:: php

   <?php

   $db = (new MongoDB\Client)->test;

   $result = $db->drop();

   var_dump($result);

The output would then resemble::

   object(MongoDB\Model\BSONDocument)#8 (1) {
     ["storage":"ArrayObject":private]=>
     array(2) {
       ["dropped"]=>
       string(4) "test"
       ["ok"]=>
       float(1)
     }
   }

See Also
--------

- :phpmethod:`MongoDB\\Client::dropDatabase()`
- :manual:`dropDatabase </reference/command/dropDatabase>` command reference in
  the MongoDB manual
