=============================================
MongoDB\\Model\\DatabaseInfo::getSizeOnDisk()
=============================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\Model\\DatabaseInfo::getSizeOnDisk()

   Return the total size of the database file on disk in bytes.

   .. code-block:: php

      function getSizeOnDisk(): integer

Return Values
-------------

The total size of the database file on disk in bytes.

Examples
--------

.. code-block:: php

   <?php

   $info = new DatabaseInfo(['sizeOnDisk' => 1048576]);

   var_dump($info->getSizeOnDisk());

The output would then resemble::

   int(1048576)

See Also
--------

- :manual:`listDatabases </reference/command/listDatabases>` command reference
  in the MongoDB manual
