===========================================
MongoDB\\GridFS\\Bucket::downloadToStream()
===========================================

.. default-domain:: mongodb

.. contents:: On this page
   :local:
   :backlinks: none
   :depth: 1
   :class: singlecol

Definition
----------

.. phpmethod:: MongoDB\\GridFS\\Bucket::downloadToStream()

   Selects a GridFS file by its ``_id`` and copies its contents to a writable
   stream.

   .. code-block:: php

      function downloadToStream($id, $destination): void

   This method has the following parameters:

   .. include:: /includes/apiargs/MongoDBGridFSBucket-method-downloadToStream-param.rst

Errors/Exceptions
-----------------

.. include:: /includes/extracts/error-gridfs-filenotfoundexception.rst
.. include:: /includes/extracts/error-invalidargumentexception.rst
.. include:: /includes/extracts/error-driver-runtimeexception.rst

Examples
--------

.. code-block:: php

   <?php

   $bucket = (new MongoDB\Client)->test->selectGridFSBucket();

   $stream = fopen('php://temp', 'w+b');
   fwrite($stream, "foobar");
   rewind($stream);

   $id = $bucket->uploadFromStream('filename', $stream);

   $destination = fopen('php://temp', 'w+b');

   $bucket->downloadToStream($id, $destination);

   var_dump(stream_get_contents($destination, -1, 0));

The output would then resemble::

   string(6) "foobar"

See Also
--------

- :phpmethod:`MongoDB\\GridFS\\Bucket::downloadToStreamByName()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::openDownloadStream()`
- :phpmethod:`MongoDB\\GridFS\\Bucket::openDownloadStreamByName()`
