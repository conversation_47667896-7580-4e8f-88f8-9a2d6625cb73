arg_name: option
name: collation
type: array|object
description: |
   :manual:`Collation </reference/collation>` allows users to specify
   language-specific rules for string comparison, such as rules for lettercase
   and accent marks. When specifying collation, the ``locale`` field is
   mandatory; all other collation fields are optional. For descriptions of the
   fields, see :manual:`Collation Document
   </reference/collation/#collation-document>`.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: comment
type: mixed
description: |
  Enables users to specify an arbitrary comment to help trace the operation
  through the :manual:`database profiler </reference/database-profiler>`,
  :manual:`currentOp </reference/command/currentOp>` output, and
  :manual:`logs </reference/log-messages>`.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: hint
type: string|array|object
description: |
  The index to use. Specify either the index name as a string or the index key
  pattern as a document. If specified, then the query system will only consider
  plans using the hinted index.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: let
type: array|object
description: |
  Map of parameter names and values. Values must be constant or closed
  expressions that do not reference document fields. Parameters can then be
  accessed as variables in an aggregate expression context (e.g. ``$$var``).

  This is not supported for server versions prior to 5.0 and will result in an
  exception at execution time if used.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: maxTimeMS
type: integer
description: |
   The cumulative time limit in milliseconds for processing operations on the
   cursor. MongoDB aborts the operation at the earliest following
   :term:`interrupt point`.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: readConcern
type: :php:`MongoDB\\Driver\\ReadConcern <class.mongodb-driver-readconcern>`
description: |
  The default read concern to use for {{resource}} operations. Defaults to the
  {{parent}}'s read concern.
interface: phpmethod
operation: ~
optional: true
replacement:
  resource: "collection"
  parent: "client"
---
arg_name: option
name: readPreference
type: :php:`MongoDB\\Driver\\ReadPreference <class.mongodb-driver-readpreference>`
description: |
  The default read preference to use for {{resource}} operations. Defaults to
  the {{parent}}'s read preference.
interface: phpmethod
operation: ~
optional: true
replacement:
  resource: "collection"
  parent: "client"
---
arg_name: option
name: session
type: :php:`MongoDB\\Driver\\Session <class.mongodb-driver-session>`
description: |
  Client session to associate with the operation.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: typeMap
type: array
description: |
  The :php:`type map
  <manual/en/mongodb.persistence.deserialization.php#mongodb.persistence.typemaps>`
  to apply to cursors, which determines how BSON documents are converted to PHP
  values. Defaults to the {{parent}}'s type map.
interface: phpmethod
operation: ~
optional: true
replacement:
  parent: "client"
---
arg_name: option
name: writeConcern
type: :php:`MongoDB\\Driver\\WriteConcern <class.mongodb-driver-writeconcern>`
description: |
  The default write concern to use for {{resource}} operations. Defaults
  to the {{parent}}'s write concern.
interface: phpmethod
operation: ~
optional: true
replacement:
  resource: "collection"
  parent: "client"
...
