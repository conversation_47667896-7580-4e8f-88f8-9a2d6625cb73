source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: collation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
arg_name: option
name: hint
type: string|array|object
description: |
  The index to use. Specify either the index name as a string or the index key
  pattern as a document. If specified, then the query system will only consider
  plans using the hinted index.

  .. versionchanged:: 1.2
     If a document is provided, it is passed to the command as-is. Previously,
     the library would convert the key pattern to an index name.
interface: phpmethod
operation: ~
optional: true
---
arg_name: option
name: limit
type: integer
description: |
  The maximum number of matching documents to return.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-option.yaml
  ref: maxTimeMS
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readConcern
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: readPreference
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
arg_name: option
name: skip
type: integer
description: |
  The number of matching documents to skip before returning results.
interface: phpmethod
operation: ~
optional: true
...
