arg_name: param
name: $fromCollectionName
type: string
description: |
  The name of the collection to rename.
interface: phpmethod
operation: ~
optional: false
---
arg_name: param
name: $toCollectionName
type: string
description: |
  The new name of the collection.
interface: phpmethod
operation: ~
optional: false
---
arg_name: param
name: $toDatabaseName
type: string
description: |
  The new database name of the collection. If a new database name is not
  specified, the current database will be used. If the new name specifies a
  different database, the command copies the collection to the new database
  and drops the source collection.
interface: phpmethod
operation: ~
optional: true
---
source:
  file: apiargs-common-param.yaml
  ref: $options
...
