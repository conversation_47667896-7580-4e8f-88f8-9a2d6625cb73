arg_name: param
name: $indexes
type: array
description: |
  The indexes to create on the collection.

  For example, the following specifies a unique index on the ``username`` field
  and a compound index on the ``email`` and ``createdAt`` fields:

  .. code-block:: php

     [
         [ 'key' => [ 'username' => -1 ], 'unique' => true ],
         [ 'key' => [ 'email' => 1, 'createdAt' => 1 ] ],
     ]
interface: phpmethod
operation: ~
optional: false
---
source:
  file: apiargs-common-param.yaml
  ref: $options
...
