source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: upsert
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: bypassDocumentValidation
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: collation
---
source:
  file: apiargs-common-option.yaml
  ref: comment
post: |
  This is not supported for server versions prior to 4.4 and will result in an
  exception at execution time if used.

  .. versionadded:: 1.13
---
source:
  file: apiargs-common-option.yaml
  ref: hint
post: |
  This option is available in MongoDB 4.2+ and will result in an exception at
  execution time if specified for an older server version.

  .. versionadded:: 1.6
---
source:
  file: apiargs-common-option.yaml
  ref: let
post: |
  .. versionadded:: 1.13
---
source:
  file: apiargs-common-option.yaml
  ref: session
post: |
  .. versionadded:: 1.3
---
source:
  file: apiargs-MongoDBCollection-common-option.yaml
  ref: writeConcern
...
