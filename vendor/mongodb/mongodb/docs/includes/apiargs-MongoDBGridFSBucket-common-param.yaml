arg_name: param
name: $filename
type: string
description: |
  The ``filename`` of the file{{action}}.
interface: phpmethod
operation: ~
optional: false
replacement:
  action: ""
---
arg_name: param
name: $id
type: mixed
description: |
  The ``_id`` of the file{{action}}.
interface: phpmethod
operation: ~
optional: false
replacement:
  action: ""
---
arg_name: param
name: $stream
type: resource
description: |
  The GridFS stream resource.
interface: phpmethod
operation: ~
---
arg_name: param
name: $destination
type: resource
description: |
  Writable stream, to which the GridFS file's contents will be written.
interface: phpmethod
operation: ~
optional: false
...
