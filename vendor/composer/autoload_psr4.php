<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'RCF\\Tests\\' => array($baseDir . '/tests'),
    'RCF\\Repositories\\' => array($baseDir . '/src/Repositories'),
    'RCF\\Models\\' => array($baseDir . '/src/Models'),
    'RCF\\Database\\' => array($baseDir . '/src/Database'),
    'RCF\\' => array($baseDir . '/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'MongoDB\\' => array($vendorDir . '/mongodb/mongodb/src'),
    'Jean85\\' => array($vendorDir . '/jean85/pretty-package-versions/src'),
);
