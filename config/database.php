<?php

return [
    'mongodb' => [
        // MongoDB connection settings
        'connection' => [
            'uri' => $_ENV['MONGODB_URI'] ?? 'mongodb://localhost:27017',
            'database' => $_ENV['MONGODB_DATABASE'] ?? 'rcf_furnishing',
            'options' => [
                'connectTimeoutMS' => 5000,
                'socketTimeoutMS' => 30000,
                'maxPoolSize' => 10,
                'minPoolSize' => 1,
                'maxIdleTimeMS' => 30000,
                'waitQueueTimeoutMS' => 5000,
                'retryWrites' => true,
                'retryReads' => true,
            ],
        ],
        
        // Collection names
        'collections' => [
            'categories' => 'categories',
            'products' => 'products',
            'users' => 'users',
            'orders' => 'orders',
            'reviews' => 'reviews',
            'vendors' => 'vendors',
            'carts' => 'carts',
            'wishlists' => 'wishlists',
        ],
        
        // Database indexes configuration
        'indexes' => [
            'categories' => [
                ['key' => ['slug' => 1], 'unique' => true],
                ['key' => ['status' => 1]],
                ['key' => ['created_at' => -1]],
            ],
            'products' => [
                ['key' => ['slug' => 1], 'unique' => true],
                ['key' => ['category_id' => 1]],
                ['key' => ['status' => 1]],
                ['key' => ['price' => 1]],
                ['key' => ['created_at' => -1]],
                ['key' => ['name' => 'text', 'description' => 'text']],
            ],
            'users' => [
                ['key' => ['email' => 1], 'unique' => true],
                ['key' => ['status' => 1]],
                ['key' => ['created_at' => -1]],
            ],
            'orders' => [
                ['key' => ['user_id' => 1]],
                ['key' => ['status' => 1]],
                ['key' => ['created_at' => -1]],
                ['key' => ['order_number' => 1], 'unique' => true],
            ],
        ],
    ],
    
    // Logging configuration
    'logging' => [
        'enabled' => $_ENV['DB_LOGGING_ENABLED'] ?? true,
        'level' => $_ENV['DB_LOG_LEVEL'] ?? 'info', // debug, info, warning, error
        'file' => $_ENV['DB_LOG_FILE'] ?? __DIR__ . '/../storage/logs/database.log',
        'max_files' => 30,
    ],
    
    // Cache configuration for database operations
    'cache' => [
        'enabled' => $_ENV['DB_CACHE_ENABLED'] ?? false,
        'ttl' => $_ENV['DB_CACHE_TTL'] ?? 3600, // 1 hour
        'prefix' => 'rcf_db_',
    ],
    
    // Migration settings
    'migrations' => [
        'collection' => 'migrations',
        'path' => __DIR__ . '/../database/migrations',
    ],
];
