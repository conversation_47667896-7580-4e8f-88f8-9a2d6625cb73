<?php

return [
    'meta' => [
        'title' => 'RC Furnishing',
        'description' => 'RC furnishings provides full hi tech funishing solutions.',
        'keywords' => 'Furnishings, home decor',
    ],
    'brand' => [
        'name' => 'RC Furnishing',
        'logos' => [
            'primary' => 'assets/images/logo/logo-01.svg',
            'secondary' => 'assets/images/logo/logo-02.svg',
            'app' => 'assets/images/logo/logo-01.svg',
        ],
        'favicon' => 'assets/images/fav.png',
    ],
    'layout' => [
        'default_body_class' => 'shop-main-h',
    ],
    'contact' => [
        'support_phone_display' => '+258 3268 21485',
        'support_phone_href' => 'tel:+258326821485',
        'support_email' => '<EMAIL>',
        'discount_message' => 'FREE delivery & 40% Discount for next 3 orders! Place your 1st order in.',
        'support_hours' => 'We deliver to your everyday from 7:00 to 22:00',
        'countdown_target' => '10/05/2025 10:20:00',
        'cta_phone_label' => '(+2) 580 123 456 789',
        'cta_phone_href' => 'tel:(+2)580123456789',
        'cta_email_label' => '<EMAIL>',
        'cta_email_href' => 'mailto:<EMAIL>',
    ],
    'navigation' => [
        'welcome_message' => 'Welcome to our Organic store RC Furnishing!',
        'header_top_links' => [
            ['label' => 'Track Order', 'href' => 'trackorder.php'],
            ['label' => 'About Us', 'href' => 'about.php'],
            ['label' => 'Contact', 'href' => 'contact.php'],
            ['label' => 'FAQ', 'href' => 'faq.php'],
        ],
        'mid_left_links' => [
            ['label' => 'About Us', 'href' => 'about.php'],
            ['label' => 'My Account', 'href' => 'account.php'],
            ['label' => 'Wishlist', 'href' => 'wishlist.php'],
        ],
        'language' => [
            'current' => 'English',
            'options' => ['Italian', 'Russian', 'Chinian'],
        ],
        'currency' => [
            'current' => 'USD',
            'options' => ['Rubol', 'Rupi', 'Euro'],
        ],
        'mid_right_links' => [
            ['label' => 'Track Order', 'href' => 'trackorder.php'],
        ],
        'main_menu' => [
            [
                'label' => 'Home',
                'href' => '#',
                'class' => 'parent has-dropdown',
                'submenu' => [
                    ['label' => 'Home One', 'href' => 'index.php'],
                    ['label' => 'Home Two', 'href' => 'index-two.php'],
                    ['label' => 'Home Three', 'href' => 'index-three.php'],
                    ['label' => 'Home Four', 'href' => 'index-four.php'],
                    ['label' => 'Home Five', 'href' => 'index-five.php'],
                ],
            ],
            [
                'label' => 'About',
                'href' => 'about.php',
                'class' => 'parent',
            ],
            [
                'label' => 'Shop',
                'href' => '#',
                'class' => 'parent with-megamenu',
                'megamenu' => [
                    'columns' => [
                        [
                            'title' => 'Shop Layout',
                            'links' => [
                                ['label' => 'Shop Grid Sidebar', 'href' => 'shop-grid-sidebar.php'],
                                ['label' => 'Shop List Sidebar', 'href' => 'shop-list-sidebar.php'],
                                ['label' => 'Shop Top Filter Grid', 'href' => 'shop-grid-top-filter.php'],
                                ['label' => 'Shop Top Filter List', 'href' => 'shop-list-top-filter.php'],
                            ],
                        ],
                        [
                            'title' => 'Shop Details',
                            'links' => [
                                ['label' => 'Shop Details', 'href' => 'shop-details.php'],
                                ['label' => 'Shop Details V2', 'href' => 'shop-details-2.php'],
                                ['label' => 'Shop Details V3', 'href' => 'shop-details-right-sidebar.php'],
                                ['label' => 'Shop Details V4', 'href' => 'shop-details-4.php'],
                            ],
                        ],
                        [
                            'title' => 'Product Feature',
                            'links' => [
                                ['label' => 'Variable Product', 'href' => 'shop-details-variable.php'],
                                ['label' => 'Affiliate Product', 'href' => 'shop-details-affiliats.php'],
                                ['label' => 'Shop Details Group', 'href' => 'shop-details-group.php'],
                                ['label' => 'Shop Compare', 'href' => 'shop-compare.php'],
                            ],
                        ],
                        [
                            'title' => 'Shop Others',
                            'links' => [
                                ['label' => 'Cart', 'href' => 'cart.php'],
                                ['label' => 'Checkout', 'href' => 'checkout.php'],
                                ['label' => 'Track Order', 'href' => 'trackorder.php'],
                            ],
                        ],
                    ],
                    'feature' => [
                        'href' => 'shop-grid-sidebar.php',
                        'image' => 'assets/images/feature/05.jpg',
                        'alt' => 'feature_product',
                    ],
                ],
            ],
            [
                'label' => 'Vendors',
                'href' => '#',
                'class' => 'parent has-dropdown',
                'submenu' => [
                    ['label' => 'Vendor List', 'href' => 'vendor-list.php'],
                    ['label' => 'Vendor Grid', 'href' => 'vendor-grid.php'],
                    ['label' => 'Vendor Details', 'href' => 'vendor-details.php'],
                ],
            ],
            [
                'label' => 'Pages',
                'href' => '#',
                'class' => 'parent has-dropdown',
                'submenu' => [
                    ['label' => 'About', 'href' => 'about.php'],
                    ['label' => 'Store', 'href' => 'store.php'],
                    ['label' => 'Faq\'s', 'href' => 'faq.php'],
                    ['label' => 'Invoice', 'href' => 'invoice.php'],
                    ['label' => 'Contact', 'href' => 'contact.php'],
                    ['label' => 'Register', 'href' => 'register.php'],
                    ['label' => 'Login', 'href' => 'login.php'],
                    ['label' => 'Privacy Policy', 'href' => 'privacy-policy.php'],
                    ['label' => 'Cookies Policy', 'href' => 'cookies-policy.php'],
                    ['label' => 'Terms & Condition', 'href' => 'terms-condition.php'],
                    ['label' => 'Error', 'href' => '404.php'],
                ],
            ],
            [
                'label' => 'Blog',
                'href' => '#',
                'class' => 'parent has-dropdown',
                'submenu' => [
                    ['label' => 'Blog', 'href' => 'blog.php'],
                    ['label' => 'Blog List Right Sidebar', 'href' => 'blog-list-left-sidebar.php'],
                    ['label' => 'Blog List Left Sidebar', 'href' => 'blog-list-right-sidebar.php'],
                    ['label' => 'Blog Details', 'href' => 'blog-details.php'],
                ],
            ],
            [
                'label' => 'Contact',
                'href' => 'contact.php',
                'class' => 'parent',
            ],
        ],
        'nav_cta' => [
            'pill' => ['label' => 'Trending Products', 'href' => '#', 'class' => 'btn-narrow'],
            'button' => ['label' => 'Get 30% Discount Now', 'href' => '#', 'badge' => 'Sale', 'class' => 'rts-btn btn-primary'],
        ],
        'categories' => [
            [
                'label' => 'Breakfast & Dairy',
                'icon' => 'assets/images/icons/01.svg',
                'submenu' => ['Breakfast', 'Dinner', 'Pumking'],
            ],
            [
                'label' => 'Meats & Seafood',
                'icon' => 'assets/images/icons/02.svg',
                'submenu' => ['Breakfast', 'Dinner', 'Pumking'],
            ],
            [
                'label' => 'Breads & Bakery',
                'icon' => 'assets/images/icons/03.svg',
            ],
            [
                'label' => 'Chips & Snacks',
                'icon' => 'assets/images/icons/04.svg',
                'submenu' => ['Breakfast', 'Dinner', 'Pumking'],
            ],
            [
                'label' => 'Medical Healthcare',
                'icon' => 'assets/images/icons/05.svg',
            ],
            [
                'label' => 'Breads & Bakery',
                'icon' => 'assets/images/icons/06.svg',
            ],
            [
                'label' => 'Biscuits & Snacks',
                'icon' => 'assets/images/icons/07.svg',
                'submenu' => ['Breakfast', 'Dinner', 'Pumking'],
            ],
            [
                'label' => 'Frozen Foods',
                'icon' => 'assets/images/icons/08.svg',
            ],
            [
                'label' => 'Grocery & Staples',
                'icon' => 'assets/images/icons/09.svg',
            ],
            [
                'label' => 'Other Items',
                'icon' => 'assets/images/icons/10.svg',
            ],
        ],
        'mobile_contact' => [
            ['icon' => 'fa-light fa-headset', 'href' => 'tel:+258326821485', 'label' => '+258 3268 21485'],
            ['icon' => 'fa-light fa-envelope', 'href' => 'mailto:<EMAIL>', 'label' => '<EMAIL>'],
        ],
    ],
    'search' => [
        'placeholder' => 'Search for products, categories or brands',
        'overlay_placeholder' => 'Search by keyword or #',
    ],
    'buttons' => [
        'search' => [
            'label' => 'Search',
            'class' => 'rts-btn btn-primary radious-sm with-icon',
            'text_wrapper' => 'div',
            'icons' => [
                ['class' => 'fa-light fa-magnifying-glass'],
                ['class' => 'fa-light fa-magnifying-glass'],
            ],
        ],
        'shop_now_primary' => [
            'label' => 'Shop Now',
            'href' => 'shop-grid-sidebar.php',
            'class' => 'rts-btn btn-primary radious-sm with-icon',
            'icons' => [
                ['class' => 'fa-light fa-arrow-right'],
                ['class' => 'fa-light fa-arrow-right'],
            ],
        ],
        'details' => [
            'label' => 'Details',
            'class' => 'rts-btn btn-primary radious-sm with-icon',
            'icons' => [
                ['class' => 'fa-regular fa-paper-plane'],
                ['class' => 'fa-regular fa-paper-plane'],
            ],
        ],
    ],
    'database' => [
        'config_file' => __DIR__ . '/database.php',
    ],
];
