<?php

require_once __DIR__ . '/FileUploadServiceTest.php';

echo "Running File Upload Service Tests...\n";
echo "===================================\n\n";

$test = new FileUploadServiceTest();
$results = $test->runAllTests();

$passed = 0;
$failed = 0;

foreach ($results as $testName => $result) {
    $status = $result['passed'] ? '✓ PASS' : '✗ FAIL';
    $color = $result['passed'] ? "\033[32m" : "\033[31m";
    $reset = "\033[0m";
    
    echo "{$color}{$status}{$reset} {$testName}: {$result['message']}\n";
    
    if ($result['passed']) {
        $passed++;
    } else {
        $failed++;
        if (isset($result['test_results'])) {
            foreach ($result['test_results'] as $subTest => $subResult) {
                $subStatus = $subResult ? '  ✓' : '  ✗';
                echo "    {$subStatus} {$subTest}\n";
            }
        }
    }
}

echo "\n===================================\n";
echo "Tests completed: " . ($passed + $failed) . "\n";
echo "\033[32mPassed: {$passed}\033[0m\n";
echo "\033[31mFailed: {$failed}\033[0m\n";

if ($failed === 0) {
    echo "\n🎉 All tests passed!\n";
    exit(0);
} else {
    echo "\n❌ Some tests failed.\n";
    exit(1);
}
