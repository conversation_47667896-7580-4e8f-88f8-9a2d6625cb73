<?php

/**
 * MongoDB Integration Test Suite
 * 
 * Comprehensive tests for MongoDB implementation
 */

require_once __DIR__ . '/../bootstrap.php';

class MongoDBIntegrationTest
{
    private array $testResults = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    
    public function runAllTests(): void
    {
        echo "🧪 MongoDB Integration Test Suite\n";
        echo "================================\n\n";
        
        $this->testDatabaseConnection();
        $this->testCategoryOperations();
        $this->testProductOperations();
        $this->testSearchAndFiltering();
        $this->testErrorHandling();
        $this->testPerformance();
        $this->testDataConsistency();
        
        $this->printSummary();
    }
    
    private function testDatabaseConnection(): void
    {
        $this->test('Database Connection', function() {
            $connection = getMongoConnection();
            
            $this->assert($connection !== null, 'Connection object created');
            $this->assert($connection->testConnection(), 'Connection test successful');
            $this->assert(is_mongodb_available(), 'MongoDB is available');
            $this->assert(!$connection->isUsingMock(), 'Using real MongoDB (not mock)');
            
            return true;
        });
    }
    
    private function testCategoryOperations(): void
    {
        $this->test('Category CRUD Operations', function() {
            $repo = getCategoryRepository();
            
            // Create
            $categoryData = [
                'name' => 'Test Category ' . time(),
                'slug' => 'test-category-' . time(),
                'description' => 'Test category description',
                'status' => 'active'
            ];
            
            $categoryId = $repo->createCategory($categoryData);
            $this->assert($categoryId !== null, 'Category created successfully');
            
            // Read
            $category = $repo->findById($categoryId);
            $this->assert($category !== null, 'Category found by ID');
            $this->assert($category['name'] === $categoryData['name'], 'Category name matches');
            
            // Update
            $updateData = ['description' => 'Updated description'];
            $updated = $repo->updateById($categoryId, $updateData);
            $this->assert($updated, 'Category updated successfully');
            
            // Verify update
            $updatedCategory = $repo->findById($categoryId);
            $this->assert($updatedCategory['description'] === 'Updated description', 'Update verified');
            
            // Delete
            $deleted = $repo->deleteById($categoryId);
            $this->assert($deleted, 'Category deleted successfully');
            
            // Verify deletion
            $deletedCategory = $repo->findById($categoryId);
            $this->assert($deletedCategory === null, 'Category deletion verified');
            
            return true;
        });
    }
    
    private function testProductOperations(): void
    {
        $this->test('Product CRUD Operations', function() {
            $productRepo = getProductRepository();
            $categoryRepo = getCategoryRepository();
            
            // Create a test category first
            $categoryId = $categoryRepo->createCategory([
                'name' => 'Test Product Category',
                'slug' => 'test-product-category-' . time(),
                'status' => 'active'
            ]);
            
            // Create product
            $productData = [
                'name' => 'Test Product ' . time(),
                'slug' => 'test-product-' . time(),
                'description' => 'Test product description',
                'price' => 99.99,
                'category_id' => $categoryId,
                'stock_quantity' => 10,
                'status' => 'active'
            ];
            
            $productId = $productRepo->createProduct($productData);
            $this->assert($productId !== null, 'Product created successfully');
            
            // Test stock operations
            $stockUpdated = $productRepo->updateStock($productId, 15, 'set');
            $this->assert($stockUpdated, 'Stock updated successfully');
            
            $stockIncremented = $productRepo->updateStock($productId, 5, 'increment');
            $this->assert($stockIncremented, 'Stock incremented successfully');
            
            // Verify stock
            $product = $productRepo->findById($productId);
            $this->assert($product['stock_quantity'] === 20, 'Stock quantity correct');
            
            // Test product search
            $searchResults = $productRepo->searchProducts('Test Product');
            $this->assert(count($searchResults) >= 1, 'Product search works');
            
            // Cleanup
            $productRepo->deleteById($productId);
            $categoryRepo->deleteById($categoryId);
            
            return true;
        });
    }
    
    private function testSearchAndFiltering(): void
    {
        $this->test('Search and Filtering', function() {
            $categoryRepo = getCategoryRepository();
            
            // Test category search
            $activeCategories = $categoryRepo->getActiveCategories();
            $this->assert(is_array($activeCategories), 'Active categories returned as array');
            
            // Test search by name
            $searchResults = $categoryRepo->searchByName('Electronics');
            $this->assert(is_array($searchResults), 'Search results returned as array');
            
            // Test statistics
            $stats = $categoryRepo->getStatistics();
            $this->assert(isset($stats['active']), 'Statistics include active count');
            $this->assert(is_int($stats['active']), 'Active count is integer');
            
            return true;
        });
    }
    
    private function testErrorHandling(): void
    {
        $this->test('Error Handling', function() {
            $repo = getCategoryRepository();
            
            // Test validation error
            try {
                $repo->createCategory(['invalid' => 'data']);
                $this->assert(false, 'Should have thrown validation error');
            } catch (InvalidArgumentException $e) {
                $this->assert(true, 'Validation error caught correctly');
            }
            
            // Test duplicate slug error
            $testSlug = 'duplicate-test-' . time();
            $repo->createCategory([
                'name' => 'First Category',
                'slug' => $testSlug,
                'status' => 'active'
            ]);
            
            try {
                $repo->createCategory([
                    'name' => 'Second Category',
                    'slug' => $testSlug,
                    'status' => 'active'
                ]);
                $this->assert(false, 'Should have thrown duplicate error');
            } catch (InvalidArgumentException $e) {
                $this->assert(true, 'Duplicate slug error caught correctly');
            }
            
            return true;
        });
    }
    
    private function testPerformance(): void
    {
        $this->test('Performance', function() {
            $start = microtime(true);
            
            $repo = getCategoryRepository();
            
            // Perform multiple operations
            for ($i = 0; $i < 10; $i++) {
                $categories = $repo->getActiveCategories();
            }
            
            $end = microtime(true);
            $duration = ($end - $start) * 1000; // Convert to milliseconds
            
            $this->assert($duration < 5000, 'Operations completed within 5 seconds');
            echo "    Performance: " . number_format($duration, 2) . "ms for 10 operations\n";
            
            return true;
        });
    }
    
    private function testDataConsistency(): void
    {
        $this->test('Data Consistency', function() {
            $repo = getCategoryRepository();
            
            // Create category
            $categoryId = $repo->createCategory([
                'name' => 'Consistency Test',
                'slug' => 'consistency-test-' . time(),
                'status' => 'active'
            ]);
            
            // Read immediately
            $category1 = $repo->findById($categoryId);
            
            // Read again
            $category2 = $repo->findById($categoryId);
            
            $this->assert($category1['id'] === $category2['id'], 'Data consistent across reads');
            $this->assert($category1['name'] === $category2['name'], 'Name consistent');
            
            // Cleanup
            $repo->deleteById($categoryId);
            
            return true;
        });
    }
    
    private function test(string $name, callable $testFunction): void
    {
        $this->totalTests++;
        echo "Testing: {$name}... ";
        
        try {
            $result = $testFunction();
            if ($result) {
                echo "✅ PASS\n";
                $this->passedTests++;
                $this->testResults[$name] = 'PASS';
            } else {
                echo "❌ FAIL\n";
                $this->testResults[$name] = 'FAIL';
            }
        } catch (Exception $e) {
            echo "❌ ERROR: " . $e->getMessage() . "\n";
            $this->testResults[$name] = 'ERROR: ' . $e->getMessage();
        }
    }
    
    private function assert(bool $condition, string $message): void
    {
        if (!$condition) {
            throw new Exception("Assertion failed: {$message}");
        }
    }
    
    private function printSummary(): void
    {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "TEST SUMMARY\n";
        echo str_repeat("=", 50) . "\n";
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests}\n";
        echo "Failed: " . ($this->totalTests - $this->passedTests) . "\n";
        echo "Success Rate: " . round(($this->passedTests / $this->totalTests) * 100, 2) . "%\n";
        
        if ($this->passedTests === $this->totalTests) {
            echo "\n🎉 ALL TESTS PASSED! MongoDB integration is working perfectly.\n";
        } else {
            echo "\n❌ Some tests failed. Please review the results above.\n";
        }
    }
}

// Run tests if this file is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new MongoDBIntegrationTest();
    $test->runAllTests();
}
