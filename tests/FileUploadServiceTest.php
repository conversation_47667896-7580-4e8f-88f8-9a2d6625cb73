<?php

require_once __DIR__ . '/../components/shared/FileUploadService.php';

/**
 * Test suite for FileUploadService
 * 
 * Tests unique naming strategies, collision handling, and edge cases
 */
class FileUploadServiceTest
{
    private string $testDir;
    private FileUploadService $service;

    public function __construct()
    {
        $this->testDir = sys_get_temp_dir() . '/rcf-upload-tests-' . uniqid();
        $this->setupTestEnvironment();
    }

    public function __destruct()
    {
        $this->cleanupTestEnvironment();
    }

    /**
     * Run all tests
     */
    public function runAllTests(): array
    {
        $results = [];
        
        $results['testTimestampNaming'] = $this->testTimestampNaming();
        $results['testUuidNaming'] = $this->testUuidNaming();
        $results['testHashNaming'] = $this->testHashNaming();
        $results['testHybridNaming'] = $this->testHybridNaming();
        $results['testCollisionHandling'] = $this->testCollisionHandling();
        $results['testConcurrentUploads'] = $this->testConcurrentUploads();
        $results['testInvalidFiles'] = $this->testInvalidFiles();
        $results['testFilenameValidation'] = $this->testFilenameValidation();
        $results['testExtensionDetection'] = $this->testExtensionDetection();
        $results['testFallbackNaming'] = $this->testFallbackNaming();

        return $results;
    }

    /**
     * Test timestamp-based naming strategy
     */
    public function testTimestampNaming(): array
    {
        $testFile = $this->createTestFile('test.jpg');
        
        $result = $this->service->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'test.jpg'],
            FileUploadService::STRATEGY_TIMESTAMP,
            'test-image'
        );

        $success = $result['success'] && 
                  isset($result['filename']) &&
                  preg_match('/^test-image-\d{8}-\d{6}-\d{6}\.jpg$/', $result['filename']);

        return [
            'passed' => $success,
            'message' => $success ? 'Timestamp naming works correctly' : 'Timestamp naming failed',
            'filename' => $result['filename'] ?? null
        ];
    }

    /**
     * Test UUID-based naming strategy
     */
    public function testUuidNaming(): array
    {
        $testFile = $this->createTestFile('test.png');
        
        $result = $this->service->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'test.png'],
            FileUploadService::STRATEGY_UUID,
            'test-image'
        );

        $success = $result['success'] && 
                  isset($result['filename']) &&
                  preg_match('/^test-image-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\.png$/', $result['filename']);

        return [
            'passed' => $success,
            'message' => $success ? 'UUID naming works correctly' : 'UUID naming failed',
            'filename' => $result['filename'] ?? null
        ];
    }

    /**
     * Test hash-based naming strategy
     */
    public function testHashNaming(): array
    {
        $testFile = $this->createTestFile('test.gif', 'unique content for hash');
        
        $result = $this->service->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'test.gif'],
            FileUploadService::STRATEGY_HASH,
            'test-image'
        );

        $success = $result['success'] && 
                  isset($result['filename']) &&
                  preg_match('/^test-image-[0-9a-f]{16}\.gif$/', $result['filename']);

        return [
            'passed' => $success,
            'message' => $success ? 'Hash naming works correctly' : 'Hash naming failed',
            'filename' => $result['filename'] ?? null
        ];
    }

    /**
     * Test hybrid naming strategy
     */
    public function testHybridNaming(): array
    {
        $testFile = $this->createTestFile('test.webp');
        
        $result = $this->service->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'test.webp'],
            FileUploadService::STRATEGY_HYBRID,
            'test-image'
        );

        $success = $result['success'] && 
                  isset($result['filename']) &&
                  preg_match('/^test-image-\d{8}-\d{6}-[0-9a-f]{6}\.webp$/', $result['filename']);

        return [
            'passed' => $success,
            'message' => $success ? 'Hybrid naming works correctly' : 'Hybrid naming failed',
            'filename' => $result['filename'] ?? null
        ];
    }

    /**
     * Test collision handling
     */
    public function testCollisionHandling(): array
    {
        $filenames = [];
        $collisions = 0;

        // Upload multiple files with same content to test collision handling
        for ($i = 0; $i < 5; $i++) {
            $testFile = $this->createTestFile("test{$i}.jpg", 'same content');
            
            $result = $this->service->uploadFile(
                ['tmp_name' => $testFile, 'name' => "test{$i}.jpg"],
                FileUploadService::STRATEGY_TIMESTAMP,
                'collision-test'
            );

            if ($result['success']) {
                $filename = $result['filename'];
                if (in_array($filename, $filenames)) {
                    $collisions++;
                }
                $filenames[] = $filename;
            }
        }

        $success = count($filenames) === 5 && $collisions === 0;

        return [
            'passed' => $success,
            'message' => $success ? 'Collision handling works correctly' : "Found {$collisions} collisions",
            'filenames' => $filenames
        ];
    }

    /**
     * Test concurrent uploads simulation
     */
    public function testConcurrentUploads(): array
    {
        $filenames = [];
        $processes = [];

        // Simulate concurrent uploads by creating multiple files quickly
        for ($i = 0; $i < 10; $i++) {
            $testFile = $this->createTestFile("concurrent{$i}.jpg");
            
            $result = $this->service->uploadFile(
                ['tmp_name' => $testFile, 'name' => "concurrent{$i}.jpg"],
                FileUploadService::STRATEGY_HYBRID,
                'concurrent-test'
            );

            if ($result['success']) {
                $filenames[] = $result['filename'];
            }
        }

        $uniqueFilenames = array_unique($filenames);
        $success = count($filenames) === count($uniqueFilenames) && count($filenames) === 10;

        return [
            'passed' => $success,
            'message' => $success ? 'Concurrent uploads handled correctly' : 'Duplicate filenames in concurrent uploads',
            'total_files' => count($filenames),
            'unique_files' => count($uniqueFilenames)
        ];
    }

    /**
     * Test invalid file handling
     */
    public function testInvalidFiles(): array
    {
        $tests = [
            'non_existent_file' => [
                'file' => ['tmp_name' => '/non/existent/file.jpg', 'name' => 'test.jpg'],
                'should_fail' => true
            ],
            'empty_filename' => [
                'file' => ['tmp_name' => $this->createTestFile('test.jpg'), 'name' => ''],
                'should_fail' => false // Should still work with extension detection
            ]
        ];

        $results = [];
        foreach ($tests as $testName => $test) {
            $result = $this->service->uploadFile($test['file'], FileUploadService::STRATEGY_TIMESTAMP, 'invalid-test');
            $passed = $test['should_fail'] ? !$result['success'] : $result['success'];
            $results[$testName] = $passed;
        }

        $allPassed = !in_array(false, $results, true);

        return [
            'passed' => $allPassed,
            'message' => $allPassed ? 'Invalid file handling works correctly' : 'Some invalid file tests failed',
            'test_results' => $results
        ];
    }

    /**
     * Test filename validation
     */
    public function testFilenameValidation(): array
    {
        $testCases = [
            'valid-filename-123.jpg' => true,
            'invalid filename with spaces.jpg' => false,
            'valid_filename_with_underscores.png' => true,
            'invalid/filename/with/slashes.gif' => false,
            'valid-filename.webp' => true,
        ];

        $results = [];
        foreach ($testCases as $filename => $shouldBeValid) {
            // Use reflection to test private method
            $reflection = new ReflectionClass($this->service);
            $method = $reflection->getMethod('isValidStoredFilename');
            $method->setAccessible(true);
            
            $isValid = $method->invoke($this->service, $filename);
            $results[$filename] = ($isValid === $shouldBeValid);
        }

        $allPassed = !in_array(false, $results, true);

        return [
            'passed' => $allPassed,
            'message' => $allPassed ? 'Filename validation works correctly' : 'Some filename validation tests failed',
            'test_results' => $results
        ];
    }

    /**
     * Test extension detection
     */
    public function testExtensionDetection(): array
    {
        $testFile = $this->createTestFile('test', 'test content'); // No extension
        
        // Create a file with JPEG header
        $jpegHeader = "\xFF\xD8\xFF\xE0";
        file_put_contents($testFile, $jpegHeader . str_repeat('x', 100));
        
        $result = $this->service->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'test'],
            FileUploadService::STRATEGY_TIMESTAMP,
            'extension-test'
        );

        // Should fail because we can't determine extension reliably
        $success = !$result['success'];

        return [
            'passed' => $success,
            'message' => $success ? 'Extension detection works correctly' : 'Extension detection failed',
            'error' => $result['error'] ?? null
        ];
    }

    /**
     * Test fallback naming when all attempts fail
     */
    public function testFallbackNaming(): array
    {
        // Create a service with very low max attempts to trigger fallback
        $limitedService = new FileUploadService($this->getUploadLocations(), [
            'max_naming_attempts' => 1
        ]);

        // Create a file that already exists to force collision
        $testFile = $this->createTestFile('fallback.jpg');
        $existingFile = $this->testDir . '/test-image-' . date('Ymd-His') . '.jpg';
        touch($existingFile);

        $result = $limitedService->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'fallback.jpg'],
            FileUploadService::STRATEGY_TIMESTAMP,
            'test-image'
        );

        $success = $result['success'] && 
                  isset($result['filename']) &&
                  strpos($result['filename'], 'fallback') !== false;

        return [
            'passed' => $success,
            'message' => $success ? 'Fallback naming works correctly' : 'Fallback naming failed',
            'filename' => $result['filename'] ?? null
        ];
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment(): void
    {
        if (!is_dir($this->testDir)) {
            mkdir($this->testDir, 0755, true);
        }

        $this->service = new FileUploadService($this->getUploadLocations());
    }

    /**
     * Get upload locations for testing
     */
    private function getUploadLocations(): array
    {
        return [
            [
                'disk' => 'test',
                'absolute' => $this->testDir,
                'public' => 'test'
            ]
        ];
    }

    /**
     * Create a test file
     */
    private function createTestFile(string $name, string $content = 'test content'): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'rcf_test_');
        file_put_contents($tempFile, $content);
        return $tempFile;
    }

    /**
     * Cleanup test environment
     */
    private function cleanupTestEnvironment(): void
    {
        if (is_dir($this->testDir)) {
            $files = glob($this->testDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            rmdir($this->testDir);
        }
    }
}
