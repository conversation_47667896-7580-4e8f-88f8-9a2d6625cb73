# File Upload System Documentation

## Overview

The RCF File Upload System provides robust, production-ready file upload functionality with unique naming strategies to prevent conflicts. The system is designed to handle concurrent uploads, prevent naming collisions, and ensure file uniqueness across multiple upload scenarios.

## Key Features

- **Multiple Naming Strategies**: Timestamp, UUID, Hash-based, and Hybrid naming
- **Collision Prevention**: Automatic collision detection and resolution
- **Concurrent Upload Support**: Handles multiple simultaneous uploads safely
- **Fallback Mechanisms**: Multiple storage locations and naming fallbacks
- **Security**: File validation, MIME type checking, and path sanitization
- **Extensible**: Easy to add new naming strategies and validation rules

## Architecture

### Core Components

1. **FileUploadService** - Main service handling file uploads and naming
2. **ProductService** - Product-specific upload handling
3. **CategoryService** - Category-specific upload handling (enhanced)
4. **ProductValidator** - Validation for product data and images
5. **Serving Endpoints** - Secure file delivery (`serve-product-image.php`, `serve-category-image.php`)

### Naming Strategies

#### 1. Timestamp Strategy (`STRATEGY_TIMESTAMP`)
- Format: `prefix-YYYYMMDD-HHMMSS-microseconds.ext`
- Example: `product-image-20240919-143022-123456.jpg`
- Best for: Sequential uploads, debugging, chronological ordering

#### 2. UUID Strategy (`STRATEGY_UUID`)
- Format: `prefix-uuid.ext`
- Example: `product-image-550e8400-e29b-41d4-a716-************.jpg`
- Best for: Maximum uniqueness, distributed systems

#### 3. Hash Strategy (`STRATEGY_HASH`)
- Format: `prefix-hash.ext`
- Example: `product-image-a1b2c3d4e5f6g7h8.jpg`
- Best for: Deduplication, content-based naming

#### 4. Hybrid Strategy (`STRATEGY_HYBRID`) - **Recommended**
- Format: `prefix-YYYYMMDD-HHMMSS-random.ext`
- Example: `product-image-20240919-143022-abc123.jpg`
- Best for: Balance of readability and uniqueness

## Usage Examples

### Basic Product Upload

```php
require_once 'components/admin/products/index.php';

$productRepository = getProductRepository();
$productService = new ProductService($productRepository);

// Handle form submission
$formData = $_POST;
$imageUpload = $_FILES['product_image'] ?? null;

$result = $productService->createProduct($formData, $imageUpload);

if ($result['success']) {
    echo "Product created successfully!";
    echo "Product ID: " . $result['product_id'];
} else {
    echo "Errors: " . implode(', ', $result['errors']);
}
```

### Direct File Upload Service Usage

```php
require_once 'components/shared/FileUploadService.php';

$uploadLocations = [
    [
        'disk' => 'assets',
        'absolute' => '/path/to/assets/uploads/products',
        'public' => 'assets/uploads/products'
    ]
];

$fileUploadService = new FileUploadService($uploadLocations);

$result = $fileUploadService->uploadFile(
    $_FILES['image'],
    FileUploadService::STRATEGY_HYBRID,
    'product-image'
);

if ($result['success']) {
    echo "File uploaded: " . $result['path'];
    echo "Filename: " . $result['filename'];
} else {
    echo "Upload failed: " . $result['error'];
}
```

### Custom Configuration

```php
$config = [
    'max_naming_attempts' => 15,
    'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    'max_file_size' => 5 * 1024 * 1024, // 5MB
];

$fileUploadService = new FileUploadService($uploadLocations, $config);
```

## File Validation

### Supported File Types
- **Images**: JPEG, PNG, GIF, WebP
- **Extensions**: .jpg, .jpeg, .png, .gif, .webp
- **MIME Types**: image/jpeg, image/png, image/gif, image/webp

### Validation Rules
- **File Size**: Configurable (default: 2MB for categories, 5MB for products)
- **Dimensions**: Minimum 50x50px, Maximum 5000x5000px
- **Security**: MIME type validation, file header checking
- **Filename**: Alphanumeric characters, hyphens, underscores only

## Directory Structure

```
assets/uploads/
├── categories/          # Category images
│   └── category-image-20240919-143022.jpg
├── products/           # Product images
│   └── product-image-20240919-143022-abc123.jpg
└── ...

storage/uploads/        # Fallback location
├── categories/
├── products/
└── ...

/tmp/rcf-*-images/     # Emergency fallback
```

## Security Features

### File Serving Security
- **Path Validation**: Prevents directory traversal attacks
- **MIME Type Checking**: Ensures only images are served
- **Filename Validation**: Strict regex patterns for allowed filenames
- **Access Control**: Files served through controlled endpoints

### Upload Security
- **File Type Validation**: Multiple layers of file type checking
- **Size Limits**: Configurable file size restrictions
- **Content Validation**: Image header verification
- **Temporary File Handling**: Secure temporary file management

## Error Handling

### Common Error Scenarios
1. **File Too Large**: Returns specific error message with size limit
2. **Invalid File Type**: Clear message about supported formats
3. **Upload Failed**: Detailed error based on upload error code
4. **Directory Permissions**: Helpful message about permission issues
5. **Naming Conflicts**: Automatic resolution with fallback strategies

### Error Response Format
```php
[
    'success' => false,
    'error' => 'Descriptive error message',
    'errors' => ['field' => 'Field-specific error'] // For form validation
]
```

## Performance Considerations

### Optimization Features
- **Collision Avoidance**: Efficient filename generation reduces retries
- **Multiple Storage Locations**: Fallback options for reliability
- **Caching Headers**: Proper cache headers for served images
- **Range Requests**: Support for partial content delivery

### Recommended Settings
- **max_naming_attempts**: 10-15 (balance between uniqueness and performance)
- **Naming Strategy**: HYBRID (best balance of features)
- **File Size Limits**: Based on your server's upload_max_filesize
- **Storage Locations**: Primary + at least one fallback

## Testing

### Running Tests
```bash
php tests/run_tests.php
```

### Test Coverage
- ✅ Unique naming strategies
- ✅ Collision handling
- ✅ Concurrent uploads
- ✅ File validation
- ✅ Error scenarios
- ✅ Filename validation
- ✅ Extension detection

## Troubleshooting

### Common Issues

#### Upload Fails with "Invalid file data"
- Check if file was actually uploaded (`$_FILES` array)
- Verify `enctype="multipart/form-data"` on form
- Check server upload limits

#### "Unable to create directory" Error
- Verify directory permissions (755 recommended)
- Check parent directory permissions
- Ensure web server has write access

#### Files Not Displaying
- Check serve-*-image.php endpoints are accessible
- Verify file paths in database match actual files
- Check web server configuration for PHP files

#### Naming Collisions Still Occurring
- Increase `max_naming_attempts` in configuration
- Consider using UUID strategy for maximum uniqueness
- Check system clock synchronization for timestamp strategy

### Debug Mode
Enable detailed logging by checking the error messages returned by the service methods. All methods return detailed error information for debugging.

## Migration from Legacy Systems

If you have existing file upload code, follow these steps:

1. **Backup existing files** and database records
2. **Update upload forms** to use new field names and validation
3. **Replace upload handlers** with new service classes
4. **Update file serving** to use new endpoints
5. **Test thoroughly** with various file types and sizes

## Future Enhancements

Planned improvements:
- Image resizing and thumbnail generation
- Cloud storage integration (AWS S3, etc.)
- Advanced image optimization
- Bulk upload support
- Progress tracking for large uploads
