<?php

require_once __DIR__ . '/../bootstrap.php';
require_once __DIR__ . '/../components/admin/products/index.php';

echo "Testing Product Upload Functionality\n";
echo "====================================\n\n";

// Test 1: Create a mock uploaded file
echo "Test 1: Creating test product with mock image...\n";

// Create a temporary image file
$testImagePath = sys_get_temp_dir() . '/test_product_image.jpg';
$imageContent = base64_decode('/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A');
file_put_contents($testImagePath, $imageContent);

// Mock file upload data
$mockFileUpload = [
    'tmp_name' => $testImagePath,
    'name' => 'test_product.jpg',
    'size' => filesize($testImagePath),
    'error' => UPLOAD_ERR_OK,
    'type' => 'image/jpeg'
];

// Mock product data
$productData = [
    'name' => 'Test Product ' . date('Y-m-d H:i:s'),
    'description' => 'This is a test product created by the demo script.',
    'price' => 99.99,
    'sale_price' => 79.99,
    'stock_quantity' => 50,
    'tags' => 'test, demo, product',
    'status' => 'active',
    'featured' => true,
    'sku' => 'TEST-' . uniqid(),
];

try {
    $productRepository = getProductRepository();
    $productService = new ProductService($productRepository);
    
    $result = $productService->createProduct($productData, $mockFileUpload);
    
    if ($result['success']) {
        echo "✓ Product created successfully!\n";
        echo "  Product ID: " . ($result['product_id'] ?? 'N/A') . "\n";
        echo "  Message: " . ($result['message'] ?? 'No message') . "\n";
    } else {
        echo "✗ Product creation failed!\n";
        echo "  Errors: " . print_r($result['errors'], true) . "\n";
        echo "  Message: " . ($result['message'] ?? 'No message') . "\n";
    }
    
} catch (Exception $e) {
    echo "✗ Exception occurred: " . $e->getMessage() . "\n";
    echo "  File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

// Cleanup
unlink($testImagePath);

echo "\n";

// Test 2: Test file naming strategies
echo "Test 2: Testing file naming strategies...\n";

$uploadLocations = [
    [
        'disk' => 'test',
        'absolute' => sys_get_temp_dir() . '/rcf-demo-test',
        'public' => 'test'
    ]
];

// Create test directory
if (!is_dir($uploadLocations[0]['absolute'])) {
    mkdir($uploadLocations[0]['absolute'], 0755, true);
}

try {
    $fileUploadService = new FileUploadService($uploadLocations);
    
    $strategies = [
        'Timestamp' => FileUploadService::STRATEGY_TIMESTAMP,
        'UUID' => FileUploadService::STRATEGY_UUID,
        'Hash' => FileUploadService::STRATEGY_HASH,
        'Hybrid' => FileUploadService::STRATEGY_HYBRID,
    ];
    
    foreach ($strategies as $name => $strategy) {
        // Create a test file
        $testFile = tempnam(sys_get_temp_dir(), 'demo_test_');
        file_put_contents($testFile, "Test content for {$name} strategy");
        
        $result = $fileUploadService->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'test.jpg'],
            $strategy,
            'demo-test'
        );
        
        if ($result['success']) {
            echo "  ✓ {$name} Strategy: " . $result['filename'] . "\n";
        } else {
            echo "  ✗ {$name} Strategy failed: " . ($result['error'] ?? 'Unknown error') . "\n";
        }
        
        unlink($testFile);
    }
    
} catch (Exception $e) {
    echo "✗ File upload service test failed: " . $e->getMessage() . "\n";
}

// Cleanup test directory
$testFiles = glob($uploadLocations[0]['absolute'] . '/*');
foreach ($testFiles as $file) {
    if (is_file($file)) {
        unlink($file);
    }
}
rmdir($uploadLocations[0]['absolute']);

echo "\n";

// Test 3: Test collision handling
echo "Test 3: Testing collision handling...\n";

$collisionTestDir = sys_get_temp_dir() . '/rcf-collision-test';
if (!is_dir($collisionTestDir)) {
    mkdir($collisionTestDir, 0755, true);
}

$collisionLocations = [
    [
        'disk' => 'collision',
        'absolute' => $collisionTestDir,
        'public' => 'collision'
    ]
];

try {
    $collisionService = new FileUploadService($collisionLocations);
    
    $filenames = [];
    for ($i = 0; $i < 5; $i++) {
        $testFile = tempnam(sys_get_temp_dir(), 'collision_test_');
        file_put_contents($testFile, "Collision test content {$i}");
        
        $result = $collisionService->uploadFile(
            ['tmp_name' => $testFile, 'name' => 'collision.jpg'],
            FileUploadService::STRATEGY_TIMESTAMP,
            'collision-test'
        );
        
        if ($result['success']) {
            $filenames[] = $result['filename'];
        }
        
        unlink($testFile);
        
        // Small delay to ensure different timestamps
        usleep(10000); // 10ms
    }
    
    $uniqueFilenames = array_unique($filenames);
    if (count($filenames) === count($uniqueFilenames)) {
        echo "  ✓ Collision handling successful: " . count($filenames) . " unique filenames generated\n";
        foreach ($filenames as $i => $filename) {
            echo "    " . ($i + 1) . ". {$filename}\n";
        }
    } else {
        echo "  ✗ Collision detected: " . (count($filenames) - count($uniqueFilenames)) . " duplicates\n";
    }
    
} catch (Exception $e) {
    echo "✗ Collision test failed: " . $e->getMessage() . "\n";
}

// Cleanup collision test
$collisionFiles = glob($collisionTestDir . '/*');
foreach ($collisionFiles as $file) {
    if (is_file($file)) {
        unlink($file);
    }
}
rmdir($collisionTestDir);

echo "\n====================================\n";
echo "Demo completed!\n";
echo "\nTo test the web interface:\n";
echo "1. Open admin/add-product.php in your browser\n";
echo "2. Fill out the form and upload an image\n";
echo "3. Check that the product is created with a unique filename\n";
echo "\nFile naming patterns to expect:\n";
echo "- Timestamp: product-image-YYYYMMDD-HHMMSS-microseconds.ext\n";
echo "- UUID: product-image-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx.ext\n";
echo "- Hash: product-image-xxxxxxxxxxxxxxxx.ext\n";
echo "- Hybrid: product-image-YYYYMMDD-HHMMSS-xxxxxx.ext\n";
